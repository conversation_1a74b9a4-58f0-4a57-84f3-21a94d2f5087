//
// MessagePack for C++ deserializing routine
//
// Copyright (C) 2017 <PERSON><PERSON><PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_PARSE_RETURN_HPP
#define MSGPACK_PARSE_RETURN_HPP

#include "msgpack/v1/parse_return.hpp"
#include "msgpack/v2/parse_return.hpp"
#include "msgpack/v3/parse_return.hpp"

#endif // MSGPACK_PARSE_RETURN_HPP
