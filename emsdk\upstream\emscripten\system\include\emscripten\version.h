/*
 * If anyone ever ends up including this file it would be an error
 * since this directory should never be in the include path.
 * The real version of this file gets auto-generated inside the
 * `sysroot/include` directory (which should be in the include path
 * instead).
 */
#error "Including files directly from the emscripten source tree is not supported.  Please use the cache/sysroot/include directory".
