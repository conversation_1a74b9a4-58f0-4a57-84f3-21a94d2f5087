<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_norm.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li><li class="navelem"><a class="el" href="dir_01de8928c960cafb028e5f164701e1de.html">reference</a></li><li class="navelem"><a class="el" href="dir_b790a865367d69962c5919afdba4a959.html">host</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_norm.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__norm_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="complex_8h.html">cutlass/complex.h</a>&quot;</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__ref_8h.html">cutlass/tensor_ref.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="host_2tensor__foreach_8h.html">cutlass/util/reference/host/tensor_foreach.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a>  {</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">namespace </span>reference {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">namespace </span>host {</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;    <span class="keyword">typename</span> Layout,</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;    <span class="keyword">typename</span> ElementReduction</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;&gt;</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#a0995c2093dcb952de0a8b9fb199352a1">   47</a></span>&#160;    ElementReduction <a class="code" href="namespacecutlass_1_1reference_1_1host.html#a0995c2093dcb952de0a8b9fb199352a1">TensorNorm</a>(</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a> view,</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        ElementReduction accumulator) {</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    <a class="code" href="namespacecutlass_1_1reference_1_1host.html#a3825b1aaaf5e5abf0de5f427e3481ada">TensorForEachLambda</a>(</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        view.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>(),</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        [&amp;](<span class="keyword">typename</span> Layout::TensorCoord <span class="keyword">const</span> &amp; coord) {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        Element element = Element(view.<a class="code" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">at</a>(coord));</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        accumulator = <a class="code" href="namespacecutlass.html#a326d3123e33cfd02c4e7ad519c56561f">cutlass::norm_accumulate</a>(element, accumulator);</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    });</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="namespacecutlass.html#a28f05d94dbdfc97cddbeab3a5d23839d">std::sqrt</a>(accumulator);</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;}</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> Element, </div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">typename</span> Layout</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;&gt;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#a8e1741fda1d41ab51a960e5f6fe5b59e">   65</a></span>&#160;<span class="keywordtype">double</span> <a class="code" href="namespacecutlass_1_1reference_1_1host.html#a0995c2093dcb952de0a8b9fb199352a1">TensorNorm</a>(<a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a> view) {</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="keywordflow">return</span> TensorNorm&lt;Element, Layout, double&gt;(view, 0);</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;}</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;} <span class="comment">// namespace host</span></div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;} <span class="comment">// namespace reference</span></div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1host_html_a0995c2093dcb952de0a8b9fb199352a1"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1host.html#a0995c2093dcb952de0a8b9fb199352a1">cutlass::reference::host::TensorNorm</a></div><div class="ttdeci">ElementReduction TensorNorm(TensorView&lt; Element, Layout &gt; view, ElementReduction accumulator)</div><div class="ttdoc">Computes the p=2 norm of the elements of a tensor with arbitrary reduction data type. </div><div class="ttdef"><b>Definition:</b> tensor_norm.h:47</div></div>
<div class="ttc" id="tensor__ref_8h_html"><div class="ttname"><a href="tensor__ref_8h.html">tensor_ref.h</a></div><div class="ttdoc">Defines a structure containing strides, bounds, and a pointer to tensor data. </div></div>
<div class="ttc" id="complex_8h_html"><div class="ttname"><a href="complex_8h.html">complex.h</a></div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a7d3914dd5042c9c40be9e21a7b4e9ece"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">cutlass::TensorView::extent</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord const &amp; extent() const </div><div class="ttdoc">Returns the extent of the view (the size along each logical dimension). </div><div class="ttdef"><b>Definition:</b> tensor_view.h:167</div></div>
<div class="ttc" id="namespacecutlass_html_a326d3123e33cfd02c4e7ad519c56561f"><div class="ttname"><a href="namespacecutlass.html#a326d3123e33cfd02c4e7ad519c56561f">cutlass::norm_accumulate</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE R norm_accumulate(T const &amp;x, R const &amp;accumulator)</div><div class="ttdoc">Norm-accumulate calculation. </div><div class="ttdef"><b>Definition:</b> complex.h:343</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html"><div class="ttname"><a href="classcutlass_1_1TensorView.html">cutlass::TensorView&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1host_html_a3825b1aaaf5e5abf0de5f427e3481ada"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1host.html#a3825b1aaaf5e5abf0de5f427e3481ada">cutlass::reference::host::TensorForEachLambda</a></div><div class="ttdeci">void TensorForEachLambda(Coord&lt; Rank &gt; extent, Func func)</div><div class="ttdoc">Iterates over the index space of a tensor and calls a C++ lambda. </div><div class="ttdef"><b>Definition:</b> host/tensor_foreach.h:98</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a8758907a1c9b1fcd00e7ece626d03b76"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">cutlass::TensorRef::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference at(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns a reference to the element at a given Coord. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:307</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="namespacecutlass_html_a28f05d94dbdfc97cddbeab3a5d23839d"><div class="ttname"><a href="namespacecutlass.html#a28f05d94dbdfc97cddbeab3a5d23839d">cutlass::sqrt</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE complex&lt; T &gt; sqrt(complex&lt; T &gt; const &amp;z)</div><div class="ttdoc">Computes the square root of complex number z. </div><div class="ttdef"><b>Definition:</b> complex.h:393</div></div>
<div class="ttc" id="host_2tensor__foreach_8h_html"><div class="ttname"><a href="host_2tensor__foreach_8h.html">tensor_foreach.h</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
