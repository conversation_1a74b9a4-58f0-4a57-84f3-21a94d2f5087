//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2016 <PERSON><PERSON><PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_TYPE_VECTOR_CHAR_HPP
#define MSGPACK_TYPE_VECTOR_CHAR_HPP

#include "msgpack/v1/adaptor/vector_char.hpp"

#endif // MSGPACK_TYPE_VECTOR_CHAR_HPP
