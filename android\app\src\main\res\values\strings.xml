<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">MLC-LLM Demo</string>
    <string name="action_about">About</string>
    <string name="action_refresh">Refresh</string>
    <string name="action_settings">Settings</string>
    
    <!-- Main Activity -->
    <string name="init_engine">Initialize MLC Engine</string>
    <string name="start_chat">Start Chat</string>
    <string name="settings">Settings</string>
    <string name="engine_status">Engine Status</string>
    <string name="system_info">System Information</string>
    
    <!-- Chat Activity -->
    <string name="chat_title">MLC-LLM Chat</string>
    <string name="message_hint">Type your message here...</string>
    <string name="send_message">Send Message</string>
    <string name="clear_chat">Clear</string>
    
    <!-- Settings Activity -->
    <string name="settings_title">Settings</string>
    <string name="model_settings">Model Settings</string>
    <string name="performance_settings">Performance Settings</string>
    <string name="about_settings">About</string>
    
    <!-- Messages -->
    <string name="engine_initializing">Initializing MLC Engine...</string>
    <string name="engine_ready">Engine Ready</string>
    <string name="engine_failed">Engine Initialization Failed</string>
    <string name="model_loading">Loading Model...</string>
    <string name="model_loaded">Model Loaded Successfully</string>
    <string name="generating_response">Generating Response...</string>
    
    <!-- About -->
    <string name="about_title">About MLC-LLM Demo</string>
    <string name="about_message">MLC-LLM Android Demo Application\n\nVersion: 1.0.0\nBuild: 2025-06-22\n\nThis app demonstrates running Large Language Models on Android devices using MLC-LLM technology.</string>
    
    <!-- Error Messages -->
    <string name="error_engine_init">Failed to initialize MLC Engine</string>
    <string name="error_model_load">Failed to load model</string>
    <string name="error_generation">Error generating response</string>
    <string name="error_empty_message">Please enter a message</string>
</resources>
