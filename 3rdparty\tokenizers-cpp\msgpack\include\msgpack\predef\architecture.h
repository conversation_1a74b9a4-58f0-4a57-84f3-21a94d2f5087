/*
Copyright Rene Rivera 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(MSGPACK_PREDEF_ARCHITECTURE_H) || defined(MSGPACK_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef MSGPACK_PREDEF_ARCHITECTURE_H
#define MSGPACK_PREDEF_ARCHITECTURE_H
#endif

#include <msgpack/predef/architecture/alpha.h>
#include <msgpack/predef/architecture/arm.h>
#include <msgpack/predef/architecture/blackfin.h>
#include <msgpack/predef/architecture/convex.h>
#include <msgpack/predef/architecture/ia64.h>
#include <msgpack/predef/architecture/m68k.h>
#include <msgpack/predef/architecture/mips.h>
#include <msgpack/predef/architecture/parisc.h>
#include <msgpack/predef/architecture/ppc.h>
#include <msgpack/predef/architecture/ptx.h>
#include <msgpack/predef/architecture/pyramid.h>
#include <msgpack/predef/architecture/rs6k.h>
#include <msgpack/predef/architecture/sparc.h>
#include <msgpack/predef/architecture/superh.h>
#include <msgpack/predef/architecture/sys370.h>
#include <msgpack/predef/architecture/sys390.h>
#include <msgpack/predef/architecture/x86.h>
#include <msgpack/predef/architecture/z.h>
/*#include <msgpack/predef/architecture/.h>*/

#endif
