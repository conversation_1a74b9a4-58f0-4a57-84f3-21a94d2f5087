//
// MessagePack for C++ zero-copy buffer implementation
//
// Copyright (C) 2008-2016 FURUHAS<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_VREFBUFFER_HPP
#define MSGPACK_VREFBUFFER_HPP

#include "msgpack/vrefbuffer_decl.hpp"

#include "msgpack/v1/vrefbuffer.hpp"

#endif // MSGPACK_VREFBUFFER_HPP
