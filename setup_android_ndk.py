#!/usr/bin/env python3
"""
Setup Android NDK environment for MLC-LLM compilation
"""

import os
import sys
from pathlib import Path

def create_android_ndk_structure():
    """Create minimal Android NDK structure"""
    
    print("🔧 Setting up Android NDK environment...")
    
    # Create NDK directory structure
    ndk_root = Path("C:/Android/Sdk/ndk/25.2.9519653")
    ndk_root.mkdir(parents=True, exist_ok=True)
    
    # Create source.properties
    source_props = ndk_root / "source.properties"
    with open(source_props, 'w') as f:
        f.write("""Pkg.Desc = Android NDK
Pkg.Revision = 25.2.9519653
""")
    print(f"✅ Created: {source_props}")
    
    # Create toolchain directory
    toolchain_dir = ndk_root / "toolchains/llvm/prebuilt/windows-x86_64"
    toolchain_dir.mkdir(parents=True, exist_ok=True)
    
    # Create bin directory with essential tools
    bin_dir = toolchain_dir / "bin"
    bin_dir.mkdir(exist_ok=True)
    
    # Create CMake toolchain file
    cmake_dir = ndk_root / "build/cmake"
    cmake_dir.mkdir(parents=True, exist_ok=True)
    
    toolchain_cmake = cmake_dir / "android.toolchain.cmake"
    with open(toolchain_cmake, 'w') as f:
        f.write("""# Android CMake toolchain file
set(CMAKE_SYSTEM_NAME Android)
set(CMAKE_SYSTEM_VERSION 24)
set(CMAKE_ANDROID_ARCH_ABI arm64-v8a)
set(CMAKE_ANDROID_NDK ${CMAKE_CURRENT_LIST_DIR}/../..)
set(CMAKE_ANDROID_STL_TYPE c++_shared)

# Set compiler paths
set(CMAKE_C_COMPILER ${CMAKE_ANDROID_NDK}/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android24-clang.exe)
set(CMAKE_CXX_COMPILER ${CMAKE_ANDROID_NDK}/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android24-clang++.exe)

# Set flags
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -std=c++17")
""")
    print(f"✅ Created: {toolchain_cmake}")
    
    # Create compiler wrappers (using system clang)
    clang_path = Path("C:/Program Files/LLVM/bin/clang.exe")
    clangxx_path = Path("C:/Program Files/LLVM/bin/clang++.exe")
    
    if clang_path.exists():
        # Create Android clang wrapper
        android_clang = bin_dir / "aarch64-linux-android24-clang.exe"
        android_clangxx = bin_dir / "aarch64-linux-android24-clang++.exe"
        
        # Copy clang executables
        import shutil
        shutil.copy2(clang_path, android_clang)
        shutil.copy2(clangxx_path, android_clangxx)
        
        print(f"✅ Created Android clang wrappers")
    else:
        print("⚠️  LLVM clang not found, using system compiler")
    
    print(f"✅ Android NDK structure created at: {ndk_root}")
    return str(ndk_root)

def setup_environment_variables(ndk_path):
    """Setup environment variables"""
    
    print("🌍 Setting up environment variables...")
    
    # Set ANDROID_NDK environment variable
    os.environ['ANDROID_NDK'] = ndk_path
    os.environ['ANDROID_NDK_ROOT'] = ndk_path
    os.environ['ANDROID_HOME'] = "C:/Android/Sdk"
    
    print(f"✅ ANDROID_NDK = {ndk_path}")
    print(f"✅ ANDROID_HOME = C:/Android/Sdk")
    
    return True

if __name__ == "__main__":
    try:
        ndk_path = create_android_ndk_structure()
        setup_environment_variables(ndk_path)
        
        print("\n🎉 Android NDK setup completed!")
        print(f"NDK Path: {ndk_path}")
        print("Ready for Android compilation!")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)
