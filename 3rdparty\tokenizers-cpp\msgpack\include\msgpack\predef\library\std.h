/*
Copyright Rene Rivera 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/
#if !defined(MSGPACK_PREDEF_LIBRARY_STD_H) || defined(MSGPACK_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef MSGPACK_PREDEF_LIBRARY_STD_H
#define MSGPACK_PREDEF_LIBRARY_STD_H
#endif

#include <msgpack/predef/library/std/_prefix.h>

#include <msgpack/predef/library/std/cxx.h>
#include <msgpack/predef/library/std/dinkumware.h>
#include <msgpack/predef/library/std/libcomo.h>
#include <msgpack/predef/library/std/modena.h>
#include <msgpack/predef/library/std/msl.h>
#include <msgpack/predef/library/std/roguewave.h>
#include <msgpack/predef/library/std/sgi.h>
#include <msgpack/predef/library/std/stdcpp3.h>
#include <msgpack/predef/library/std/stlport.h>
#include <msgpack/predef/library/std/vacpp.h>

#endif
