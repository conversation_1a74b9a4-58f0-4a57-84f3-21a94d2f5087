<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::IdentityTensorLayout&lt; Rank &gt; Class Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html">IdentityTensorLayout</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classcutlass_1_1IdentityTensorLayout-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::IdentityTensorLayout&lt; Rank &gt; Class Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tensor__ref_8h_source.html">tensor_ref.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:ae64fd86033500257785f6923da2558c0"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">Index</a> = int32_t</td></tr>
<tr class="memdesc:ae64fd86033500257785f6923da2558c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Index type used for coordinates.  <a href="#ae64fd86033500257785f6923da2558c0">More...</a><br /></td></tr>
<tr class="separator:ae64fd86033500257785f6923da2558c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aea0b83b611144c3f5860712967234ab4"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a> = int64_t</td></tr>
<tr class="memdesc:aea0b83b611144c3f5860712967234ab4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Long index type used for offsets.  <a href="#aea0b83b611144c3f5860712967234ab4">More...</a><br /></td></tr>
<tr class="separator:aea0b83b611144c3f5860712967234ab4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e2af5a4dea388d9de74bf1c98c610ed"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a2e2af5a4dea388d9de74bf1c98c610ed">TensorCoord</a> = <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a813d116ff0e45679a2a7960a7c10fd1b">kRank</a>, <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">Index</a> &gt;</td></tr>
<tr class="memdesc:a2e2af5a4dea388d9de74bf1c98c610ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Logical coordinate.  <a href="#a2e2af5a4dea388d9de74bf1c98c610ed">More...</a><br /></td></tr>
<tr class="separator:a2e2af5a4dea388d9de74bf1c98c610ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e1b58137ca0996e3fa0f727a1d85761"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a> = <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965">kStrideRank</a>, <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">Index</a> &gt;</td></tr>
<tr class="memdesc:a5e1b58137ca0996e3fa0f727a1d85761"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stride vector.  <a href="#a5e1b58137ca0996e3fa0f727a1d85761">More...</a><br /></td></tr>
<tr class="separator:a5e1b58137ca0996e3fa0f727a1d85761"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a2aff6124a25853f227cdd7f3b36733c4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a2aff6124a25853f227cdd7f3b36733c4">IdentityTensorLayout</a> (<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a> const &amp;<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#adcc4153dde7e6dbd35afd30a28eb9596">stride</a>=<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a>())</td></tr>
<tr class="separator:a2aff6124a25853f227cdd7f3b36733c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a471e797514da29a5cad6c61fffe9eb5c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a471e797514da29a5cad6c61fffe9eb5c">operator()</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank &gt; const &amp;coord) const </td></tr>
<tr class="memdesc:a471e797514da29a5cad6c61fffe9eb5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the offset of a coordinate in linear memory.  <a href="#a471e797514da29a5cad6c61fffe9eb5c">More...</a><br /></td></tr>
<tr class="separator:a471e797514da29a5cad6c61fffe9eb5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcc4153dde7e6dbd35afd30a28eb9596"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#adcc4153dde7e6dbd35afd30a28eb9596">stride</a> () const </td></tr>
<tr class="memdesc:adcc4153dde7e6dbd35afd30a28eb9596"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the stride of the layout.  <a href="#adcc4153dde7e6dbd35afd30a28eb9596">More...</a><br /></td></tr>
<tr class="separator:adcc4153dde7e6dbd35afd30a28eb9596"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a041b285984b1940d70a2b7768416e996"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a041b285984b1940d70a2b7768416e996">stride</a> ()</td></tr>
<tr class="memdesc:a041b285984b1940d70a2b7768416e996"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the stride of the layout.  <a href="#a041b285984b1940d70a2b7768416e996">More...</a><br /></td></tr>
<tr class="separator:a041b285984b1940d70a2b7768416e996"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3dc530520b5eb35bc57c75de7954b59f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a3dc530520b5eb35bc57c75de7954b59f">capacity</a> (<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a2e2af5a4dea388d9de74bf1c98c610ed">TensorCoord</a> const &amp;size) const </td></tr>
<tr class="memdesc:a3dc530520b5eb35bc57c75de7954b59f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the number of contiguous elements needed to store a tensor with the given size.  <a href="#a3dc530520b5eb35bc57c75de7954b59f">More...</a><br /></td></tr>
<tr class="separator:a3dc530520b5eb35bc57c75de7954b59f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a813d116ff0e45679a2a7960a7c10fd1b"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a813d116ff0e45679a2a7960a7c10fd1b">kRank</a> = Rank</td></tr>
<tr class="memdesc:a813d116ff0e45679a2a7960a7c10fd1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Logical rank of tensor.  <a href="#a813d116ff0e45679a2a7960a7c10fd1b">More...</a><br /></td></tr>
<tr class="separator:a813d116ff0e45679a2a7960a7c10fd1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0ed9dc11a6284d25ea588d6933d2965"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965">kStrideRank</a> = Rank</td></tr>
<tr class="memdesc:ad0ed9dc11a6284d25ea588d6933d2965"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rank of stride vector.  <a href="#ad0ed9dc11a6284d25ea588d6933d2965">More...</a><br /></td></tr>
<tr class="separator:ad0ed9dc11a6284d25ea588d6933d2965"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;int Rank&gt;<br />
class cutlass::IdentityTensorLayout&lt; Rank &gt;</h3>

<p>Default layout function from coordinates in a tensor's index space into the n-D array held in memory.</p>
<p>All layout functions must define at least the members shown in IdentityTensorLayout&lt;&gt;. </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ae64fd86033500257785f6923da2558c0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">Index</a> =  int32_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aea0b83b611144c3f5860712967234ab4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a> =  int64_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5e1b58137ca0996e3fa0f727a1d85761"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a> =  <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965">kStrideRank</a>, <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">Index</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2e2af5a4dea388d9de74bf1c98c610ed"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a2e2af5a4dea388d9de74bf1c98c610ed">TensorCoord</a> =  <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;<a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a813d116ff0e45679a2a7960a7c10fd1b">kRank</a>, <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">Index</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a2aff6124a25853f227cdd7f3b36733c4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::<a class="el" href="classcutlass_1_1IdentityTensorLayout.html">IdentityTensorLayout</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a> const &amp;&#160;</td>
          <td class="paramname"><em>stride</em> = <code><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a>()</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a3dc530520b5eb35bc57c75de7954b59f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::capacity </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a2e2af5a4dea388d9de74bf1c98c610ed">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>size</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a471e797514da29a5cad6c61fffe9eb5c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank &gt; const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adcc4153dde7e6dbd35afd30a28eb9596"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::stride </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a041b285984b1940d70a2b7768416e996"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a>&amp; <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::stride </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a813d116ff0e45679a2a7960a7c10fd1b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::kRank = Rank</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad0ed9dc11a6284d25ea588d6933d2965"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a>&lt; Rank &gt;::kStrideRank = Rank</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tensor__ref_8h_source.html">tensor_ref.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
