project Zlib is

   for Languages use ("Ada");
   for Source_Dirs use (".");
   for Object_Dir use ".";
   for Main use ("test.adb", "mtest.adb", "read.adb", "buffer_demo");

   package Compiler is
      for Default_Switches ("ada") use ("-gnatwcfilopru", "-gnatVcdfimorst", "-gnatyabcefhiklmnoprst");
   end Compiler;

   package Linker is
      for Default_Switches ("ada") use ("-lz");
   end Linker;

   package Builder is
      for Default_Switches ("ada") use ("-s", "-gnatQ");
   end Builder;

end Zlib;
