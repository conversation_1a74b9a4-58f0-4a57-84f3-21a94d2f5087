<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_2296cf082f2778f9a3503c8ea1010763.html">layout</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;assert.h&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="fast__math_8h.html">cutlass/fast_math.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__coord_8h.html">cutlass/tensor_coord.h</a>&quot;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">namespace </span>layout {</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="comment">// Defines data layouts of various tensor formats usable by TensorRef and other classes.</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html">   53</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html">TensorNHWC</a> {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#afa85abf66e9f96d675a038307f8896bc">   56</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#afa85abf66e9f96d675a038307f8896bc">kRank</a> = 4;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#aa03e4031f1ea30e534df11ffdc0cf59c">   59</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#aa03e4031f1ea30e534df11ffdc0cf59c">kStrideRank</a> = 3;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#ac261fc9c833126bb2afa5c70feacf2a3">   62</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#ac261fc9c833126bb2afa5c70feacf2a3">Index</a> = int32_t;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">   65</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a> = int64_t;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#a89257c24195c4ebe72466ed6be4ea898">   68</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#ada7dfba98be04cc7b99cb53195c1a416">   71</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank&gt;</a>;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4214c37382cd4e479748dae3f5e3bc52">   88</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4214c37382cd4e479748dae3f5e3bc52">TensorNHWC</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a> = <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#ada7dfba98be04cc7b99cb53195c1a416">Stride</a>(0)): stride_(<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a>) { }</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#a0f03db22381cc9a08699667452e42e1e">   92</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a0f03db22381cc9a08699667452e42e1e">TensorNHWC</a>(<span class="keyword">typename</span> <a class="code" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Stride::Index</a> c, <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Stride::Index</a> wc, <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Stride::Index</a> hwc): stride_(<a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(c, wc, hwc)) { }</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#ab38ea68b38cf635ea661360967edb8d2">   96</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html">TensorNHWC</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#ab38ea68b38cf635ea661360967edb8d2">packed</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4214c37382cd4e479748dae3f5e3bc52">TensorNHWC</a>(</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;      <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>(), </div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>(),</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>()</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;      )</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    );</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  }</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  </div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#a681b2a1c1a1df6b68b6fb68a18a8392a">  108</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a681b2a1c1a1df6b68b6fb68a18a8392a">operator()</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    <span class="keywordflow">return</span> coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>() + </div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[0] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>()) + </div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[1] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>()) +</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[2] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>());</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  }</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#ae700570f695693a3f5e344366cf1ae65">  117</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">RowMajor</a>() {</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">RowMajor</a>(stride_[0]);</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  }</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00123"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#ac172f5707f9eb601046c8fde392a2bf6">  123</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#ac172f5707f9eb601046c8fde392a2bf6">inverse</a>(<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a> index)<span class="keyword"> const </span>{</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="keywordtype">int</span> n = 0, h = 0, w = 0, c = 0;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__)</span></div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <span class="keywordtype">int</span> tmp = 0;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    c = int(index % static_cast&lt;int&gt;(stride_[0]));</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> hw_mul, hw_shr, w_mul, w_shr, c_mul, c_shr;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    <a class="code" href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">find_divisor</a>(hw_mul, hw_shr, stride_[2]);</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    <a class="code" href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">find_divisor</a>(w_mul, w_shr, stride_[1]);</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    <a class="code" href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">find_divisor</a>(c_mul, c_shr, stride_[0]);</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;    <a class="code" href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">fast_divmod</a>(n, tmp, index, <span class="keywordtype">int</span>(stride_[2]), hw_mul, hw_shr);</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <a class="code" href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">fast_divmod</a>(h, w, tmp, <span class="keywordtype">int</span>(stride_[1]), w_mul, w_shr);</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <a class="code" href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">fast_divmod</a>(w, tmp, w, <span class="keywordtype">int</span>(stride_[0]), c_mul, c_shr);</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    n = int(index / (stride_[0] * stride_[1] * stride_[2]));</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a> residual = index % (stride_[0] * stride_[1] * stride_[2]);</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;    h = int(residual / (stride_[0] * stride_[1]));</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    residual = (residual % (stride_[0] * stride_[1]));</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    w = int(residual / stride_[0]);</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    c = int(residual % stride_[0]);</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a89257c24195c4ebe72466ed6be4ea898">TensorCoord</a>(n, h, w, c);</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  }</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00157"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">  157</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;  }</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#a82ef6fe64c60f808f2844fcb15834a4c">  163</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a82ef6fe64c60f808f2844fcb15834a4c">stride</a>() {</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  }</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNHWC.html#a3bb3250d891e752789fa02d5c0cc0ede">  169</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a3bb3250d891e752789fa02d5c0cc0ede">capacity</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    <span class="comment">// it does not make sense if the extent is larger than stride</span></div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    <span class="comment">// and we could not rely on the capacity calculation in such cases</span></div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    <span class="comment">// we could move this checkers to debug code only</span></div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    <span class="keywordflow">if</span> ((extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>() &gt; stride_[0])</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        || (extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * stride_[0] &gt; stride_[1]) </div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        || (extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>() * stride_[1] &gt; stride_[2])) {</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;      assert(0);</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    }</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="keywordflow">return</span> extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>() * stride_[2];</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  }</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;};</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html">  186</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html">TensorNCHW</a> {</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00189"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a1cf0ae642ae746413398d4996abfba1f">  189</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 4;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a7ee93120511aa8735105c4417739e815">  192</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 3;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div><div class="line"><a name="l00195"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a3d14ee878cfbd3b46a2aef671f9cdfb3">  195</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#a3d14ee878cfbd3b46a2aef671f9cdfb3">Index</a> = int32_t;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00198"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a9f0b098e30b11bc2f5aaef336c8ed204">  198</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#a9f0b098e30b11bc2f5aaef336c8ed204">LongIndex</a> = int64_t;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div><div class="line"><a name="l00201"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#ab611c66238ef1cefade8a17cefae892d">  201</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a09ead218d25432e2f70666b5775cfed1">  204</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank&gt;</a>;</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00221"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#aa0b2d1bcdf7697cf07d26b91aba7f5d8">  221</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#aa0b2d1bcdf7697cf07d26b91aba7f5d8">TensorNCHW</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a> = <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#ada7dfba98be04cc7b99cb53195c1a416">Stride</a>(0)): stride_(<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a>) { }</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00225"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a3096dbd7c7243eaa54c139d137b7e2b5">  225</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html">TensorNCHW</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#a3096dbd7c7243eaa54c139d137b7e2b5">packed</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html">TensorNCHW</a>(</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;      <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;        extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>(),</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;        extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>(),</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;        extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>()</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;      )</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;    );</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  }</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00237"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#adb289aec0ed0a3e5db329210ec55c9be">  237</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#a9f0b098e30b11bc2f5aaef336c8ed204">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#adb289aec0ed0a3e5db329210ec55c9be">operator()</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;    <span class="keywordflow">return</span> coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() + </div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[0] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>()) + </div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[1] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>()) + </div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[2] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>());</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;  }</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00246"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a71ed1de94210b6110c5017a10a52376d">  246</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#a71ed1de94210b6110c5017a10a52376d">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;  }</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00252"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#ae3256320d227b911be28f4cfad9f8280">  252</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#ae3256320d227b911be28f4cfad9f8280">stride</a>() {</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;  }</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00258"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCHW.html#a20dd56dacab1558db7253fb737704c51">  258</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#a9f0b098e30b11bc2f5aaef336c8ed204">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCHW.html#a20dd56dacab1558db7253fb737704c51">capacity</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;    <span class="keywordflow">return</span> extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>() * stride_[2];</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;  }</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;};</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Interleave&gt;</div><div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html">  267</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html">TensorNCxHWx</a> {</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div><div class="line"><a name="l00271"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#aa9d3b74f0ae63b54b4e3a55cdbb9edef">  271</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kInterleave = Interleave;</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div><div class="line"><a name="l00274"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a1c774534c9c250bcf81f3e32e7b00edb">  274</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 4;</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;</div><div class="line"><a name="l00277"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#aa17170d312069cb54025c207b318e76a">  277</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 3;</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;</div><div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a57a1e3a60ba44629bbdabe43dc588899">  280</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a57a1e3a60ba44629bbdabe43dc588899">Index</a> = int32_t;</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div><div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a32f02e6a348caf051d2faab503fbc65c">  283</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a32f02e6a348caf051d2faab503fbc65c">LongIndex</a> = int64_t;</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;</div><div class="line"><a name="l00286"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a362a077d7a6c3e2f8354043cddcb4811">  286</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>;</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;</div><div class="line"><a name="l00289"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a48066db2a98a8b2cdf585b0b2c9ab887">  289</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank&gt;</a>;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00306"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a59e93fceb4a8c9a7dd7492a19e4c98df">  306</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a59e93fceb4a8c9a7dd7492a19e4c98df">TensorNCxHWx</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a> = <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#ada7dfba98be04cc7b99cb53195c1a416">Stride</a>(0)): stride_(<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a>) { }</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00310"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#af6a8593026ce08ec962f43db36c84496">  310</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html">TensorNCxHWx</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#af6a8593026ce08ec962f43db36c84496">packed</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html">TensorNCxHWx</a>(</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;      <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;        kInterleave * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>(),</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;        kInterleave * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>(),</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;        extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>()</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;      )</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;    );</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;  }</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00322"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#aee9b7569e5e31b15d35ca64cfa0bc0d6">  322</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a32f02e6a348caf051d2faab503fbc65c">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#aee9b7569e5e31b15d35ca64cfa0bc0d6">operator()</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;    <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a57a1e3a60ba44629bbdabe43dc588899">Index</a> c_minor = (coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>() % kInterleave);</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;    <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a57a1e3a60ba44629bbdabe43dc588899">Index</a> c_major = (coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>() / kInterleave);</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;    <span class="keywordflow">return</span> c_minor + </div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(kInterleave * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>()) + </div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[0] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>()) + </div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[1] * c_major) + </div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[2] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>());</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  }</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00336"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a7b136a1523b0ba92e8e54b7882696cf9">  336</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a7b136a1523b0ba92e8e54b7882696cf9">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;  }</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00342"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a18eccaeabb7badcd2fc5d7326d194730">  342</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a18eccaeabb7badcd2fc5d7326d194730">stride</a>() {</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;  }</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00348"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a915e1193d4f9c1feb973ae1331687bf9">  348</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a32f02e6a348caf051d2faab503fbc65c">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a915e1193d4f9c1feb973ae1331687bf9">capacity</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;    <span class="keywordflow">return</span> extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>() * stride_[2];</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;  }</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;};</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Interleave&gt;</div><div class="line"><a name="l00357"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html">  357</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html">TensorCxRSKx</a> {</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;</div><div class="line"><a name="l00361"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a325038d558d62bd3a30e14f5938b5ace">  361</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kInterleave = Interleave;</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;</div><div class="line"><a name="l00364"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#aafb84a4ad18832ea95e514bab7e1b884">  364</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = 4;</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;</div><div class="line"><a name="l00367"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#af93c5ebb2e713da1e75bded321e6bd0e">  367</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = 3;</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;</div><div class="line"><a name="l00370"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#aa7759f0e487cfa2d314951e3dec569e3">  370</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#aa7759f0e487cfa2d314951e3dec569e3">Index</a> = int32_t;</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;</div><div class="line"><a name="l00373"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a782c4ac33be5bb8a934897f2e7f588dd">  373</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a782c4ac33be5bb8a934897f2e7f588dd">LongIndex</a> = int64_t;</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;</div><div class="line"><a name="l00376"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a4839a52624e918aaf8a65c2253dd545c">  376</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>;</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;</div><div class="line"><a name="l00379"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a920f31bed38a2132747cfb56a50fa1e5">  379</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank&gt;</a>;</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00396"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a53cbde36dc454e12231492154c3c0e3e">  396</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a53cbde36dc454e12231492154c3c0e3e">TensorCxRSKx</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a> = <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#ada7dfba98be04cc7b99cb53195c1a416">Stride</a>(0)): stride_(<a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">stride</a>) { }</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00400"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a48194ca13de771a824c37d48bf3885d7">  400</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html">TensorCxRSKx</a> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a48194ca13de771a824c37d48bf3885d7">packed</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html">TensorCxRSKx</a>(</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;      <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(</div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;        kInterleave * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>(),</div><div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;        kInterleave * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>(),</div><div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;        kInterleave * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>() * extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>()</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;      )</div><div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;    );</div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;  }</div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;</div><div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00412"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a1f7f0c8accc79e69057fc4ac487142b4">  412</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a782c4ac33be5bb8a934897f2e7f588dd">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a1f7f0c8accc79e69057fc4ac487142b4">operator()</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;</div><div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;    <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#aa7759f0e487cfa2d314951e3dec569e3">Index</a> c_minor = (coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>() % kInterleave);</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;    <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#aa7759f0e487cfa2d314951e3dec569e3">Index</a> c_major = (coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>() / kInterleave);</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;</div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;    <span class="keywordflow">return</span> c_minor + </div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(kInterleave * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>()) + </div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[0] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>()) + </div><div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[1] * coord.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>()) + </div><div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;      <a class="code" href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">LongIndex</a>(stride_[2] * c_major);</div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;  }</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;</div><div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00426"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a80a74c41700027d19b7dd8e134506a3a">  426</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a80a74c41700027d19b7dd8e134506a3a">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;  }</div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div><div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00432"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#abe444796b2156c056d77f399c81616ac">  432</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#abe444796b2156c056d77f399c81616ac">stride</a>() {</div><div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;  }</div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;</div><div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00438"></a><span class="lineno"><a class="line" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a27c2569d09991401630d7842c0c1ba67">  438</a></span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a782c4ac33be5bb8a934897f2e7f588dd">LongIndex</a> <a class="code" href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a27c2569d09991401630d7842c0c1ba67">capacity</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;    <span class="keywordflow">return</span> (extent.<a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>() / kInterleave * stride_[2]);</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;  }</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;};</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;</div><div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;</div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;} <span class="comment">// namespace layout</span></div><div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_ada7dfba98be04cc7b99cb53195c1a416"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#ada7dfba98be04cc7b99cb53195c1a416">cutlass::layout::TensorNHWC::Stride</a></div><div class="ttdeci">Coord&lt; kStrideRank &gt; Stride</div><div class="ttdoc">Stride vector. </div><div class="ttdef"><b>Definition:</b> tensor.h:71</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_a71ed1de94210b6110c5017a10a52376d"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#a71ed1de94210b6110c5017a10a52376d">cutlass::layout::TensorNCHW::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:246</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html">cutlass::Tensor4DCoord</a></div><div class="ttdoc">Defines a canonical 4D coordinate used by tensor operations. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:38</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_a53cbde36dc454e12231492154c3c0e3e"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a53cbde36dc454e12231492154c3c0e3e">cutlass::layout::TensorCxRSKx::TensorCxRSKx</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCxRSKx(Stride const &amp;stride=Stride(0))</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> tensor.h:396</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="namespacecutlass_html_ab9726f5a6b39322cf13cd916257fd9a7"><div class="ttname"><a href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">cutlass::fast_divmod</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void fast_divmod(int &amp;quo, int &amp;rem, int src, int div, unsigned int mul, unsigned int shr)</div><div class="ttdef"><b>Definition:</b> fast_math.h:176</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_a915e1193d4f9c1feb973ae1331687bf9"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a915e1193d4f9c1feb973ae1331687bf9">cutlass::layout::TensorNCxHWx::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor.h:348</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_a59e93fceb4a8c9a7dd7492a19e4c98df"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a59e93fceb4a8c9a7dd7492a19e4c98df">cutlass::layout::TensorNCxHWx::TensorNCxHWx</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorNCxHWx(Stride const &amp;stride=Stride(0))</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> tensor.h:306</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_aa03e4031f1ea30e534df11ffdc0cf59c"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#aa03e4031f1ea30e534df11ffdc0cf59c">cutlass::layout::TensorNHWC::kStrideRank</a></div><div class="ttdeci">static int const kStrideRank</div><div class="ttdoc">Rank of stride vector. </div><div class="ttdef"><b>Definition:</b> tensor.h:59</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_af6a8593026ce08ec962f43db36c84496"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#af6a8593026ce08ec962f43db36c84496">cutlass::layout::TensorNCxHWx::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorNCxHWx packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor.h:310</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="namespacecutlass_html_a7419519fa453a121dfa5f26bf87318d9"><div class="ttname"><a href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">cutlass::make_Coord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord&lt; 1 &gt; make_Coord(int _0)</div><div class="ttdoc">Helper to make a 2-element coordinate. </div><div class="ttdef"><b>Definition:</b> coord.h:387</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_a1f7f0c8accc79e69057fc4ac487142b4"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a1f7f0c8accc79e69057fc4ac487142b4">cutlass::layout::TensorCxRSKx::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns the offset of a coordinate in linear memory. </div><div class="ttdef"><b>Definition:</b> tensor.h:412</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_a7b136a1523b0ba92e8e54b7882696cf9"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a7b136a1523b0ba92e8e54b7882696cf9">cutlass::layout::TensorNCxHWx::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:336</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_a3bb3250d891e752789fa02d5c0cc0ede"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#a3bb3250d891e752789fa02d5c0cc0ede">cutlass::layout::TensorNHWC::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor.h:169</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_ac261fc9c833126bb2afa5c70feacf2a3"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#ac261fc9c833126bb2afa5c70feacf2a3">cutlass::layout::TensorNHWC::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor.h:62</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_a89257c24195c4ebe72466ed6be4ea898"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#a89257c24195c4ebe72466ed6be4ea898">cutlass::layout::TensorNHWC::TensorCoord</a></div><div class="ttdeci">Tensor4DCoord TensorCoord</div><div class="ttdoc">Logical coordinate (n, h, w, c) </div><div class="ttdef"><b>Definition:</b> tensor.h:68</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html">cutlass::layout::TensorNCxHWx</a></div><div class="ttdoc">Mapping function for 4-D NC/xHWx tensors. </div><div class="ttdef"><b>Definition:</b> tensor.h:267</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_a9f0b098e30b11bc2f5aaef336c8ed204"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#a9f0b098e30b11bc2f5aaef336c8ed204">cutlass::layout::TensorNCHW::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor.h:198</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ae3136dc898c4ef079e73b51b1850ba7e"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">cutlass::Tensor4DCoord::w</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; w() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:95</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_a4214c37382cd4e479748dae3f5e3bc52"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#a4214c37382cd4e479748dae3f5e3bc52">cutlass::layout::TensorNHWC::TensorNHWC</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorNHWC(Stride const &amp;stride=Stride(0))</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> tensor.h:88</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_a7a89e5661ef391dd9f4fe81f0c982b75"><div class="ttname"><a href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">cutlass::Coord&lt; kStrideRank &gt;::Index</a></div><div class="ttdeci">int Index</div><div class="ttdoc">Index type used to store elements. </div><div class="ttdef"><b>Definition:</b> coord.h:55</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_a3096dbd7c7243eaa54c139d137b7e2b5"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#a3096dbd7c7243eaa54c139d137b7e2b5">cutlass::layout::TensorNCHW::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorNCHW packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor.h:225</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_a82ef6fe64c60f808f2844fcb15834a4c"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#a82ef6fe64c60f808f2844fcb15834a4c">cutlass::layout::TensorNHWC::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:163</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_aa0b2d1bcdf7697cf07d26b91aba7f5d8"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#aa0b2d1bcdf7697cf07d26b91aba7f5d8">cutlass::layout::TensorNCHW::TensorNCHW</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorNCHW(Stride const &amp;stride=Stride(0))</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> tensor.h:221</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_afa85abf66e9f96d675a038307f8896bc"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#afa85abf66e9f96d675a038307f8896bc">cutlass::layout::TensorNHWC::kRank</a></div><div class="ttdeci">static int const kRank</div><div class="ttdoc">Logical rank of tensor. </div><div class="ttdef"><b>Definition:</b> tensor.h:56</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_a18eccaeabb7badcd2fc5d7326d194730"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a18eccaeabb7badcd2fc5d7326d194730">cutlass::layout::TensorNCxHWx::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:342</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_a20dd56dacab1558db7253fb737704c51"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#a20dd56dacab1558db7253fb737704c51">cutlass::layout::TensorNCHW::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor.h:258</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ab0f58e5f54b42534fca77a662c78c7ad"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">cutlass::Tensor4DCoord::c</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; c() const </div><div class="ttdoc">Returns the channel of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:103</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_aa7759f0e487cfa2d314951e3dec569e3"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#aa7759f0e487cfa2d314951e3dec569e3">cutlass::layout::TensorCxRSKx::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor.h:370</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_a0f03db22381cc9a08699667452e42e1e"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#a0f03db22381cc9a08699667452e42e1e">cutlass::layout::TensorNHWC::TensorNHWC</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorNHWC(typename Stride::Index c, typename Stride::Index wc, typename Stride::Index hwc)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> tensor.h:92</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_abe444796b2156c056d77f399c81616ac"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#abe444796b2156c056d77f399c81616ac">cutlass::layout::TensorCxRSKx::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:432</div></div>
<div class="ttc" id="tensor__coord_8h_html"><div class="ttname"><a href="tensor__coord_8h.html">tensor_coord.h</a></div><div class="ttdoc">Defines a canonical coordinate for rank=4 tensors offering named indices. </div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_a80a74c41700027d19b7dd8e134506a3a"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a80a74c41700027d19b7dd8e134506a3a">cutlass::layout::TensorCxRSKx::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:426</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html">cutlass::layout::TensorCxRSKx</a></div><div class="ttdoc">Mapping function for 4-D CxRSKx tensors. </div><div class="ttdef"><b>Definition:</b> tensor.h:357</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_a4e66e7ef2905194af62c8f0a8d3be4c9"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#a4e66e7ef2905194af62c8f0a8d3be4c9">cutlass::layout::TensorNHWC::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor.h:65</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_ab38ea68b38cf635ea661360967edb8d2"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#ab38ea68b38cf635ea661360967edb8d2">cutlass::layout::TensorNHWC::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorNHWC packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed NHWC tensor. </div><div class="ttdef"><b>Definition:</b> tensor.h:96</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html">cutlass::layout::TensorNCHW</a></div><div class="ttdoc">Mapping function for 4-D NCHW tensors. </div><div class="ttdef"><b>Definition:</b> tensor.h:186</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_acff01037b5d6cd8040f644060cdac639"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#acff01037b5d6cd8040f644060cdac639">cutlass::layout::TensorNHWC::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:157</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_a27c2569d09991401630d7842c0c1ba67"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a27c2569d09991401630d7842c0c1ba67">cutlass::layout::TensorCxRSKx::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor.h:438</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_a48194ca13de771a824c37d48bf3885d7"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a48194ca13de771a824c37d48bf3885d7">cutlass::layout::TensorCxRSKx::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE TensorCxRSKx packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor.h:400</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_a3d14ee878cfbd3b46a2aef671f9cdfb3"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#a3d14ee878cfbd3b46a2aef671f9cdfb3">cutlass::layout::TensorNCHW::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor.h:195</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_adb289aec0ed0a3e5db329210ec55c9be"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#adb289aec0ed0a3e5db329210ec55c9be">cutlass::layout::TensorNCHW::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns the offset of a coordinate in linear memory. </div><div class="ttdef"><b>Definition:</b> tensor.h:237</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord&lt; kStrideRank &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a2fa718218c21df006b71d9325f1ddb5a"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">cutlass::Tensor4DCoord::n</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; n() const </div><div class="ttdoc">Returns the batch of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:79</div></div>
<div class="ttc" id="namespacecutlass_html_aac63a770acddafd828619834cf2c99d3"><div class="ttname"><a href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">cutlass::find_divisor</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void find_divisor(unsigned int &amp;mul, unsigned int &amp;shr, unsigned int denom)</div><div class="ttdef"><b>Definition:</b> fast_math.h:159</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorCxRSKx_html_a782c4ac33be5bb8a934897f2e7f588dd"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorCxRSKx.html#a782c4ac33be5bb8a934897f2e7f588dd">cutlass::layout::TensorCxRSKx::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor.h:373</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCHW_html_ae3256320d227b911be28f4cfad9f8280"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCHW.html#ae3256320d227b911be28f4cfad9f8280">cutlass::layout::TensorNCHW::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor.h:252</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_aee9b7569e5e31b15d35ca64cfa0bc0d6"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#aee9b7569e5e31b15d35ca64cfa0bc0d6">cutlass::layout::TensorNCxHWx::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns the offset of a coordinate in linear memory. </div><div class="ttdef"><b>Definition:</b> tensor.h:322</div></div>
<div class="ttc" id="fast__math_8h_html"><div class="ttname"><a href="fast__math_8h.html">fast_math.h</a></div><div class="ttdoc">Math utilities. </div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_a32f02e6a348caf051d2faab503fbc65c"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a32f02e6a348caf051d2faab503fbc65c">cutlass::layout::TensorNCxHWx::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor.h:283</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a71dda571a04037e564f238bb9a76f213"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">cutlass::Tensor4DCoord::h</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; h() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:87</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html">cutlass::layout::TensorNHWC</a></div><div class="ttdoc">Mapping function for 4-D NHWC tensors. </div><div class="ttdef"><b>Definition:</b> tensor.h:53</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNCxHWx_html_a57a1e3a60ba44629bbdabe43dc588899"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNCxHWx.html#a57a1e3a60ba44629bbdabe43dc588899">cutlass::layout::TensorNCxHWx::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor.h:280</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_ac172f5707f9eb601046c8fde392a2bf6"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#ac172f5707f9eb601046c8fde392a2bf6">cutlass::layout::TensorNHWC::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex index) const </div><div class="ttdoc">Returns the logical coordinate (n, h, w, c) from a given offset in linear memory. ...</div><div class="ttdef"><b>Definition:</b> tensor.h:123</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1TensorNHWC_html_a681b2a1c1a1df6b68b6fb68a18a8392a"><div class="ttname"><a href="classcutlass_1_1layout_1_1TensorNHWC.html#a681b2a1c1a1df6b68b6fb68a18a8392a">cutlass::layout::TensorNHWC::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns the offset of a coordinate (n, h, w, c) in linear memory. </div><div class="ttdef"><b>Definition:</b> tensor.h:108</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
