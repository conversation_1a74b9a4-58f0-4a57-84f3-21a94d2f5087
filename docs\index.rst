👋 Welcome to MLC LLM
=====================

`Discord <https://discord.gg/9Xpy2HGBuD>`_ | `GitHub <https://github.com/mlc-ai/mlc-llm>`_




MLC LLM is a machine learning compiler and high-performance deployment
engine for large language models.  The mission of this project is to enable
everyone to develop, optimize, and deploy AI models natively on everyone's platforms. 

Quick Start
-----------

Check out :ref:`quick-start` for quick start examples of using MLC LLM.

Introduction to MLC LLM
-----------------------

Check out :ref:`introduction-to-mlc-llm` for the introduction and tutorial of a complete workflow in MLC LLM.


.. toctree::
   :maxdepth: 1
   :caption: Get Started
   :hidden:

   get_started/quick_start.rst
   get_started/introduction.rst

.. toctree::
   :maxdepth: 1
   :caption: Build and Deploy Apps
   :hidden:

   deploy/webllm.rst
   deploy/rest.rst
   deploy/cli.rst
   deploy/python_engine.rst
   deploy/ios.rst
   deploy/android.rst
   deploy/ide_integration.rst
   deploy/mlc_chat_config.rst

.. toctree::
   :maxdepth: 1
   :caption: Compile Models
   :hidden:

   compilation/convert_weights.rst
   compilation/compile_models.rst
   compilation/package_libraries_and_weights.rst
   compilation/define_new_models.rst
   compilation/configure_quantization.rst

.. toctree::
   :maxdepth: 1
   :caption: Dependency Installation
   :hidden:

   install/tvm.rst
   install/mlc_llm.rst
   install/conda.rst
   install/gpu.rst
   install/emcc.rst

.. toctree::
   :maxdepth: 1
   :caption: Microserving API
   :hidden:

   microserving/tutorial.rst

.. toctree::
   :maxdepth: 1
   :caption: Community
   :hidden:

   community/guideline.rst
   community/faq.rst


.. toctree::
   :maxdepth: 1
   :caption: Privacy
   :hidden:

   privacy.rst
