//
// MessagePack for C++ deserializing routine
//
// Copyright (C) 2017 <PERSON>ON<PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_V3_NULL_VISITOR_DECL_HPP
#define MSGPACK_V3_NULL_VISITOR_DECL_HPP

#include "msgpack/v2/null_visitor_decl.hpp"

namespace msgpack {

/// @cond
MSGPACK_API_VERSION_NAMESPACE(v3) {
/// @endcond

using v2::null_visitor;

/// @cond
}  // MSGPACK_API_VERSION_NAMESPACE(v3)
/// @endcond

}  // namespace msgpack

#endif // MSGPACK_V3_NULL_VISITOR_DECL_HPP
