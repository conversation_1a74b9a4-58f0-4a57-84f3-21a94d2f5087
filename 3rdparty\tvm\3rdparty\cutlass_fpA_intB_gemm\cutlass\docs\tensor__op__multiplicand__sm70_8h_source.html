<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_op_multiplicand_sm70.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_2296cf082f2778f9a3503c8ea1010763.html">layout</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_op_multiplicand_sm70.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__op__multiplicand__sm70_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear_8h.html">cutlass/layout/pitch_linear.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span>layout {</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="comment">// template &lt;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="comment">//   int ElementSize,</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="comment">//   gemm::Operand Operand</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="comment">// &gt;</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="comment">// struct VoltaTensorOpMultiplicandCongruous;</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="comment">// template &lt;</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="comment">//   int ElementSize,</span></div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="comment">//   gemm::Operand Operand</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="comment">// &gt;</span></div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="comment">// struct ColumnMajorVoltaTensorOpMultiplicandCongruous;</span></div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="comment">// template &lt;</span></div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="comment">//   int ElementSize,</span></div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="comment">//   gemm::Operand Operand</span></div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="comment">// &gt;</span></div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="comment">// struct RowMajorVoltaTensorOpMultiplicandCongruous;</span></div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize&gt;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">   60</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">VoltaTensorOpMultiplicandCongruous</a> {</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">   63</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">   66</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a53359179aa16d5e938f097a5df36bb71">   69</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a53359179aa16d5e938f097a5df36bb71">Index</a> = int32_t;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#aed6da31b1c9467654006afb9154ef4ca">   72</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#aed6da31b1c9467654006afb9154ef4ca">LongIndex</a> = int64_t;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af07980e5d19d1a34db6704b6e7dab2fe">   75</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ae94bb98dc71e7510a577b0e628582a35">   78</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">   85</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = 128;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a37cf782182391c394b7bc00cb947f8cb">   88</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">TileShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;8, 4&gt;</a>;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ab823a7af93e0b440aaaef19f40d11500">   91</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;8, 2&gt;</a>;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">   97</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = ElementSize;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">   98</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = kAccessSize / <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a>;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  </div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionCount</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape</a>&lt;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>,</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a></div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a13f44ba4804056f56d5166a2f0403377">  103</a></span>&#160;  &gt;;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">AccessCount</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape</a>&lt;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>,</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a></div><div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a01bb7a9d4e19798075935e5a4fdae982">  108</a></span>&#160;  &gt;;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ace186f69fd389edff3909fe39598e93d">  126</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ace186f69fd389edff3909fe39598e93d">VoltaTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a53359179aa16d5e938f097a5df36bb71">Index</a> ldm = 0): stride_(ldm) { }</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ab804ea82f631e3950d476e12a92a7189">  130</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ab804ea82f631e3950d476e12a92a7189">VoltaTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>): stride_(stride) { }</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a0f6dfb913a9f6d1dd6a6e4d83e3c87ec">  134</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">VoltaTensorOpMultiplicandCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a0f6dfb913a9f6d1dd6a6e4d83e3c87ec">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ace186f69fd389edff3909fe39598e93d">VoltaTensorOpMultiplicandCongruous</a>(extent[0]);</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  }</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00141"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a7054268bd8313c4e3b55b0b27d662f8f">  141</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#aed6da31b1c9467654006afb9154ef4ca">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a7054268bd8313c4e3b55b0b27d662f8f">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    </div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="comment">// First, compute c and s of vector within source (in units of vector accesses)</span></div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordtype">int</span> vec_contiguous_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() / <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a>;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;    <span class="keywordtype">int</span> vec_strided_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>();</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    <span class="comment">// Compute the fundamental tile being accessed</span></div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    <span class="keywordtype">int</span> tile_contiguous_idx = vec_contiguous_idx / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a>;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    <span class="keywordtype">int</span> tile_strided_idx = vec_strided_idx / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a>;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <span class="keywordtype">int</span> tile_contiguous_residual = vec_contiguous_idx % <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a>;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    <span class="keywordtype">int</span> tile_strided_residual = vec_strided_idx % <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a>;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <span class="comment">// Then swizzle in a tile</span></div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <span class="comment">// Swizzle pattern is (tid[2:0] &lt;&lt; 2)|(tid[4:3] ^ tid[2:1])</span></div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <span class="keywordtype">int</span> permuted_strided_within_tile = (tile_contiguous_residual &gt;&gt; 1);</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <span class="keywordtype">int</span> permuted_contiguous_within_tile = (tile_strided_residual ^ permuted_strided_within_tile) |</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;                                       ((tile_contiguous_residual &amp; 1) &lt;&lt; 2);</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    <span class="comment">// Compute final element location</span></div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    <span class="keywordtype">int</span> element_contiguous = (tile_contiguous_idx * <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> +</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        permuted_contiguous_within_tile) * kElementsPerAccess + (coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() % <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a>);</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    <span class="keywordtype">int</span> element_strided = tile_strided_idx * <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a> + permuted_strided_within_tile;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    <span class="keywordflow">return</span> element_contiguous + element_strided * stride_[0];</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;  }</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">  170</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  }</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a64590ba904ff295c497d259813401029">  176</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a64590ba904ff295c497d259813401029">stride</a>() {</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;  }</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af9149c2f7914f62232dcb3bd8f46384d">  182</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#aed6da31b1c9467654006afb9154ef4ca">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af9149c2f7914f62232dcb3bd8f46384d">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    <span class="keywordflow">return</span> extent[1] * stride_[0];</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;  }</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;};</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize&gt;</div><div class="line"><a name="l00191"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html">  191</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html">ColumnMajorVoltaTensorOpMultiplicandCongruous</a> {</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a354c90c7af5bf5f2119ee92378fe0f53">  194</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a1125039c8a7a3f636806282a53a91414">  197</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div><div class="line"><a name="l00200"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a411d7c9444f2d66bf8b8f27d3a6f1226">  200</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a411d7c9444f2d66bf8b8f27d3a6f1226">Index</a> = int32_t;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div><div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ac398ab0e518a8e8ff09ca0ee2d58be8c">  203</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ac398ab0e518a8e8ff09ca0ee2d58be8c">LongIndex</a> = int64_t;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div><div class="line"><a name="l00206"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a541c3552d683b904206955be496a9684">  206</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a95761bc6391c623cd48000048be14d81">  209</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;</div><div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a6d02cbf1ad87aac334582cd91f0c2bd0">  215</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">VoltaTensorOpMultiplicandCongruous&lt;ElementSize&gt;</a>;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a478922587dc4bcf510121bc2309180c7">  218</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = Base::kAccessSize;</div><div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a7989e2bec8cc7d5ec9af9023e5eb6ce6">  219</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a7989e2bec8cc7d5ec9af9023e5eb6ce6">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::TileShape</a>;</div><div class="line"><a name="l00220"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#acc538c9d418a299ce3cffb8f914af15e">  220</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#acc538c9d418a299ce3cffb8f914af15e">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionShape</a>;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div><div class="line"><a name="l00226"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#afa7b88c7e7c79f8a2ae71ea85f675943">  226</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = Base::kElementSize;</div><div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a97621de01ef7a3c31f46e97e38dab9b2">  227</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = Base::kElementsPerAccess;</div><div class="line"><a name="l00228"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#aa9095d999b45d7e37dbeaa102112696a">  228</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#aa9095d999b45d7e37dbeaa102112696a">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionCount</a>;</div><div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a6c5a1858ade078036ed6659e3b3cce62">  229</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a6c5a1858ade078036ed6659e3b3cce62">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::AccessCount</a>;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">Base</a> layout_;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00246"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a9019c397159688cd2bafe55967ee7f36">  246</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a9019c397159688cd2bafe55967ee7f36">ColumnMajorVoltaTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a411d7c9444f2d66bf8b8f27d3a6f1226">Index</a> ldm = 0): layout_(ldm) { }</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a963c8058ab8ef1a5d21403bd1dc27277">  250</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a963c8058ab8ef1a5d21403bd1dc27277">ColumnMajorVoltaTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>): layout_(stride) { }</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00254"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#af4efe777f1d9ec8c62d870e3ced85114">  254</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html">ColumnMajorVoltaTensorOpMultiplicandCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#af4efe777f1d9ec8c62d870e3ced85114">packed</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html">ColumnMajorVoltaTensorOpMultiplicandCongruous</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>());</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;  }</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00261"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ae7bcec1e4b5e2f8960c6b4ba475df7a0">  261</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ac398ab0e518a8e8ff09ca0ee2d58be8c">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ae7bcec1e4b5e2f8960c6b4ba475df7a0">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;  }</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#adc9104c8495005e5df53accd5aca86d4">  267</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#adc9104c8495005e5df53accd5aca86d4">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ac398ab0e518a8e8ff09ca0ee2d58be8c">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>());</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;  }</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00274"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a9f2ab08d3706874cf69ee0a53e6353b4">  274</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a9f2ab08d3706874cf69ee0a53e6353b4">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>();</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  }</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a08840aa86668d1a359db6472884fcf2a">  280</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a08840aa86668d1a359db6472884fcf2a">stride</a>() {</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>();</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  }</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00286"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ae616638f7e2dc315865b3693f71f52cd">  286</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ac398ab0e518a8e8ff09ca0ee2d58be8c">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ae616638f7e2dc315865b3693f71f52cd">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af9149c2f7914f62232dcb3bd8f46384d">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;  }</div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;};</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize&gt;</div><div class="line"><a name="l00293"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html">  293</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html">RowMajorVoltaTensorOpMultiplicandCongruous</a> {</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div><div class="line"><a name="l00296"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a177d3d065bfab1153b7782f194f4717c">  296</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;</div><div class="line"><a name="l00299"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aa740540df69532336fc076787bb76f3a">  299</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;</div><div class="line"><a name="l00302"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ae9ab9cba1b0b7e8d73677d27aefe4179">  302</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ae9ab9cba1b0b7e8d73677d27aefe4179">Index</a> = int32_t;</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div><div class="line"><a name="l00305"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#afefc0ee1313d3771e6ada7dfe5a1e96c">  305</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#afefc0ee1313d3771e6ada7dfe5a1e96c">LongIndex</a> = int64_t;</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div><div class="line"><a name="l00308"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ac444580c230214d26e6bf38bd0c0b6cc">  308</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;</div><div class="line"><a name="l00311"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ab6b3e51e115cf8b9badc339ae9e509c4">  311</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;</div><div class="line"><a name="l00317"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#acb7a21efe21bed04ecf46a705745d8bb">  317</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">VoltaTensorOpMultiplicandCongruous&lt;ElementSize&gt;</a>;</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;</div><div class="line"><a name="l00320"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a469537089ba16873fde88b1d31255050">  320</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = Base::kAccessSize;</div><div class="line"><a name="l00321"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aaabe6a5ac2462ceaefb9112e89669243">  321</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aaabe6a5ac2462ceaefb9112e89669243">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::TileShape</a>;</div><div class="line"><a name="l00322"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#af3c9f1bd2d159857671da73de894d6c9">  322</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#af3c9f1bd2d159857671da73de894d6c9">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionShape</a>;</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div><div class="line"><a name="l00328"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a176656177616042b3ecf2b8d04519a73">  328</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = Base::kElementSize;</div><div class="line"><a name="l00329"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ae2e6dc5d33fe0acbd5e15440963d8ec9">  329</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = Base::kElementsPerAccess;</div><div class="line"><a name="l00330"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aab81368102d96f090a143aabf4f72595">  330</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aab81368102d96f090a143aabf4f72595">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionCount</a>;</div><div class="line"><a name="l00331"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a1613a66528cfbbb1d86e566166034b4f">  331</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a1613a66528cfbbb1d86e566166034b4f">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::AccessCount</a>;</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">Base</a> layout_;</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00348"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#acefc319105f8150e6005d2a1a7ce9366">  348</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#acefc319105f8150e6005d2a1a7ce9366">RowMajorVoltaTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ae9ab9cba1b0b7e8d73677d27aefe4179">Index</a> ldm = 0): layout_(ldm) { }</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00352"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a3b857440e261091eef2cccb93608c54d">  352</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a3b857440e261091eef2cccb93608c54d">RowMajorVoltaTensorOpMultiplicandCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>): layout_(stride) { }</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00356"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a0930c8e2ba61cdfd454817c841ef5135">  356</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html">RowMajorVoltaTensorOpMultiplicandCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a0930c8e2ba61cdfd454817c841ef5135">packed</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html">RowMajorVoltaTensorOpMultiplicandCongruous</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>());</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;  }</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00363"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#af6003c4aee50471c4c09ef74f3996560">  363</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#afefc0ee1313d3771e6ada7dfe5a1e96c">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#af6003c4aee50471c4c09ef74f3996560">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  }</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00369"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a49a401c647d68b6f845ccd704e7328bb">  369</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a49a401c647d68b6f845ccd704e7328bb">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#afefc0ee1313d3771e6ada7dfe5a1e96c">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>());</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;  }</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00376"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a2b898fb4943d32164d1537e31241d262">  376</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a2b898fb4943d32164d1537e31241d262">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>();</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;  }</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00382"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a465777cef252418e249c1ed6a33f5525">  382</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a465777cef252418e249c1ed6a33f5525">stride</a>() {</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>();</div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  }</div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00388"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a38fc7f3ffab51a56a76d297ecdc7edf7">  388</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#afefc0ee1313d3771e6ada7dfe5a1e96c">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a38fc7f3ffab51a56a76d297ecdc7edf7">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af9149c2f7914f62232dcb3bd8f46384d">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  }</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;};</div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;</div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;<span class="comment">// template &lt;int ElementSize, Operand Operand&gt;</span></div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize&gt;</div><div class="line"><a name="l00397"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">  397</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">VoltaTensorOpMultiplicandBCongruous</a> {</div><div class="line"><a name="l00399"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a2539ea13c07ef646d54d7e6f1f33286f">  399</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;</div><div class="line"><a name="l00402"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a04bc03f9b125eb07ca99289c49520845">  402</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div><div class="line"><a name="l00405"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aced549eda6008715c78b461396c81b87">  405</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aced549eda6008715c78b461396c81b87">Index</a> = int32_t;</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;</div><div class="line"><a name="l00408"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a635537ca830a3f621ed99ab323b1e7f1">  408</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a635537ca830a3f621ed99ab323b1e7f1">LongIndex</a> = int64_t;</div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;</div><div class="line"><a name="l00411"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#af26eb8761e8022b81ad74b7fbf1e2721">  411</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;</div><div class="line"><a name="l00414"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a96bcd423a4db984af13307d7f6a9aa24">  414</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;</div><div class="line"><a name="l00421"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aa3fb793c8975ae81e031bac761a23857">  421</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = 128;</div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;</div><div class="line"><a name="l00424"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a820421097af8b825f004f73e8ba89bf0">  424</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">TileShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;8, 4&gt;</a>;</div><div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;</div><div class="line"><a name="l00427"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#ae54ee072f8405ef10574d8898489b543">  427</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionShape</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape&lt;4, 4&gt;</a>;</div><div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;</div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;</div><div class="line"><a name="l00433"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aa1ac4434032b09b6bbec2761288d6854">  433</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = ElementSize;</div><div class="line"><a name="l00434"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a4f63aebef89812b3a2424a818f5919f4">  434</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = kAccessSize / <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a>;</div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;  </div><div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PartitionCount</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape</a>&lt;</div><div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>,</div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a></div><div class="line"><a name="l00439"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a0e5c5d56b6dc1daf9268b32ec18c44b0">  439</a></span>&#160;  &gt;;</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">AccessCount</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">PitchLinearShape</a>&lt;</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">PartitionShape::kContiguous</a>,</div><div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">PartitionShape::kStrided</a></div><div class="line"><a name="l00444"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a05a4f6a3634d44eb7847d1bd944d3af6">  444</a></span>&#160;  &gt;;</div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;</div><div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;</div><div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;</div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;</div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;</div><div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00462"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#ac31322c93fd5973b3652c8127c0b8f3a">  462</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#ac31322c93fd5973b3652c8127c0b8f3a">VoltaTensorOpMultiplicandBCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aced549eda6008715c78b461396c81b87">Index</a> ldm = 0): stride_(ldm) { }</div><div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;</div><div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00466"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#af096631a3d336c4d3c6d3c9706a0766c">  466</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#af096631a3d336c4d3c6d3c9706a0766c">VoltaTensorOpMultiplicandBCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>): stride_(stride) { }</div><div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;</div><div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00470"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a9e54d47e637060ba318d04416d35f4f4">  470</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">VoltaTensorOpMultiplicandBCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a9e54d47e637060ba318d04416d35f4f4">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">VoltaTensorOpMultiplicandBCongruous</a>(extent[0]);</div><div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;  }</div><div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00477"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a89ab20e8b1809e094ffa09ded1042c69">  477</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a635537ca830a3f621ed99ab323b1e7f1">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a89ab20e8b1809e094ffa09ded1042c69">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;    </div><div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;    <span class="comment">// First, compute c and s of vector within source (in units of vector accesses)</span></div><div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;    <span class="keywordtype">int</span> vec_contiguous_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() / <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a>;</div><div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;    <span class="keywordtype">int</span> vec_strided_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>();</div><div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;</div><div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;    <span class="comment">// Compute the fundamental tile being accessed</span></div><div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;    <span class="keywordtype">int</span> tile_contiguous_idx = vec_contiguous_idx / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a>;</div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;    <span class="keywordtype">int</span> tile_strided_idx = vec_strided_idx / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a>;</div><div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;</div><div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;    <span class="keywordtype">int</span> tile_contiguous_residual = vec_contiguous_idx % <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a>;</div><div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;    <span class="keywordtype">int</span> tile_strided_residual = vec_strided_idx % <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a>;</div><div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;</div><div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;    <span class="comment">// Then swizzle in a tile</span></div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;    <span class="comment">// Swizzle pattern is (tid[1:0] &lt;&lt; 3)|(tid &amp; 0x4)|(tid[1:0])</span></div><div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;    <span class="keywordtype">int</span> permuted_strided_within_tile = (tile_contiguous_residual &amp; 0x3);</div><div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;    <span class="keywordtype">int</span> permuted_contiguous_within_tile = (tile_strided_residual ^ permuted_strided_within_tile) |</div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;                                       (tile_contiguous_residual &amp; 0x4);</div><div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;  </div><div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;    <span class="comment">// Compute final element location</span></div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;    <span class="keywordtype">int</span> element_contiguous = (tile_contiguous_idx * <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">TileShape::kContiguous</a> +</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;        permuted_contiguous_within_tile) * kElementsPerAccess + (coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() % <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a>);</div><div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;    <span class="keywordtype">int</span> element_strided = tile_strided_idx * <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">TileShape::kStrided</a> + permuted_strided_within_tile;</div><div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;    <span class="keywordflow">return</span> element_contiguous + element_strided * stride_[0];</div><div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;  }</div><div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;</div><div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> </div><div class="line"><a name="l00507"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#adb5db94746b675b216ac68ded0c35c13">  507</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#adb5db94746b675b216ac68ded0c35c13">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;  }</div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00513"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#ace754b27bf19a8765e697d932db76b04">  513</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#ace754b27bf19a8765e697d932db76b04">stride</a>() {</div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;  }</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00519"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aaafaf46f0d3bb10de607e999e28ca87a">  519</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a635537ca830a3f621ed99ab323b1e7f1">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aaafaf46f0d3bb10de607e999e28ca87a">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;    <span class="keywordflow">return</span> extent[1] * stride_[0];</div><div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;  }</div><div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;};</div><div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;</div><div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;</div><div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize&gt;</div><div class="line"><a name="l00528"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html">  528</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html">ColumnMajorVoltaTensorOpMultiplicandBCongruous</a> {</div><div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;</div><div class="line"><a name="l00531"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aff2f8aaa5256aa9a4ca91575c7857766">  531</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;</div><div class="line"><a name="l00534"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a54c1bf44ae979e72af5946bb76d19f22">  534</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;</div><div class="line"><a name="l00537"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a19cf61b364213ff4cccaa75ffbfcac52">  537</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a19cf61b364213ff4cccaa75ffbfcac52">Index</a> = int32_t;</div><div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;</div><div class="line"><a name="l00540"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aece8bef8b6b52ecafccb265c439a64b0">  540</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aece8bef8b6b52ecafccb265c439a64b0">LongIndex</a> = int64_t;</div><div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;</div><div class="line"><a name="l00543"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ad5712f604df6d9d4c7d4e896d3edbfce">  543</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;</div><div class="line"><a name="l00546"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a66e18a8eb3e12a7d3df2acb0d843ac96">  546</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;</div><div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;</div><div class="line"><a name="l00552"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#af70725b417f2c35e19866be8d57487be">  552</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">VoltaTensorOpMultiplicandBCongruous&lt;ElementSize&gt;</a>;</div><div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;</div><div class="line"><a name="l00555"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab1a5c2c723f8c39bf21bf54b1e6f1127">  555</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = Base::kAccessSize;</div><div class="line"><a name="l00556"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab58f0d2e8bbfe703e850eb0b35c8b0a1">  556</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab58f0d2e8bbfe703e850eb0b35c8b0a1">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::TileShape</a>;</div><div class="line"><a name="l00557"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a4606210cdd1342bd1dc17ab37c4ef133">  557</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a4606210cdd1342bd1dc17ab37c4ef133">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionShape</a>;</div><div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;</div><div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;</div><div class="line"><a name="l00563"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a643e8cbff2d6339692cbec3ed34ad341">  563</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = Base::kElementSize;</div><div class="line"><a name="l00564"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#af7fb6b600c555fc0c5fb649289528854">  564</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = Base::kElementsPerAccess;</div><div class="line"><a name="l00565"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a50beb822a56d11fe7d7511b2514d9eeb">  565</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a50beb822a56d11fe7d7511b2514d9eeb">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionCount</a>;</div><div class="line"><a name="l00566"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a8262341cde0bddd17a92e0077fbd6e56">  566</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a8262341cde0bddd17a92e0077fbd6e56">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::AccessCount</a>;</div><div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;</div><div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;</div><div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160;</div><div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">Base</a> layout_;</div><div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;</div><div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;</div><div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00583"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab42f59c4fb58814ce2b5617b12e3faf0">  583</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab42f59c4fb58814ce2b5617b12e3faf0">ColumnMajorVoltaTensorOpMultiplicandBCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a19cf61b364213ff4cccaa75ffbfcac52">Index</a> ldm = 0): layout_(ldm) { }</div><div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;</div><div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00587"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a80099cf16729a8b553677fc70bea81bc">  587</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a80099cf16729a8b553677fc70bea81bc">ColumnMajorVoltaTensorOpMultiplicandBCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>): layout_(stride) { }</div><div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;</div><div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00591"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a61d5ba91f20217170458cb0affbe3e02">  591</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html">ColumnMajorVoltaTensorOpMultiplicandBCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a61d5ba91f20217170458cb0affbe3e02">packed</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html">ColumnMajorVoltaTensorOpMultiplicandBCongruous</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>());</div><div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;  }</div><div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;</div><div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00598"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a2f828abd034bcb9d47106bcac109d989">  598</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aece8bef8b6b52ecafccb265c439a64b0">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a2f828abd034bcb9d47106bcac109d989">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;  }</div><div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;</div><div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00604"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab83e570cf85596c1639c8eac50ae8597">  604</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab83e570cf85596c1639c8eac50ae8597">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aece8bef8b6b52ecafccb265c439a64b0">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>());</div><div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;  }</div><div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;</div><div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00611"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a757ed1e942e9818559b7c0ad765414b7">  611</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a757ed1e942e9818559b7c0ad765414b7">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#adb5db94746b675b216ac68ded0c35c13">stride</a>();</div><div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;  }</div><div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;</div><div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00617"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a50476b78270a2012b0823b7d8227f5bb">  617</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a50476b78270a2012b0823b7d8227f5bb">stride</a>() {</div><div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#adb5db94746b675b216ac68ded0c35c13">stride</a>();</div><div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;  }</div><div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;</div><div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00623"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a9ac9d347fb65719c8a05295816120fec">  623</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aece8bef8b6b52ecafccb265c439a64b0">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a9ac9d347fb65719c8a05295816120fec">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aaafaf46f0d3bb10de607e999e28ca87a">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;  }</div><div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;};</div><div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;</div><div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize&gt;</div><div class="line"><a name="l00630"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html">  630</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html">RowMajorVoltaTensorOpMultiplicandBCongruous</a> {</div><div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;</div><div class="line"><a name="l00633"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a921af4231b727ed09cbd6f8f03443b6c">  633</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;</div><div class="line"><a name="l00636"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#af4a8ae7ae7264c025a223d49bc296a56">  636</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;</div><div class="line"><a name="l00639"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a80908838bd1135f03e37d342a140bc34">  639</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a80908838bd1135f03e37d342a140bc34">Index</a> = int32_t;</div><div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;</div><div class="line"><a name="l00642"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a25800f82b5edd600b45db3ce69b51ddb">  642</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a25800f82b5edd600b45db3ce69b51ddb">LongIndex</a> = int64_t;</div><div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;</div><div class="line"><a name="l00645"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a4a0e04585251e71a206eed06abcba609">  645</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;</div><div class="line"><a name="l00648"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#aa336aaa24657da9066b65071fc6c079d">  648</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;</div><div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;</div><div class="line"><a name="l00654"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a8b6ca2d7852ba45313d67cf83536bd1e">  654</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">VoltaTensorOpMultiplicandBCongruous&lt;ElementSize&gt;</a>;</div><div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;</div><div class="line"><a name="l00657"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ad2355d0822557706181cdc0f69d120d5">  657</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = Base::kAccessSize;</div><div class="line"><a name="l00658"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a308cc4d18560408bc3c17ea090877b18">  658</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a308cc4d18560408bc3c17ea090877b18">TileShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::TileShape</a>;</div><div class="line"><a name="l00659"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ac2ea9ce6186f5940fbfbb9d8528c450d">  659</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ac2ea9ce6186f5940fbfbb9d8528c450d">PartitionShape</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionShape</a>;</div><div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;</div><div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;</div><div class="line"><a name="l00665"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a43c426d2a016db41441769263525254b">  665</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = Base::kElementSize;</div><div class="line"><a name="l00666"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a57a4589fb0bc4b5675577326f21250b9">  666</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = Base::kElementsPerAccess;</div><div class="line"><a name="l00667"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ac8c8479bd68f8ff26281ccf20257c416">  667</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ac8c8479bd68f8ff26281ccf20257c416">PartitionCount</a> =  <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::PartitionCount</a>;</div><div class="line"><a name="l00668"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ae6751845741fbf3494391564ba85c1c3">  668</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ae6751845741fbf3494391564ba85c1c3">AccessCount</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">Base::AccessCount</a>;</div><div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;</div><div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00671"></a><span class="lineno">  671</span>&#160;</div><div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;</div><div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">Base</a> layout_;</div><div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;</div><div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;</div><div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00685"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#aad4ca016701d73fee071bf10c35219ab">  685</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#aad4ca016701d73fee071bf10c35219ab">RowMajorVoltaTensorOpMultiplicandBCongruous</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a80908838bd1135f03e37d342a140bc34">Index</a> ldm = 0): layout_(ldm) { }</div><div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;</div><div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00689"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ab6c4659f6f6a893ae6327964158a762b">  689</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ab6c4659f6f6a893ae6327964158a762b">RowMajorVoltaTensorOpMultiplicandBCongruous</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>): layout_(stride) { }</div><div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;</div><div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00693"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a860c3a44cff442eada17a765b5744049">  693</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html">RowMajorVoltaTensorOpMultiplicandBCongruous</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a860c3a44cff442eada17a765b5744049">packed</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html">RowMajorVoltaTensorOpMultiplicandBCongruous</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>());</div><div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;  }</div><div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;</div><div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00700"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#abd1a744d04f46d408e2bdce12660ccf2">  700</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a25800f82b5edd600b45db3ce69b51ddb">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#abd1a744d04f46d408e2bdce12660ccf2">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;  }</div><div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;</div><div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00706"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ab5bc99d0b654ac8a086e1f8012fe2f16">  706</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ab5bc99d0b654ac8a086e1f8012fe2f16">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a25800f82b5edd600b45db3ce69b51ddb">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>());</div><div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;  }</div><div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;</div><div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00713"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a3339e229b6a98b36c12fbe078c000253">  713</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a3339e229b6a98b36c12fbe078c000253">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#adb5db94746b675b216ac68ded0c35c13">stride</a>();</div><div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;  }</div><div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;</div><div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00719"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a2670d9258bf26606a2893178060c4045">  719</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a2670d9258bf26606a2893178060c4045">stride</a>() {</div><div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#adb5db94746b675b216ac68ded0c35c13">stride</a>();</div><div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;  }</div><div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;</div><div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00725"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a2a556452484f1d6958ec57d0a5b68dea">  725</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a25800f82b5edd600b45db3ce69b51ddb">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a2a556452484f1d6958ec57d0a5b68dea">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aaafaf46f0d3bb10de607e999e28ca87a">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;  }</div><div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;};</div><div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;</div><div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> KBlock&gt;</div><div class="line"><a name="l00733"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">  733</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">VoltaTensorOpMultiplicandCrosswise</a> {</div><div class="line"><a name="l00735"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa8d1036369fd26bad635446582a6fd84">  735</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;</div><div class="line"><a name="l00738"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#ad519840cf6c7dc8a88e06133c15ac598">  738</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;</div><div class="line"><a name="l00741"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a064fd090cbaa1e359bc44e6edcbf7cde">  741</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a064fd090cbaa1e359bc44e6edcbf7cde">Index</a> = int32_t;</div><div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;</div><div class="line"><a name="l00744"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a61455b7fac8d838b05b8d0c3cf6b5b12">  744</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a61455b7fac8d838b05b8d0c3cf6b5b12">LongIndex</a> = int64_t;</div><div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;</div><div class="line"><a name="l00747"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#acd003fd35641c72f7cfd35922ec2cab4">  747</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>;</div><div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;</div><div class="line"><a name="l00750"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a88aa7ae9f23d8949cc790279d295273d">  750</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;</div><div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;</div><div class="line"><a name="l00757"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a5e7ab2fc429ba408a2fd537489bc87ae">  757</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = 64;</div><div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;</div><div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;</div><div class="line"><a name="l00763"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a5e0c3537c079116e936e0f7365f37642">  763</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = ElementSize;</div><div class="line"><a name="l00764"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a150d62daad6c89add3c0cc3bf6402b39">  764</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = kAccessSize / <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a>;</div><div class="line"><a name="l00765"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8ecfcee5a9743848a57ec78e4a73b8ba">  765</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kKBlock = KBlock;</div><div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;</div><div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;</div><div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;</div><div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00781"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa68d825db94190611ce9bd54e25b0d48">  781</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa68d825db94190611ce9bd54e25b0d48">VoltaTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a064fd090cbaa1e359bc44e6edcbf7cde">Index</a> ldm = 0) : stride_(ldm) {}</div><div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;</div><div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00785"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#af7cedde7974be824cef4ed7248903320">  785</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#af7cedde7974be824cef4ed7248903320">VoltaTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>) : stride_(stride) {}</div><div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;</div><div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00789"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa27ee17b10d393e8578afdbdedaa190e">  789</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">VoltaTensorOpMultiplicandCrosswise</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa27ee17b10d393e8578afdbdedaa190e">packed</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">VoltaTensorOpMultiplicandCrosswise</a>(extent[1]);</div><div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;  }</div><div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;</div><div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00796"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#abf01787fadeda14d212bf99efe40e32e">  796</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a61455b7fac8d838b05b8d0c3cf6b5b12">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#abf01787fadeda14d212bf99efe40e32e">operator()</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;</div><div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;    <span class="comment">// First, compute c and s of vector within source (in units of vector</span></div><div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;    <span class="comment">// accesses)</span></div><div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;    <span class="keywordtype">int</span> vec_contiguous_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() / <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a>;</div><div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;    <span class="keywordtype">int</span> vec_strided_idx = coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>();</div><div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;</div><div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;    <span class="comment">// Then swizzle</span></div><div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;    <span class="comment">// The mapping is like this:</span></div><div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;    <span class="comment">// id[1:0]|(id[3]^id[4])|id[2]</span></div><div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;</div><div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;    <span class="keywordtype">int</span> vec_strided_within_tile = vec_contiguous_idx &amp; 0x7;</div><div class="line"><a name="l00811"></a><span class="lineno">  811</span>&#160;    <span class="keywordtype">int</span> permuted_vec_contiguous =</div><div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;        (vec_strided_idx &amp; (~0xF)) + (vec_strided_idx &amp; 0x3) * 4 +</div><div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;        (((vec_strided_idx &gt;&gt; 2) ^ ((vec_strided_idx &amp; 0x10) &gt;&gt; 3)) &amp; 0x3);</div><div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;</div><div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;    permuted_vec_contiguous ^= ((vec_strided_within_tile &gt;&gt; 1) &amp; 0x3);</div><div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;</div><div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;    <span class="keywordtype">int</span> permuted_vec_strided = vec_contiguous_idx;</div><div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;</div><div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00820"></a><span class="lineno">  820</span>&#160;    <span class="comment">// Compute final element location</span></div><div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;</div><div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160;    <span class="keywordtype">int</span> element_contiguous = permuted_vec_contiguous *  kElementsPerAccess + </div><div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;                             (coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() % <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a>);</div><div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;    </div><div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;    <span class="keywordflow">return</span> element_contiguous + permuted_vec_strided * (stride_[0] * <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a>);</div><div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;  }</div><div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160;</div><div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00831"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8f6d1bd78917c94cbea9b8f3f51ff35b">  831</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8f6d1bd78917c94cbea9b8f3f51ff35b">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> stride_; }</div><div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;</div><div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00835"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a2b15613a7fb66206c502077137820572">  835</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a2b15613a7fb66206c502077137820572">stride</a>() { <span class="keywordflow">return</span> stride_; }</div><div class="line"><a name="l00836"></a><span class="lineno">  836</span>&#160;</div><div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00840"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a0c2e65352446d60ea1a917ff840245e8">  840</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a61455b7fac8d838b05b8d0c3cf6b5b12">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a0c2e65352446d60ea1a917ff840245e8">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;    <span class="keywordflow">return</span> extent[0] * stride_[0];</div><div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;  }</div><div class="line"><a name="l00843"></a><span class="lineno">  843</span>&#160;};</div><div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;</div><div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> KBlock&gt;</div><div class="line"><a name="l00848"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html">  848</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html">ColumnMajorVoltaTensorOpMultiplicandCrosswise</a> {</div><div class="line"><a name="l00850"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a1ae07ad4be4b0dee528f5f60f69f2c5e">  850</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00851"></a><span class="lineno">  851</span>&#160;</div><div class="line"><a name="l00853"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a5749b6879a23f4f94e383dd4e84f4208">  853</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;</div><div class="line"><a name="l00856"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a7f6ef66932fda16f2784dd2a1e224ca5">  856</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a7f6ef66932fda16f2784dd2a1e224ca5">Index</a> = int32_t;</div><div class="line"><a name="l00857"></a><span class="lineno">  857</span>&#160;</div><div class="line"><a name="l00859"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ab0f9e152320c2785c856965e6e3c02fb">  859</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ab0f9e152320c2785c856965e6e3c02fb">LongIndex</a> = int64_t;</div><div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160;</div><div class="line"><a name="l00862"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#af9fc3eefc5139991345e81b5714119a7">  862</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;</div><div class="line"><a name="l00865"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a30a3aa04f9ce7a572288b182d2d0397e">  865</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;</div><div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160;</div><div class="line"><a name="l00871"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a9ea316d870cf7abc6f1f6bb193af9b9b">  871</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">VoltaTensorOpMultiplicandCrosswise&lt;ElementSize, KBlock&gt;</a>;</div><div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;</div><div class="line"><a name="l00874"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a9fbbafdefa92eaecbe9b9d89d118a6f7">  874</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = Base::kAccessSize;</div><div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;</div><div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;</div><div class="line"><a name="l00880"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a44dba8d44799aa180e2e0d7dc4410eea">  880</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = Base::kElementSize;</div><div class="line"><a name="l00881"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a9fee65225e8075cf8d2ecc812c30f35e">  881</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = Base::kElementsPerAccess;</div><div class="line"><a name="l00882"></a><span class="lineno">  882</span>&#160;</div><div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;</div><div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">Base</a> layout_;</div><div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;</div><div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00892"></a><span class="lineno">  892</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;</div><div class="line"><a name="l00896"></a><span class="lineno">  896</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00897"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#af2456911e4cfca021d50ec16ca1d7505">  897</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#af2456911e4cfca021d50ec16ca1d7505">ColumnMajorVoltaTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a7f6ef66932fda16f2784dd2a1e224ca5">Index</a> ldm = 0) : layout_(ldm) {}</div><div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;</div><div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00901"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a7c839af8ec4dda7a6114d49daec52728">  901</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a7c839af8ec4dda7a6114d49daec52728">ColumnMajorVoltaTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>) : layout_(stride) {}</div><div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;</div><div class="line"><a name="l00904"></a><span class="lineno">  904</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00905"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ac0c8aea5f38628ce078dd1562e44ff06">  905</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html">ColumnMajorVoltaTensorOpMultiplicandCrosswise</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ac0c8aea5f38628ce078dd1562e44ff06">packed</a>(</div><div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;      <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html">ColumnMajorVoltaTensorOpMultiplicandCrosswise</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>());</div><div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;  }</div><div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;</div><div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00913"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a28756bc06eb1cf2567c51ee78f161911">  913</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ab0f9e152320c2785c856965e6e3c02fb">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a28756bc06eb1cf2567c51ee78f161911">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;  }</div><div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;</div><div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00919"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a5b7ac45dcec3ffdee65117ab9d9c439d">  919</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a5b7ac45dcec3ffdee65117ab9d9c439d">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ab0f9e152320c2785c856965e6e3c02fb">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00920"></a><span class="lineno">  920</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>());</div><div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;  }</div><div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;</div><div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00926"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a2d5fa68188863017afcb0f41848c13ba">  926</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a2d5fa68188863017afcb0f41848c13ba">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8f6d1bd78917c94cbea9b8f3f51ff35b">stride</a>(); }</div><div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;</div><div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00930"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a45ab64b5e43d8fa9f6bbb22111c97c56">  930</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a45ab64b5e43d8fa9f6bbb22111c97c56">stride</a>() { <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8f6d1bd78917c94cbea9b8f3f51ff35b">stride</a>(); }</div><div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;</div><div class="line"><a name="l00934"></a><span class="lineno">  934</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00935"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a1c709df04e3d4693707dc30b6c1f08f5">  935</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ab0f9e152320c2785c856965e6e3c02fb">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a1c709df04e3d4693707dc30b6c1f08f5">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a0c2e65352446d60ea1a917ff840245e8">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>()));</div><div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;  }</div><div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;};</div><div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;</div><div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementSize, <span class="keywordtype">int</span> KBlock&gt;</div><div class="line"><a name="l00943"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html">  943</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html">RowMajorVoltaTensorOpMultiplicandCrosswise</a> {</div><div class="line"><a name="l00945"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aa6f600940d38ea49079c6e411f34d2c1">  945</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">kRank</a> = 2;</div><div class="line"><a name="l00946"></a><span class="lineno">  946</span>&#160;</div><div class="line"><a name="l00948"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#afbff2da11814cff114a68a12206cf1fb">  948</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">kStrideRank</a> = 1;</div><div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;</div><div class="line"><a name="l00951"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a783af2d53a8d6a93c878c9ec4ebdd6b1">  951</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a783af2d53a8d6a93c878c9ec4ebdd6b1">Index</a> = int32_t;</div><div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;</div><div class="line"><a name="l00954"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aea4095c067a1f92f8d65c9f3372de90e">  954</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aea4095c067a1f92f8d65c9f3372de90e">LongIndex</a> = int64_t;</div><div class="line"><a name="l00955"></a><span class="lineno">  955</span>&#160;</div><div class="line"><a name="l00957"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a1b746aa0de4735668a1906bef6f54b12">  957</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;</div><div class="line"><a name="l00960"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aa36fa5f86c2c546cdc67a0cfec5355a5">  960</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index, LongIndex&gt;</a>;</div><div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;</div><div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;  <span class="comment">// Invariants</span></div><div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;</div><div class="line"><a name="l00966"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aca6bbb33a339a182fbc6b7cb40938d0c">  966</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">Base</a> = <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">VoltaTensorOpMultiplicandCrosswise&lt;ElementSize, KBlock&gt;</a>;</div><div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;</div><div class="line"><a name="l00969"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a88192ee545e21fa3096e082599e69459">  969</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">kAccessSize</a> = Base::kAccessSize;</div><div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;</div><div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;  <span class="comment">// Static constants</span></div><div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;</div><div class="line"><a name="l00975"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#abdcf1ff5105cf7a26817a62bc5e7adcc">  975</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">kElementSize</a> = Base::kElementSize;</div><div class="line"><a name="l00976"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aa9cd0b0069123cc028acd65f5f395f19">  976</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">kElementsPerAccess</a> = Base::kElementsPerAccess;</div><div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;</div><div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00979"></a><span class="lineno">  979</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;</div><div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">Base</a> layout_;</div><div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;</div><div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00988"></a><span class="lineno">  988</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;</div><div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00992"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a602a5e3cd52edc8c6e733cc8ea271484">  992</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a602a5e3cd52edc8c6e733cc8ea271484">RowMajorVoltaTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a783af2d53a8d6a93c878c9ec4ebdd6b1">Index</a> ldm = 0) : layout_(ldm) {}</div><div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;</div><div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00996"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a324cdfe3699077b6ba1f25f807e117c7">  996</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a324cdfe3699077b6ba1f25f807e117c7">RowMajorVoltaTensorOpMultiplicandCrosswise</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">stride</a>) : layout_(stride) {}</div><div class="line"><a name="l00997"></a><span class="lineno">  997</span>&#160;</div><div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01000"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a6ccf0dc730b1b68b5f842c10d4fb710c"> 1000</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html">RowMajorVoltaTensorOpMultiplicandCrosswise</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a6ccf0dc730b1b68b5f842c10d4fb710c">packed</a>(</div><div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;      <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent) {</div><div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html">RowMajorVoltaTensorOpMultiplicandCrosswise</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>());</div><div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160;  }</div><div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160;</div><div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01008"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a28d2430f1d6fc98eb1cccd36fe985099"> 1008</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aea4095c067a1f92f8d65c9f3372de90e">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a28d2430f1d6fc98eb1cccd36fe985099">operator()</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l01009"></a><span class="lineno"> 1009</span>&#160;    <span class="keywordflow">return</span> layout_(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;  }</div><div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;</div><div class="line"><a name="l01013"></a><span class="lineno"> 1013</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01014"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a36a240a540307090f18c6ed152deeae2"> 1014</a></span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a36a240a540307090f18c6ed152deeae2">inverse</a>(<a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aea4095c067a1f92f8d65c9f3372de90e">LongIndex</a> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a> coord = layout_.inverse(offset);</div><div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>(), coord.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>());</div><div class="line"><a name="l01017"></a><span class="lineno"> 1017</span>&#160;  }</div><div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;</div><div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01021"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#ab1bad7daa1b2a62d33fe499c4e1d830b"> 1021</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#ab1bad7daa1b2a62d33fe499c4e1d830b">stride</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8f6d1bd78917c94cbea9b8f3f51ff35b">stride</a>(); }</div><div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;</div><div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01025"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#afeb403bfdff6c2ec1c494e37b7034894"> 1025</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp;<a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#afeb403bfdff6c2ec1c494e37b7034894">stride</a>() { <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8f6d1bd78917c94cbea9b8f3f51ff35b">stride</a>(); }</div><div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;</div><div class="line"><a name="l01029"></a><span class="lineno"> 1029</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01030"></a><span class="lineno"><a class="line" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a0a6c5b20d5c84ace7d2440c03b42f29e"> 1030</a></span>&#160;  <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aea4095c067a1f92f8d65c9f3372de90e">LongIndex</a> <a class="code" href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a0a6c5b20d5c84ace7d2440c03b42f29e">capacity</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;extent)<span class="keyword"> const </span>{</div><div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;    <span class="keywordflow">return</span> layout_.<a class="code" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a0c2e65352446d60ea1a917ff840245e8">capacity</a>(<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">PitchLinearCoord</a>(extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>(), extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>()));</div><div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;  }</div><div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;};</div><div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;</div><div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;} <span class="comment">// namespace layout</span></div><div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;</div><div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_a38fc7f3ffab51a56a76d297ecdc7edf7"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a38fc7f3ffab51a56a76d297ecdc7edf7">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:388</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_a1c709df04e3d4693707dc30b6c1f08f5"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a1c709df04e3d4693707dc30b6c1f08f5">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:935</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_ab1bad7daa1b2a62d33fe499c4e1d830b"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#ab1bad7daa1b2a62d33fe499c4e1d830b">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:1021</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdoc">Template mapping a row-major view of pitch-linear memory to VoltaTensorOpMultiplicandCongruous. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:630</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a19cf61b364213ff4cccaa75ffbfcac52"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a19cf61b364213ff4cccaa75ffbfcac52">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:537</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_aaafaf46f0d3bb10de607e999e28ca87a"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aaafaf46f0d3bb10de607e999e28ca87a">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:519</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_ae7bcec1e4b5e2f8960c6b4ba475df7a0"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ae7bcec1e4b5e2f8960c6b4ba475df7a0">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:261</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_ab83e570cf85596c1639c8eac50ae8597"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab83e570cf85596c1639c8eac50ae8597">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:604</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_a602a5e3cd52edc8c6e733cc8ea271484"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a602a5e3cd52edc8c6e733cc8ea271484">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::RowMajorVoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandCrosswise(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:992</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a53359179aa16d5e938f097a5df36bb71"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a53359179aa16d5e938f097a5df36bb71">cutlass::layout::VoltaTensorOpMultiplicandCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:69</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_ab804ea82f631e3950d476e12a92a7189"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ab804ea82f631e3950d476e12a92a7189">cutlass::layout::VoltaTensorOpMultiplicandCongruous::VoltaTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:130</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html">cutlass::layout::PitchLinearCoord</a></div><div class="ttdoc">Coordinate in pitch-linear space. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:52</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a50476b78270a2012b0823b7d8227f5bb"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a50476b78270a2012b0823b7d8227f5bb">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:617</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_aa9095d999b45d7e37dbeaa102112696a"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#aa9095d999b45d7e37dbeaa102112696a">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:228</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_acc538c9d418a299ce3cffb8f914af15e"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#acc538c9d418a299ce3cffb8f914af15e">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:220</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_a0930c8e2ba61cdfd454817c841ef5135"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a0930c8e2ba61cdfd454817c841ef5135">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:356</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_a45ab64b5e43d8fa9f6bbb22111c97c56"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a45ab64b5e43d8fa9f6bbb22111c97c56">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:930</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_ab0f9e152320c2785c856965e6e3c02fb"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ab0f9e152320c2785c856965e6e3c02fb">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:859</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_ab5bc99d0b654ac8a086e1f8012fe2f16"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ab5bc99d0b654ac8a086e1f8012fe2f16">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:706</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_a0a6c5b20d5c84ace7d2440c03b42f29e"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a0a6c5b20d5c84ace7d2440c03b42f29e">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:1030</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a1b5afacb3737012d82f8037ffd68b28b"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1b5afacb3737012d82f8037ffd68b28b">cutlass::layout::VoltaTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:170</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_a465777cef252418e249c1ed6a33f5525"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a465777cef252418e249c1ed6a33f5525">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:382</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_ab42f59c4fb58814ce2b5617b12e3faf0"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab42f59c4fb58814ce2b5617b12e3faf0">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::ColumnMajorVoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandBCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:583</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_a1613a66528cfbbb1d86e566166034b4f"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a1613a66528cfbbb1d86e566166034b4f">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:331</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_a2d5fa68188863017afcb0f41848c13ba"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a2d5fa68188863017afcb0f41848c13ba">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:926</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_aece8bef8b6b52ecafccb265c439a64b0"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#aece8bef8b6b52ecafccb265c439a64b0">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:540</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:848</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_a89ab20e8b1809e094ffa09ded1042c69"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a89ab20e8b1809e094ffa09ded1042c69">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:477</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_a324cdfe3699077b6ba1f25f807e117c7"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a324cdfe3699077b6ba1f25f807e117c7">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::RowMajorVoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandCrosswise(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:996</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_adb5db94746b675b216ac68ded0c35c13"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#adb5db94746b675b216ac68ded0c35c13">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:507</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_ad404b4e10152a83243aaf95f7e2ab26a"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ad404b4e10152a83243aaf95f7e2ab26a">cutlass::layout::VoltaTensorOpMultiplicandCongruous::kElementSize</a></div><div class="ttdeci">static int const kElementSize</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:97</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_a308cc4d18560408bc3c17ea090877b18"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a308cc4d18560408bc3c17ea090877b18">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:658</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_a0c2e65352446d60ea1a917ff840245e8"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a0c2e65352446d60ea1a917ff840245e8">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:840</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_aaabe6a5ac2462ceaefb9112e89669243"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aaabe6a5ac2462ceaefb9112e89669243">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:321</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_a860c3a44cff442eada17a765b5744049"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a860c3a44cff442eada17a765b5744049">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandBCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:693</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_adc9104c8495005e5df53accd5aca86d4"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#adc9104c8495005e5df53accd5aca86d4">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:267</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_a25800f82b5edd600b45db3ce69b51ddb"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a25800f82b5edd600b45db3ce69b51ddb">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:642</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a0f6dfb913a9f6d1dd6a6e4d83e3c87ec"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a0f6dfb913a9f6d1dd6a6e4d83e3c87ec">cutlass::layout::VoltaTensorOpMultiplicandCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:134</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a4606210cdd1342bd1dc17ab37c4ef133"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a4606210cdd1342bd1dc17ab37c4ef133">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:557</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a></div><div class="ttdoc">Template defining a shape used by pitch-linear operators. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:43</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_afeb403bfdff6c2ec1c494e37b7034894"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#afeb403bfdff6c2ec1c494e37b7034894">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:1025</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_a49a401c647d68b6f845ccd704e7328bb"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a49a401c647d68b6f845ccd704e7328bb">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:369</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_a411d7c9444f2d66bf8b8f27d3a6f1226"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a411d7c9444f2d66bf8b8f27d3a6f1226">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:200</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_a3b857440e261091eef2cccb93608c54d"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a3b857440e261091eef2cccb93608c54d">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::RowMajorVoltaTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:352</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_a7989e2bec8cc7d5ec9af9023e5eb6ce6"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a7989e2bec8cc7d5ec9af9023e5eb6ce6">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:219</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_ace754b27bf19a8765e697d932db76b04"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#ace754b27bf19a8765e697d932db76b04">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:513</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdoc">Template mapping a column-major view of pitch-linear memory to VoltaTensorOpMultiplicandCongruous. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:528</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html_aaec0afa0c26627d951d2d2b98a3e5601"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">cutlass::layout::PitchLinearShape::kStrided</a></div><div class="ttdeci">static int const kStrided</div><div class="ttdef"><b>Definition:</b> pitch_linear.h:45</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_af7cedde7974be824cef4ed7248903320"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#af7cedde7974be824cef4ed7248903320">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::VoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandCrosswise(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:785</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a9ac9d347fb65719c8a05295816120fec"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a9ac9d347fb65719c8a05295816120fec">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:623</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_a28d2430f1d6fc98eb1cccd36fe985099"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a28d2430f1d6fc98eb1cccd36fe985099">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:1008</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_aced549eda6008715c78b461396c81b87"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#aced549eda6008715c78b461396c81b87">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:405</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html_a0d1cfb72e7511d162d123fcb36d181b7"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">cutlass::layout::PitchLinearShape::kContiguous</a></div><div class="ttdeci">static int const kContiguous</div><div class="ttdef"><b>Definition:</b> pitch_linear.h:44</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_ac8c8479bd68f8ff26281ccf20257c416"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ac8c8479bd68f8ff26281ccf20257c416">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:667</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a21bc56227398fa3a011e635a88d5d595"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a21bc56227398fa3a011e635a88d5d595">cutlass::layout::VoltaTensorOpMultiplicandCongruous::kElementsPerAccess</a></div><div class="ttdeci">static int const kElementsPerAccess</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:98</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_a5b7ac45dcec3ffdee65117ab9d9c439d"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a5b7ac45dcec3ffdee65117ab9d9c439d">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:919</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a61d5ba91f20217170458cb0affbe3e02"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a61d5ba91f20217170458cb0affbe3e02">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandBCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:591</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_ac398ab0e518a8e8ff09ca0ee2d58be8c"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ac398ab0e518a8e8ff09ca0ee2d58be8c">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:203</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_aab81368102d96f090a143aabf4f72595"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#aab81368102d96f090a143aabf4f72595">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:330</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_a2a556452484f1d6958ec57d0a5b68dea"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a2a556452484f1d6958ec57d0a5b68dea">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:725</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a2f828abd034bcb9d47106bcac109d989"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a2f828abd034bcb9d47106bcac109d989">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:598</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a50beb822a56d11fe7d7511b2514d9eeb"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a50beb822a56d11fe7d7511b2514d9eeb">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::PartitionCount</a></div><div class="ttdeci">typename Base::PartitionCount PartitionCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:565</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_a783af2d53a8d6a93c878c9ec4ebdd6b1"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a783af2d53a8d6a93c878c9ec4ebdd6b1">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:951</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous</a></div><div class="ttdoc">Template mapping a row-major view of pitch-linear memory to VoltaTensorOpMultiplicandCongruous. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:293</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_af738c34a6c8abbec8f88f5235014c188"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af738c34a6c8abbec8f88f5235014c188">cutlass::layout::VoltaTensorOpMultiplicandCongruous::kRank</a></div><div class="ttdeci">static int const kRank</div><div class="ttdoc">Logical rank of tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:63</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_a6ccf0dc730b1b68b5f842c10d4fb710c"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a6ccf0dc730b1b68b5f842c10d4fb710c">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandCrosswise packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:1000</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_afefc0ee1313d3771e6ada7dfe5a1e96c"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#afefc0ee1313d3771e6ada7dfe5a1e96c">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:305</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_ac2ea9ce6186f5940fbfbb9d8528c450d"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ac2ea9ce6186f5940fbfbb9d8528c450d">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:659</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_a36a240a540307090f18c6ed152deeae2"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#a36a240a540307090f18c6ed152deeae2">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::inverse</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord inverse(LongIndex offset) const </div><div class="ttdoc">Inverse of layout function, mapping linear offset to logical coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:1014</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_af2456911e4cfca021d50ec16ca1d7505"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#af2456911e4cfca021d50ec16ca1d7505">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::ColumnMajorVoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandCrosswise(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:897</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html">cutlass::layout::VoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdoc">Template based on element size (in bits) - defined in terms of pitch-linear memory. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:397</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_ae6751845741fbf3494391564ba85c1c3"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ae6751845741fbf3494391564ba85c1c3">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:668</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_af6003c4aee50471c4c09ef74f3996560"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#af6003c4aee50471c4c09ef74f3996560">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:363</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_a28756bc06eb1cf2567c51ee78f161911"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a28756bc06eb1cf2567c51ee78f161911">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:913</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_ae9ab9cba1b0b7e8d73677d27aefe4179"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#ae9ab9cba1b0b7e8d73677d27aefe4179">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:302</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_af096631a3d336c4d3c6d3c9706a0766c"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#af096631a3d336c4d3c6d3c9706a0766c">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::VoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandBCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:466</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_aa27ee17b10d393e8578afdbdedaa190e"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa27ee17b10d393e8578afdbdedaa190e">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandCrosswise packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:789</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html_adb31bc9b8cf49dfff64245b70a850834"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">cutlass::layout::PitchLinearCoord::contiguous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; contiguous() const </div><div class="ttdoc">Returns the contiguous dimension. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:89</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_ab6c4659f6f6a893ae6327964158a762b"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#ab6c4659f6f6a893ae6327964158a762b">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::RowMajorVoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandBCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:689</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_a9e54d47e637060ba318d04416d35f4f4"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a9e54d47e637060ba318d04416d35f4f4">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandBCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:470</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_aed6da31b1c9467654006afb9154ef4ca"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#aed6da31b1c9467654006afb9154ef4ca">cutlass::layout::VoltaTensorOpMultiplicandCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:72</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_af4efe777f1d9ec8c62d870e3ced85114"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#af4efe777f1d9ec8c62d870e3ced85114">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandCongruous packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:254</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_abf01787fadeda14d212bf99efe40e32e"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#abf01787fadeda14d212bf99efe40e32e">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:796</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_a064fd090cbaa1e359bc44e6edcbf7cde"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a064fd090cbaa1e359bc44e6edcbf7cde">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:741</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_a2670d9258bf26606a2893178060c4045"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a2670d9258bf26606a2893178060c4045">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:719</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a757ed1e942e9818559b7c0ad765414b7"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a757ed1e942e9818559b7c0ad765414b7">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:611</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_ac0c8aea5f38628ce078dd1562e44ff06"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#ac0c8aea5f38628ce078dd1562e44ff06">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandCrosswise packed(TensorCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:905</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_a2b15613a7fb66206c502077137820572"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a2b15613a7fb66206c502077137820572">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:835</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_a9f2ab08d3706874cf69ee0a53e6353b4"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a9f2ab08d3706874cf69ee0a53e6353b4">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:274</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a8262341cde0bddd17a92e0077fbd6e56"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a8262341cde0bddd17a92e0077fbd6e56">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:566</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html">cutlass::layout::VoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:733</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_acefc319105f8150e6005d2a1a7ce9366"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#acefc319105f8150e6005d2a1a7ce9366">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::RowMajorVoltaTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:348</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord&lt; kStrideRank, Index, LongIndex &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:943</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_ab58f0d2e8bbfe703e850eb0b35c8b0a1"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#ab58f0d2e8bbfe703e850eb0b35c8b0a1">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::TileShape</a></div><div class="ttdeci">typename Base::TileShape TileShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:556</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_a80908838bd1135f03e37d342a140bc34"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a80908838bd1135f03e37d342a140bc34">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:639</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_a2b898fb4943d32164d1537e31241d262"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#a2b898fb4943d32164d1537e31241d262">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:376</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_a61455b7fac8d838b05b8d0c3cf6b5b12"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a61455b7fac8d838b05b8d0c3cf6b5b12">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:744</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a80aa98e0c1d3ee7f7d5bb92fc0b2efac"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a80aa98e0c1d3ee7f7d5bb92fc0b2efac">cutlass::layout::VoltaTensorOpMultiplicandCongruous::kStrideRank</a></div><div class="ttdeci">static int const kStrideRank</div><div class="ttdoc">Rank of stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:66</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise_html_aea4095c067a1f92f8d65c9f3372de90e"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCrosswise.html#aea4095c067a1f92f8d65c9f3372de90e">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCrosswise::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:954</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous_html_a80099cf16729a8b553677fc70bea81bc"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandBCongruous.html#a80099cf16729a8b553677fc70bea81bc">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous::ColumnMajorVoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandBCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:587</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_a9019c397159688cd2bafe55967ee7f36"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a9019c397159688cd2bafe55967ee7f36">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::ColumnMajorVoltaTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:246</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_ace186f69fd389edff3909fe39598e93d"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#ace186f69fd389edff3909fe39598e93d">cutlass::layout::VoltaTensorOpMultiplicandCongruous::VoltaTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:126</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_af9149c2f7914f62232dcb3bd8f46384d"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#af9149c2f7914f62232dcb3bd8f46384d">cutlass::layout::VoltaTensorOpMultiplicandCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:182</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_a6c5a1858ade078036ed6659e3b3cce62"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a6c5a1858ade078036ed6659e3b3cce62">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::AccessCount</a></div><div class="ttdeci">typename Base::AccessCount AccessCount</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:229</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_aa68d825db94190611ce9bd54e25b0d48"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#aa68d825db94190611ce9bd54e25b0d48">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::VoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandCrosswise(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:781</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a1e3a11d053aed1428ffaf4893cdc4715"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a1e3a11d053aed1428ffaf4893cdc4715">cutlass::layout::VoltaTensorOpMultiplicandCongruous::kAccessSize</a></div><div class="ttdeci">static int const kAccessSize</div><div class="ttdoc">This layout is optimized for 128b accesses. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:85</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a64590ba904ff295c497d259813401029"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a64590ba904ff295c497d259813401029">cutlass::layout::VoltaTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:176</div></div>
<div class="ttc" id="pitch__linear_8h_html"><div class="ttname"><a href="pitch__linear_8h.html">pitch_linear.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes for pitch-linear memory. </div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_a3339e229b6a98b36c12fbe078c000253"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#a3339e229b6a98b36c12fbe078c000253">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:713</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_ac31322c93fd5973b3652c8127c0b8f3a"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#ac31322c93fd5973b3652c8127c0b8f3a">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::VoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE VoltaTensorOpMultiplicandBCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:462</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_a7f6ef66932fda16f2784dd2a1e224ca5"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a7f6ef66932fda16f2784dd2a1e224ca5">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:856</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous</a></div><div class="ttdoc">Template mapping a column-major view of pitch-linear memory to VoltaTensorOpMultiplicandCongruous. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:191</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_ae616638f7e2dc315865b3693f71f52cd"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#ae616638f7e2dc315865b3693f71f52cd">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;extent) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:286</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_abd1a744d04f46d408e2bdce12660ccf2"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#abd1a744d04f46d408e2bdce12660ccf2">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:700</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous_html_af3c9f1bd2d159857671da73de894d6c9"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandCongruous.html#af3c9f1bd2d159857671da73de894d6c9">cutlass::layout::RowMajorVoltaTensorOpMultiplicandCongruous::PartitionShape</a></div><div class="ttdeci">typename Base::PartitionShape PartitionShape</div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:322</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_a08840aa86668d1a359db6472884fcf2a"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a08840aa86668d1a359db6472884fcf2a">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:280</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise_html_a7c839af8ec4dda7a6114d49daec52728"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCrosswise.html#a7c839af8ec4dda7a6114d49daec52728">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise::ColumnMajorVoltaTensorOpMultiplicandCrosswise</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandCrosswise(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:901</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous_html_a635537ca830a3f621ed99ab323b1e7f1"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandBCongruous.html#a635537ca830a3f621ed99ab323b1e7f1">cutlass::layout::VoltaTensorOpMultiplicandBCongruous::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:408</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">cutlass::layout::VoltaTensorOpMultiplicandCongruous</a></div><div class="ttdoc">Template based on element size (in bits) - defined in terms of pitch-linear memory. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:60</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html_aa828f8dbee3903754b56759c1e6a6043"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">cutlass::layout::PitchLinearCoord::strided</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; strided() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:97</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise_html_a8f6d1bd78917c94cbea9b8f3f51ff35b"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCrosswise.html#a8f6d1bd78917c94cbea9b8f3f51ff35b">cutlass::layout::VoltaTensorOpMultiplicandCrosswise::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:831</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous_html_a963c8058ab8ef1a5d21403bd1dc27277"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorVoltaTensorOpMultiplicandCongruous.html#a963c8058ab8ef1a5d21403bd1dc27277">cutlass::layout::ColumnMajorVoltaTensorOpMultiplicandCongruous::ColumnMajorVoltaTensorOpMultiplicandCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ColumnMajorVoltaTensorOpMultiplicandCongruous(Stride stride)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:250</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous_html_a7054268bd8313c4e3b55b0b27d662f8f"><div class="ttname"><a href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html#a7054268bd8313c4e3b55b0b27d662f8f">cutlass::layout::VoltaTensorOpMultiplicandCongruous::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(TensorCoord const &amp;coord) const </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:141</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous_html_aad4ca016701d73fee071bf10c35219ab"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorVoltaTensorOpMultiplicandBCongruous.html#aad4ca016701d73fee071bf10c35219ab">cutlass::layout::RowMajorVoltaTensorOpMultiplicandBCongruous::RowMajorVoltaTensorOpMultiplicandBCongruous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE RowMajorVoltaTensorOpMultiplicandBCongruous(Index ldm=0)</div><div class="ttdoc">Ctor. </div><div class="ttdef"><b>Definition:</b> tensor_op_multiplicand_sm70.h:685</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
