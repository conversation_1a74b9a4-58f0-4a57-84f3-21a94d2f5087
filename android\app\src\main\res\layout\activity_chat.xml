<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />

    <!-- Chat History -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <ScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp">

            <TextView
                android:id="@+id/chatHistory"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="14sp"
                android:textColor="@android:color/black"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="4dp" />

        </ScrollView>

    </androidx.cardview.widget.CardView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        style="?android:attr/progressBarStyleHorizontal"
        android:indeterminate="true" />

    <!-- Input Area -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="8dp">

        <!-- Message Input -->
        <EditText
            android:id="@+id/messageInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:hint="Type your message here..."
            android:background="@drawable/edit_text_background"
            android:padding="12dp"
            android:textSize="16sp"
            android:maxLines="4"
            android:scrollbars="vertical"
            android:layout_marginBottom="8dp" />

        <!-- Button Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/clearButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="Clear"
                android:textSize="14sp"
                android:layout_marginEnd="8dp"
                android:backgroundTint="@android:color/darker_gray" />

            <Button
                android:id="@+id/sendButton"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="2"
                android:text="Send Message"
                android:textSize="16sp"
                android:backgroundTint="@color/colorPrimary" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
