dist: trusty
sudo: false
group: travis_latest
language: c++

env:
  global:
  - COMPILER=g++

addons:
  apt:
    sources: &apt_sources
      - ubuntu-toolchain-r-test
      - llvm-toolchain-precise-3.5
      - llvm-toolchain-precise-3.6
      - llvm-toolchain-precise-3.7
      - llvm-toolchain-precise-3.8
      - llvm-toolchain-trusty-3.9
      - llvm-toolchain-trusty-4.0
      - llvm-toolchain-trusty-5.0

compiler: clang
os: linux

matrix:
  fast_finish: true
  include:
    - env: COMPILER=g++-5
      compiler: gcc
      addons: &gcc5
        apt:
          packages: ["g++-5", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=g++-6
      compiler: gcc
      addons: &gcc6
        apt:
          packages: ["g++-6", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=g++-7
      compiler: gcc
      addons: &gcc7
        apt:
          packages: ["g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=clang++-3.5
      compiler: clang
      addons: &clang35
        apt:
          packages: ["clang-3.5", "g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=clang++-3.6
      compiler: clang
      addons: &clang36
        apt:
          packages: ["clang-3.6", "g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=clang++-3.7
      compiler: clang
      addons: &clang37
        apt:
          packages: ["clang-3.7", "g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=clang++-3.8
      compiler: clang
      addons: &clang38
        apt:
          packages: ["clang-3.8", "g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=clang++-3.9
      compiler: clang
      addons: &clang39
        apt:
          packages: ["clang-3.9", "g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=clang++-4.0
      compiler: clang
      addons: &clang40
        apt:
          packages: ["clang-4.0", "g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

    - env: COMPILER=clang++-5.0
      compiler: clang
      addons: &clang50
        apt:
          packages: ["clang-5.0", "g++-7", "python3-pip", "lcov"]
          sources: *apt_sources

  allow_failures:
    - env: COMPILER=clang++-3.7
      compiler: clang

    - env: COMPILER=clang++-3.9
      compiler: clang


install:
  - wget https://github.com/ninja-build/ninja/releases/download/v1.8.2/ninja-linux.zip && unzip -q ninja-linux.zip -d build-ninja
  - wget https://github.com/danmar/cppcheck/releases/download/1.81/cppcheck-1.81.zip && unzip -q cppcheck-1.81.zip
  - cd cppcheck-1.81/ && make SRCDIR=build CFGDIR=cfg CXXFLAGS="-O1 -DNDEBUG" -j2 && cd ..
  - pip3 install --user meson
  - pip3 install --user conan
  - conan remote add bincrafters https://api.bintray.com/conan/bincrafters/public-conan
  - export PATH="`pwd`/build-ninja:${PATH}"
  - export PATH="`pwd`/cppcheck-1.81/:${PATH}"
  - export PATH="~/.local/bin:${PATH}"

before_script:
  - export CXX=$COMPILER
  - mkdir debug && cd debug
  - conan install ..
  - conan build ..
  - meson configure -Dbuildtype=debug
  - meson configure -Dwarning_level=3
  - if [[ "${COMPILER}" == clang++* ]]; then meson configure -Db_sanitize="address,undefined"; fi;
  - meson configure -Db_coverage=true
  - ninja
  - cd ..
  - mkdir release-sanitize && cd release-sanitize
  - conan install ..
  - conan build ..
  - meson configure -Dbuildtype=release
  - meson configure -Dwarning_level=3
  - if [[ "${COMPILER}" == clang* ]]; then meson configure -Db_sanitize="address,undefined"; fi;
  - ninja
  - cd ..
  - mkdir release && cd release
  - conan install ..
  - conan build ..
  - meson configure -Dbuildtype=release
  - meson configure -Dwarning_level=3
  - ninja
  - cd ..

script:
  - cd release && ./test/mainTest && ./test/colorTest && ./test/envTermMissing
  - ninja cppcheck && cd ..

after_success:
  - cd debug && ./test/mainTest && ./test/colorTest && ./test/envTermMissing
  - bash <(curl -s https://codecov.io/bash)
  - cd .. && cd release-sanitize && ./test/mainTest && ./test/colorTest && ./test/envTermMissing

notifications:
  email: false
