/*
Copyright <PERSON> Rivera 2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#ifndef MSGPACK_PREDEF_OS_ANDROID_H
#define MSGPACK_PREDEF_OS_ANDROID_H

#include <msgpack/predef/version_number.h>
#include <msgpack/predef/make.h>

/*`
[heading `MSGPACK_OS_ANDROID`]

[@http://en.wikipedia.org/wiki/Android_%28operating_system%29 Android] operating system.

[table
    [[__predef_symbol__] [__predef_version__]]

    [[`__ANDROID__`] [__predef_detection__]]
    ]
 */

#define MSGPACK_OS_ANDROID MSGPACK_VERSION_NUMBER_NOT_AVAILABLE

#if !defined(MSGPACK_PREDEF_DETAIL_OS_DETECTED) && ( \
    defined(__ANDROID__) \
    )
#   undef MS<PERSON><PERSON><PERSON>_OS_ANDROID
#   define MSGPACK_OS_ANDROID MSGPACK_VERSION_NUMBER_AVAILABLE
#endif

#if MSGPAC<PERSON>_OS_ANDROID
#   define MSGPACK_OS_ANDROID_AVAILABLE
#   include <msgpack/predef/detail/os_detected.h>
#endif

#define MSGPACK_OS_ANDROID_NAME "Android"

#endif

#include <msgpack/predef/detail/test.h>
MSGPACK_PREDEF_DECLARE_TEST(MSGPACK_OS_ANDROID,MSGPACK_OS_ANDROID_NAME)
