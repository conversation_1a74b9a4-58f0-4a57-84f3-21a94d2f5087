# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002-2011.                             *
#  *     (C) Copyright <PERSON> 2011.                                    *
#  *     Distributed under the Boost Software License, Version 1.0. (See      *
#  *     accompanying file LICENSE_1_0.txt or copy at                         *
#  *     http://www.boost.org/LICENSE_1_0.txt)                                *
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_FACILITIES_HPP
# define MSGPACK_PREPROCESSOR_FACILITIES_HPP
#
# include <msgpack/preprocessor/facilities/apply.hpp>
# include <msgpack/preprocessor/facilities/empty.hpp>
# include <msgpack/preprocessor/facilities/expand.hpp>
# include <msgpack/preprocessor/facilities/identity.hpp>
# include <msgpack/preprocessor/facilities/intercept.hpp>
# include <msgpack/preprocessor/facilities/overload.hpp>
#
# endif
