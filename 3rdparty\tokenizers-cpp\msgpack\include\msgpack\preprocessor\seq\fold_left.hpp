# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_SEQ_FOLD_LEFT_HPP
# define MSGPACK_PREPROCESSOR_SEQ_FOLD_LEFT_HPP
#
# include <msgpack/preprocessor/arithmetic/dec.hpp>
# include <msgpack/preprocessor/cat.hpp>
# include <msgpack/preprocessor/control/if.hpp>
# include <msgpack/preprocessor/debug/error.hpp>
# include <msgpack/preprocessor/detail/auto_rec.hpp>
# include <msgpack/preprocessor/seq/seq.hpp>
# include <msgpack/preprocessor/seq/size.hpp>
#
# /* MSGPACK_PP_SEQ_FOLD_LEFT */
#
# if 0
#    define MSGPACK_PP_SEQ_FOLD_LEFT(op, state, seq) ...
# endif
#
# define MSGPACK_PP_SEQ_FOLD_LEFT MSGPACK_PP_CAT(MSGPACK_PP_SEQ_FOLD_LEFT_, MSGPACK_PP_AUTO_REC(MSGPACK_PP_SEQ_FOLD_LEFT_P, 256))
# define MSGPACK_PP_SEQ_FOLD_LEFT_P(n) MSGPACK_PP_CAT(MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_, MSGPACK_PP_SEQ_FOLD_LEFT_I_ ## n(MSGPACK_PP_SEQ_FOLD_LEFT_O, MSGPACK_PP_NIL, (nil), 1))
# define MSGPACK_PP_SEQ_FOLD_LEFT_O(s, st, _) st
#
# define MSGPACK_PP_SEQ_FOLD_LEFT_257(op, st, ss) MSGPACK_PP_ERROR(0x0005)
# define MSGPACK_PP_SEQ_FOLD_LEFT_I_257(op, st, ss, sz) MSGPACK_PP_ERROR(0x0005)
#
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_NIL 1
#
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, sz) 0
# define MSGPACK_PP_SEQ_FOLD_LEFT_CHECK_MSGPACK_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, sz) 0
#
# define MSGPACK_PP_SEQ_FOLD_LEFT_F(op, st, ss, sz) st
#
# define MSGPACK_PP_SEQ_FOLD_LEFT_1(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_2(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_3(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_4(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_5(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_6(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_7(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_8(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_9(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_10(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_11(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_12(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_13(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_14(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_15(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_16(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_17(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_18(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_19(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_20(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_21(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_22(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_23(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_24(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_25(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_26(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_27(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_28(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_29(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_30(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_31(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_32(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_33(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_34(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_35(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_36(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_37(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_38(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_39(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_40(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_41(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_42(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_43(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_44(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_45(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_46(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_47(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_48(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_49(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_50(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_51(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_52(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_53(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_54(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_55(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_56(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_57(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_58(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_59(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_60(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_61(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_62(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_63(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_64(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_65(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_66(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_67(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_68(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_69(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_70(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_71(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_72(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_73(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_74(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_75(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_76(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_77(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_78(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_79(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_80(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_81(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_82(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_83(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_84(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_85(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_86(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_87(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_88(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_89(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_90(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_91(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_92(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_93(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_94(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_95(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_96(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_97(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_98(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_99(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_100(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_101(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_102(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_103(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_104(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_105(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_106(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_107(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_108(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_109(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_110(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_111(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_112(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_113(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_114(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_115(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_116(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_117(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_118(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_119(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_120(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_121(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_122(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_123(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_124(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_125(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_126(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_127(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_128(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_129(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_130(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_131(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_132(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_133(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_134(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_135(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_136(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_137(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_138(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_139(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_140(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_141(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_142(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_143(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_144(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_145(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_146(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_147(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_148(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_149(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_150(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_151(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_152(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_153(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_154(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_155(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_156(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_157(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_158(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_159(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_160(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_161(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_162(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_163(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_164(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_165(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_166(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_167(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_168(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_169(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_170(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_171(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_172(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_173(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_174(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_175(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_176(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_177(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_178(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_179(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_180(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_181(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_182(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_183(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_184(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_185(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_186(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_187(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_188(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_189(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_190(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_191(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_192(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_193(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_194(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_195(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_196(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_197(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_198(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_199(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_200(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_201(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_202(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_203(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_204(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_205(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_206(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_207(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_208(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_209(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_210(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_211(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_212(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_213(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_214(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_215(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_216(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_217(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_218(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_219(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_220(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_221(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_222(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_223(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_224(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_225(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_226(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_227(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_228(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_229(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_230(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_231(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_232(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_233(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_234(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_235(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_236(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_237(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_238(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_239(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_240(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_241(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_242(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_243(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_244(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_245(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_246(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_247(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_248(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_249(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_250(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_251(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_252(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_253(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_254(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_255(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
# define MSGPACK_PP_SEQ_FOLD_LEFT_256(op, st, ss) MSGPACK_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, MSGPACK_PP_SEQ_SIZE(ss))
#
# if ~MSGPACK_PP_CONFIG_FLAGS() & MSGPACK_PP_CONFIG_DMC()
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_2, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(2, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_3, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(3, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_4, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(4, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_5, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(5, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_6, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(6, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_7, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(7, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_8, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(8, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_9, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(9, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_10, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(10, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_11, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(11, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_12, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(12, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_13, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(13, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_14, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(14, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_15, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(15, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_16, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(16, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_17, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(17, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_18, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(18, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_19, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(19, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_20, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(20, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_21, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(21, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_22, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(22, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_23, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(23, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_24, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(24, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_25, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(25, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_26, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(26, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_27, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(27, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_28, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(28, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_29, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(29, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_30, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(30, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_31, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(31, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_32, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(32, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_33, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(33, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_34, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(34, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_35, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(35, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_36, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(36, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_37, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(37, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_38, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(38, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_39, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(39, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_40, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(40, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_41, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(41, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_42, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(42, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_43, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(43, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_44, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(44, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_45, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(45, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_46, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(46, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_47, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(47, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_48, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(48, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_49, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(49, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_50, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(50, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_51, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(51, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_52, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(52, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_53, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(53, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_54, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(54, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_55, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(55, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_56, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(56, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_57, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(57, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_58, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(58, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_59, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(59, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_60, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(60, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_61, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(61, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_62, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(62, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_63, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(63, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_64, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(64, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_65, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(65, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_66, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(66, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_67, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(67, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_68, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(68, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_69, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(69, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_70, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(70, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_71, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(71, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_72, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(72, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_73, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(73, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_74, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(74, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_75, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(75, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_76, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(76, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_77, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(77, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_78, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(78, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_79, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(79, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_80, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(80, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_81, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(81, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_82, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(82, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_83, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(83, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_84, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(84, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_85, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(85, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_86, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(86, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_87, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(87, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_88, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(88, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_89, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(89, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_90, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(90, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_91, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(91, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_92, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(92, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_93, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(93, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_94, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(94, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_95, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(95, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_96, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(96, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_97, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(97, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_98, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(98, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_99, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(99, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_100, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(100, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_101, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(101, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_102, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(102, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_103, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(103, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_104, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(104, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_105, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(105, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_106, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(106, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_107, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(107, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_108, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(108, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_109, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(109, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_110, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(110, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_111, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(111, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_112, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(112, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_113, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(113, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_114, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(114, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_115, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(115, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_116, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(116, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_117, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(117, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_118, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(118, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_119, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(119, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_120, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(120, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_121, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(121, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_122, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(122, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_123, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(123, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_124, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(124, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_125, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(125, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_126, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(126, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_127, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(127, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_128, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(128, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_129, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(129, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_130, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(130, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_131, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(131, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_132, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(132, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_133, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(133, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_134, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(134, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_135, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(135, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_136, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(136, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_137, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(137, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_138, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(138, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_139, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(139, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_140, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(140, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_141, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(141, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_142, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(142, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_143, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(143, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_144, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(144, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_145, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(145, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_146, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(146, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_147, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(147, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_148, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(148, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_149, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(149, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_150, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(150, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_151, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(151, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_152, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(152, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_153, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(153, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_154, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(154, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_155, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(155, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_156, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(156, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_157, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(157, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_158, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(158, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_159, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(159, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_160, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(160, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_161, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(161, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_162, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(162, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_163, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(163, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_164, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(164, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_165, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(165, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_166, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(166, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_167, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(167, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_168, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(168, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_169, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(169, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_170, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(170, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_171, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(171, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_172, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(172, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_173, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(173, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_174, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(174, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_175, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(175, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_176, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(176, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_177, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(177, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_178, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(178, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_179, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(179, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_180, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(180, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_181, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(181, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_182, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(182, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_183, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(183, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_184, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(184, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_185, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(185, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_186, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(186, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_187, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(187, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_188, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(188, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_189, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(189, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_190, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(190, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_191, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(191, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_192, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(192, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_193, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(193, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_194, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(194, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_195, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(195, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_196, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(196, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_197, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(197, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_198, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(198, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_199, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(199, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_200, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(200, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_201, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(201, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_202, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(202, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_203, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(203, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_204, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(204, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_205, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(205, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_206, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(206, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_207, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(207, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_208, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(208, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_209, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(209, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_210, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(210, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_211, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(211, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_212, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(212, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_213, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(213, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_214, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(214, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_215, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(215, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_216, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(216, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_217, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(217, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_218, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(218, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_219, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(219, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_220, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(220, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_221, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(221, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_222, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(222, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_223, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(223, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_224, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(224, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_225, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(225, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_226, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(226, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_227, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(227, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_228, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(228, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_229, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(229, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_230, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(230, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_231, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(231, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_232, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(232, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_233, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(233, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_234, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(234, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_235, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(235, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_236, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(236, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_237, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(237, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_238, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(238, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_239, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(239, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_240, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(240, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_241, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(241, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_242, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(242, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_243, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(243, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_244, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(244, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_245, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(245, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_246, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(246, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_247, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(247, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_248, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(248, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_249, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(249, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_250, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(250, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_251, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(251, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_252, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(252, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_253, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(253, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_254, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(254, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_255, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(255, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_256, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(256, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_257, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op(257, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
# else
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_2, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(2, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_3, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(3, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_4, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(4, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_5, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(5, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_6, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(6, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_7, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(7, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_8, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(8, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_9, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(9, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_10, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(10, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_11, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(11, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_12, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(12, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_13, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(13, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_14, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(14, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_15, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(15, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_16, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(16, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_17, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(17, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_18, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(18, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_19, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(19, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_20, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(20, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_21, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(21, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_22, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(22, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_23, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(23, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_24, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(24, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_25, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(25, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_26, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(26, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_27, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(27, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_28, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(28, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_29, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(29, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_30, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(30, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_31, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(31, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_32, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(32, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_33, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(33, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_34, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(34, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_35, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(35, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_36, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(36, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_37, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(37, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_38, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(38, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_39, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(39, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_40, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(40, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_41, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(41, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_42, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(42, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_43, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(43, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_44, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(44, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_45, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(45, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_46, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(46, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_47, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(47, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_48, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(48, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_49, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(49, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_50, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(50, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_51, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(51, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_52, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(52, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_53, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(53, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_54, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(54, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_55, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(55, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_56, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(56, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_57, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(57, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_58, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(58, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_59, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(59, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_60, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(60, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_61, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(61, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_62, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(62, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_63, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(63, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_64, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(64, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_65, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(65, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_66, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(66, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_67, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(67, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_68, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(68, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_69, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(69, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_70, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(70, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_71, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(71, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_72, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(72, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_73, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(73, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_74, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(74, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_75, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(75, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_76, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(76, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_77, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(77, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_78, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(78, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_79, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(79, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_80, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(80, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_81, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(81, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_82, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(82, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_83, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(83, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_84, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(84, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_85, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(85, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_86, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(86, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_87, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(87, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_88, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(88, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_89, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(89, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_90, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(90, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_91, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(91, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_92, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(92, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_93, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(93, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_94, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(94, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_95, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(95, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_96, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(96, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_97, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(97, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_98, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(98, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_99, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(99, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_100, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(100, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_101, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(101, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_102, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(102, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_103, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(103, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_104, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(104, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_105, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(105, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_106, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(106, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_107, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(107, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_108, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(108, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_109, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(109, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_110, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(110, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_111, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(111, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_112, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(112, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_113, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(113, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_114, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(114, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_115, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(115, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_116, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(116, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_117, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(117, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_118, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(118, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_119, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(119, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_120, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(120, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_121, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(121, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_122, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(122, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_123, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(123, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_124, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(124, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_125, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(125, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_126, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(126, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_127, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(127, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_128, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(128, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_129, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(129, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_130, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(130, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_131, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(131, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_132, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(132, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_133, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(133, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_134, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(134, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_135, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(135, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_136, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(136, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_137, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(137, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_138, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(138, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_139, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(139, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_140, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(140, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_141, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(141, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_142, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(142, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_143, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(143, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_144, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(144, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_145, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(145, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_146, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(146, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_147, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(147, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_148, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(148, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_149, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(149, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_150, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(150, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_151, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(151, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_152, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(152, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_153, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(153, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_154, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(154, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_155, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(155, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_156, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(156, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_157, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(157, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_158, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(158, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_159, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(159, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_160, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(160, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_161, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(161, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_162, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(162, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_163, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(163, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_164, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(164, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_165, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(165, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_166, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(166, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_167, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(167, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_168, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(168, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_169, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(169, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_170, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(170, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_171, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(171, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_172, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(172, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_173, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(173, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_174, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(174, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_175, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(175, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_176, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(176, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_177, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(177, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_178, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(178, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_179, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(179, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_180, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(180, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_181, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(181, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_182, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(182, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_183, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(183, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_184, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(184, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_185, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(185, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_186, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(186, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_187, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(187, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_188, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(188, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_189, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(189, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_190, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(190, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_191, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(191, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_192, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(192, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_193, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(193, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_194, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(194, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_195, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(195, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_196, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(196, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_197, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(197, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_198, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(198, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_199, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(199, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_200, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(200, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_201, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(201, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_202, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(202, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_203, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(203, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_204, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(204, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_205, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(205, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_206, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(206, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_207, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(207, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_208, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(208, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_209, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(209, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_210, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(210, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_211, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(211, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_212, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(212, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_213, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(213, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_214, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(214, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_215, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(215, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_216, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(216, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_217, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(217, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_218, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(218, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_219, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(219, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_220, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(220, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_221, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(221, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_222, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(222, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_223, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(223, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_224, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(224, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_225, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(225, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_226, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(226, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_227, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(227, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_228, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(228, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_229, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(229, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_230, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(230, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_231, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(231, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_232, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(232, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_233, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(233, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_234, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(234, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_235, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(235, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_236, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(236, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_237, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(237, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_238, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(238, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_239, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(239, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_240, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(240, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_241, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(241, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_242, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(242, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_243, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(243, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_244, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(244, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_245, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(245, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_246, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(246, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_247, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(247, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_248, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(248, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_249, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(249, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_250, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(250, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_251, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(251, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_252, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(252, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_253, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(253, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_254, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(254, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_255, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(255, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_256, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(256, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
#    define MSGPACK_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, sz) MSGPACK_PP_IF(MSGPACK_PP_DEC(sz), MSGPACK_PP_SEQ_FOLD_LEFT_I_257, MSGPACK_PP_SEQ_FOLD_LEFT_F)(op, op##(257, st, MSGPACK_PP_SEQ_HEAD(ss)), MSGPACK_PP_SEQ_TAIL(ss), MSGPACK_PP_DEC(sz))
# endif
#
# endif
