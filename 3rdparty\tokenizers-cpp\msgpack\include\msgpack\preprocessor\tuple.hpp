# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2011,2013) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_TUPLE_HPP
# define MSGPACK_PREPROCESSOR_TUPLE_HPP
#
# include <msgpack/preprocessor/tuple/eat.hpp>
# include <msgpack/preprocessor/tuple/elem.hpp>
# include <msgpack/preprocessor/tuple/enum.hpp>
# include <msgpack/preprocessor/tuple/insert.hpp>
# include <msgpack/preprocessor/tuple/pop_back.hpp>
# include <msgpack/preprocessor/tuple/pop_front.hpp>
# include <msgpack/preprocessor/tuple/push_back.hpp>
# include <msgpack/preprocessor/tuple/push_front.hpp>
# include <msgpack/preprocessor/tuple/rem.hpp>
# include <msgpack/preprocessor/tuple/remove.hpp>
# include <msgpack/preprocessor/tuple/replace.hpp>
# include <msgpack/preprocessor/tuple/reverse.hpp>
# include <msgpack/preprocessor/tuple/size.hpp>
# include <msgpack/preprocessor/tuple/to_array.hpp>
# include <msgpack/preprocessor/tuple/to_list.hpp>
# include <msgpack/preprocessor/tuple/to_seq.hpp>
#
# endif
