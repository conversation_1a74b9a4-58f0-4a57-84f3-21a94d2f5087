# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2005.                                  *
#  *     Distributed under the Boost Software License, Version 1.0. (See      *
#  *     accompanying file LICENSE_1_0.txt or copy at                         *
#  *     http://www.boost.org/LICENSE_1_0.txt)                                *
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_SLOT_COUNTER_HPP
# define MSGPACK_PREPROCESSOR_SLOT_COUNTER_HPP
#
# include <msgpack/preprocessor/slot/detail/def.hpp>
#
# /* MSGPACK_PP_COUNTER */
#
# define MSGPACK_PP_COUNTER 0
#
# /* MSGPACK_PP_UPDATE_COUNTER */
#
# define MSGPACK_PP_UPDATE_COUNTER() <msgpack/preprocessor/slot/detail/counter.hpp>
#
# endif
