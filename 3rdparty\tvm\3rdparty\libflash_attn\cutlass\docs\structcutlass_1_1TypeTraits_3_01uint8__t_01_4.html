<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::TypeTraits&lt; uint8_t &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html">TypeTraits&lt; uint8_t &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::TypeTraits&lt; uint8_t &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="type__traits_8h_source.html">type_traits.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a38489c4605f497c7d7d8b766935805ed"><td class="memItemLeft" align="right" valign="top">typedef uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a38489c4605f497c7d7d8b766935805ed">host_type</a></td></tr>
<tr class="separator:a38489c4605f497c7d7d8b766935805ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a582965751efd761e874611a3282dbe34"><td class="memItemLeft" align="right" valign="top">typedef uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a582965751efd761e874611a3282dbe34">device_type</a></td></tr>
<tr class="separator:a582965751efd761e874611a3282dbe34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac014e1dc998a6bbd4fcc30f4544ce2be"><td class="memItemLeft" align="right" valign="top">typedef uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#ac014e1dc998a6bbd4fcc30f4544ce2be">integer_type</a></td></tr>
<tr class="separator:ac014e1dc998a6bbd4fcc30f4544ce2be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ad36f02c21dbf06d3631793b524b5b7"><td class="memItemLeft" align="right" valign="top">typedef uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a3ad36f02c21dbf06d3631793b524b5b7">unsigned_type</a></td></tr>
<tr class="separator:a3ad36f02c21dbf06d3631793b524b5b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a11a6931adbdd24743a919c999f44eda9"><td class="memItemLeft" align="right" valign="top">static uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a11a6931adbdd24743a919c999f44eda9">remove_negative_zero</a> (uint8_t x)</td></tr>
<tr class="separator:a11a6931adbdd24743a919c999f44eda9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d8e72e3085df6766bbdfdc83405f3a2"><td class="memItemLeft" align="right" valign="top">static uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a1d8e72e3085df6766bbdfdc83405f3a2">to_print</a> (uint8_t x)</td></tr>
<tr class="separator:a1d8e72e3085df6766bbdfdc83405f3a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84de624a6a2a3431cd4f842994a9946d"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a582965751efd761e874611a3282dbe34">device_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a84de624a6a2a3431cd4f842994a9946d">to_device</a> (<a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a38489c4605f497c7d7d8b766935805ed">host_type</a> x)</td></tr>
<tr class="separator:a84de624a6a2a3431cd4f842994a9946d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:ae5edc866e5de8527b6ddf06c3844684b"><td class="memItemLeft" align="right" valign="top">static cudaDataType_t const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#ae5edc866e5de8527b6ddf06c3844684b">cublas_type</a> = CUDA_R_8I</td></tr>
<tr class="separator:ae5edc866e5de8527b6ddf06c3844684b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a582965751efd761e874611a3282dbe34"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint8_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a582965751efd761e874611a3282dbe34">device_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a38489c4605f497c7d7d8b766935805ed"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint8_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a38489c4605f497c7d7d8b766935805ed">host_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac014e1dc998a6bbd4fcc30f4544ce2be"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint8_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#ac014e1dc998a6bbd4fcc30f4544ce2be">integer_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3ad36f02c21dbf06d3631793b524b5b7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint8_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a3ad36f02c21dbf06d3631793b524b5b7">unsigned_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a11a6931adbdd24743a919c999f44eda9"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static uint8_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::remove_negative_zero </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a84de624a6a2a3431cd4f842994a9946d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a582965751efd761e874611a3282dbe34">device_type</a> <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::to_device </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a38489c4605f497c7d7d8b766935805ed">host_type</a>&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1d8e72e3085df6766bbdfdc83405f3a2"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static uint32_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::to_print </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="ae5edc866e5de8527b6ddf06c3844684b"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">cudaDataType_t const <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; uint8_t &gt;::cublas_type = CUDA_R_8I</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="type__traits_8h_source.html">type_traits.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
