# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_REPETITION_DETAIL_MSVC_FOR_HPP
# define MSGPACK_PREPROCESSOR_REPETITION_DETAIL_MSVC_FOR_HPP
#
# include <msgpack/preprocessor/control/if.hpp>
# include <msgpack/preprocessor/tuple/eat.hpp>
#
# define MSGPACK_PP_FOR_1(s, p, o, m) MSGPACK_PP_IF(p(2, s), m, MSGPACK_PP_TUPLE_EAT_2)(2, s) MSGPACK_PP_IF(p(2, s), MSGPACK_PP_FOR_2, MSGPACK_PP_TUPLE_EAT_4)(o(2, s), p, o, m)
# define MSGPACK_PP_FOR_2(s, p, o, m) MSGPACK_PP_IF(p(3, s), m, MSGPACK_PP_TUPLE_EAT_2)(3, s) MSGPACK_PP_IF(p(3, s), MSGPACK_PP_FOR_3, MSGPACK_PP_TUPLE_EAT_4)(o(3, s), p, o, m)
# define MSGPACK_PP_FOR_3(s, p, o, m) MSGPACK_PP_IF(p(4, s), m, MSGPACK_PP_TUPLE_EAT_2)(4, s) MSGPACK_PP_IF(p(4, s), MSGPACK_PP_FOR_4, MSGPACK_PP_TUPLE_EAT_4)(o(4, s), p, o, m)
# define MSGPACK_PP_FOR_4(s, p, o, m) MSGPACK_PP_IF(p(5, s), m, MSGPACK_PP_TUPLE_EAT_2)(5, s) MSGPACK_PP_IF(p(5, s), MSGPACK_PP_FOR_5, MSGPACK_PP_TUPLE_EAT_4)(o(5, s), p, o, m)
# define MSGPACK_PP_FOR_5(s, p, o, m) MSGPACK_PP_IF(p(6, s), m, MSGPACK_PP_TUPLE_EAT_2)(6, s) MSGPACK_PP_IF(p(6, s), MSGPACK_PP_FOR_6, MSGPACK_PP_TUPLE_EAT_4)(o(6, s), p, o, m)
# define MSGPACK_PP_FOR_6(s, p, o, m) MSGPACK_PP_IF(p(7, s), m, MSGPACK_PP_TUPLE_EAT_2)(7, s) MSGPACK_PP_IF(p(7, s), MSGPACK_PP_FOR_7, MSGPACK_PP_TUPLE_EAT_4)(o(7, s), p, o, m)
# define MSGPACK_PP_FOR_7(s, p, o, m) MSGPACK_PP_IF(p(8, s), m, MSGPACK_PP_TUPLE_EAT_2)(8, s) MSGPACK_PP_IF(p(8, s), MSGPACK_PP_FOR_8, MSGPACK_PP_TUPLE_EAT_4)(o(8, s), p, o, m)
# define MSGPACK_PP_FOR_8(s, p, o, m) MSGPACK_PP_IF(p(9, s), m, MSGPACK_PP_TUPLE_EAT_2)(9, s) MSGPACK_PP_IF(p(9, s), MSGPACK_PP_FOR_9, MSGPACK_PP_TUPLE_EAT_4)(o(9, s), p, o, m)
# define MSGPACK_PP_FOR_9(s, p, o, m) MSGPACK_PP_IF(p(10, s), m, MSGPACK_PP_TUPLE_EAT_2)(10, s) MSGPACK_PP_IF(p(10, s), MSGPACK_PP_FOR_10, MSGPACK_PP_TUPLE_EAT_4)(o(10, s), p, o, m)
# define MSGPACK_PP_FOR_10(s, p, o, m) MSGPACK_PP_IF(p(11, s), m, MSGPACK_PP_TUPLE_EAT_2)(11, s) MSGPACK_PP_IF(p(11, s), MSGPACK_PP_FOR_11, MSGPACK_PP_TUPLE_EAT_4)(o(11, s), p, o, m)
# define MSGPACK_PP_FOR_11(s, p, o, m) MSGPACK_PP_IF(p(12, s), m, MSGPACK_PP_TUPLE_EAT_2)(12, s) MSGPACK_PP_IF(p(12, s), MSGPACK_PP_FOR_12, MSGPACK_PP_TUPLE_EAT_4)(o(12, s), p, o, m)
# define MSGPACK_PP_FOR_12(s, p, o, m) MSGPACK_PP_IF(p(13, s), m, MSGPACK_PP_TUPLE_EAT_2)(13, s) MSGPACK_PP_IF(p(13, s), MSGPACK_PP_FOR_13, MSGPACK_PP_TUPLE_EAT_4)(o(13, s), p, o, m)
# define MSGPACK_PP_FOR_13(s, p, o, m) MSGPACK_PP_IF(p(14, s), m, MSGPACK_PP_TUPLE_EAT_2)(14, s) MSGPACK_PP_IF(p(14, s), MSGPACK_PP_FOR_14, MSGPACK_PP_TUPLE_EAT_4)(o(14, s), p, o, m)
# define MSGPACK_PP_FOR_14(s, p, o, m) MSGPACK_PP_IF(p(15, s), m, MSGPACK_PP_TUPLE_EAT_2)(15, s) MSGPACK_PP_IF(p(15, s), MSGPACK_PP_FOR_15, MSGPACK_PP_TUPLE_EAT_4)(o(15, s), p, o, m)
# define MSGPACK_PP_FOR_15(s, p, o, m) MSGPACK_PP_IF(p(16, s), m, MSGPACK_PP_TUPLE_EAT_2)(16, s) MSGPACK_PP_IF(p(16, s), MSGPACK_PP_FOR_16, MSGPACK_PP_TUPLE_EAT_4)(o(16, s), p, o, m)
# define MSGPACK_PP_FOR_16(s, p, o, m) MSGPACK_PP_IF(p(17, s), m, MSGPACK_PP_TUPLE_EAT_2)(17, s) MSGPACK_PP_IF(p(17, s), MSGPACK_PP_FOR_17, MSGPACK_PP_TUPLE_EAT_4)(o(17, s), p, o, m)
# define MSGPACK_PP_FOR_17(s, p, o, m) MSGPACK_PP_IF(p(18, s), m, MSGPACK_PP_TUPLE_EAT_2)(18, s) MSGPACK_PP_IF(p(18, s), MSGPACK_PP_FOR_18, MSGPACK_PP_TUPLE_EAT_4)(o(18, s), p, o, m)
# define MSGPACK_PP_FOR_18(s, p, o, m) MSGPACK_PP_IF(p(19, s), m, MSGPACK_PP_TUPLE_EAT_2)(19, s) MSGPACK_PP_IF(p(19, s), MSGPACK_PP_FOR_19, MSGPACK_PP_TUPLE_EAT_4)(o(19, s), p, o, m)
# define MSGPACK_PP_FOR_19(s, p, o, m) MSGPACK_PP_IF(p(20, s), m, MSGPACK_PP_TUPLE_EAT_2)(20, s) MSGPACK_PP_IF(p(20, s), MSGPACK_PP_FOR_20, MSGPACK_PP_TUPLE_EAT_4)(o(20, s), p, o, m)
# define MSGPACK_PP_FOR_20(s, p, o, m) MSGPACK_PP_IF(p(21, s), m, MSGPACK_PP_TUPLE_EAT_2)(21, s) MSGPACK_PP_IF(p(21, s), MSGPACK_PP_FOR_21, MSGPACK_PP_TUPLE_EAT_4)(o(21, s), p, o, m)
# define MSGPACK_PP_FOR_21(s, p, o, m) MSGPACK_PP_IF(p(22, s), m, MSGPACK_PP_TUPLE_EAT_2)(22, s) MSGPACK_PP_IF(p(22, s), MSGPACK_PP_FOR_22, MSGPACK_PP_TUPLE_EAT_4)(o(22, s), p, o, m)
# define MSGPACK_PP_FOR_22(s, p, o, m) MSGPACK_PP_IF(p(23, s), m, MSGPACK_PP_TUPLE_EAT_2)(23, s) MSGPACK_PP_IF(p(23, s), MSGPACK_PP_FOR_23, MSGPACK_PP_TUPLE_EAT_4)(o(23, s), p, o, m)
# define MSGPACK_PP_FOR_23(s, p, o, m) MSGPACK_PP_IF(p(24, s), m, MSGPACK_PP_TUPLE_EAT_2)(24, s) MSGPACK_PP_IF(p(24, s), MSGPACK_PP_FOR_24, MSGPACK_PP_TUPLE_EAT_4)(o(24, s), p, o, m)
# define MSGPACK_PP_FOR_24(s, p, o, m) MSGPACK_PP_IF(p(25, s), m, MSGPACK_PP_TUPLE_EAT_2)(25, s) MSGPACK_PP_IF(p(25, s), MSGPACK_PP_FOR_25, MSGPACK_PP_TUPLE_EAT_4)(o(25, s), p, o, m)
# define MSGPACK_PP_FOR_25(s, p, o, m) MSGPACK_PP_IF(p(26, s), m, MSGPACK_PP_TUPLE_EAT_2)(26, s) MSGPACK_PP_IF(p(26, s), MSGPACK_PP_FOR_26, MSGPACK_PP_TUPLE_EAT_4)(o(26, s), p, o, m)
# define MSGPACK_PP_FOR_26(s, p, o, m) MSGPACK_PP_IF(p(27, s), m, MSGPACK_PP_TUPLE_EAT_2)(27, s) MSGPACK_PP_IF(p(27, s), MSGPACK_PP_FOR_27, MSGPACK_PP_TUPLE_EAT_4)(o(27, s), p, o, m)
# define MSGPACK_PP_FOR_27(s, p, o, m) MSGPACK_PP_IF(p(28, s), m, MSGPACK_PP_TUPLE_EAT_2)(28, s) MSGPACK_PP_IF(p(28, s), MSGPACK_PP_FOR_28, MSGPACK_PP_TUPLE_EAT_4)(o(28, s), p, o, m)
# define MSGPACK_PP_FOR_28(s, p, o, m) MSGPACK_PP_IF(p(29, s), m, MSGPACK_PP_TUPLE_EAT_2)(29, s) MSGPACK_PP_IF(p(29, s), MSGPACK_PP_FOR_29, MSGPACK_PP_TUPLE_EAT_4)(o(29, s), p, o, m)
# define MSGPACK_PP_FOR_29(s, p, o, m) MSGPACK_PP_IF(p(30, s), m, MSGPACK_PP_TUPLE_EAT_2)(30, s) MSGPACK_PP_IF(p(30, s), MSGPACK_PP_FOR_30, MSGPACK_PP_TUPLE_EAT_4)(o(30, s), p, o, m)
# define MSGPACK_PP_FOR_30(s, p, o, m) MSGPACK_PP_IF(p(31, s), m, MSGPACK_PP_TUPLE_EAT_2)(31, s) MSGPACK_PP_IF(p(31, s), MSGPACK_PP_FOR_31, MSGPACK_PP_TUPLE_EAT_4)(o(31, s), p, o, m)
# define MSGPACK_PP_FOR_31(s, p, o, m) MSGPACK_PP_IF(p(32, s), m, MSGPACK_PP_TUPLE_EAT_2)(32, s) MSGPACK_PP_IF(p(32, s), MSGPACK_PP_FOR_32, MSGPACK_PP_TUPLE_EAT_4)(o(32, s), p, o, m)
# define MSGPACK_PP_FOR_32(s, p, o, m) MSGPACK_PP_IF(p(33, s), m, MSGPACK_PP_TUPLE_EAT_2)(33, s) MSGPACK_PP_IF(p(33, s), MSGPACK_PP_FOR_33, MSGPACK_PP_TUPLE_EAT_4)(o(33, s), p, o, m)
# define MSGPACK_PP_FOR_33(s, p, o, m) MSGPACK_PP_IF(p(34, s), m, MSGPACK_PP_TUPLE_EAT_2)(34, s) MSGPACK_PP_IF(p(34, s), MSGPACK_PP_FOR_34, MSGPACK_PP_TUPLE_EAT_4)(o(34, s), p, o, m)
# define MSGPACK_PP_FOR_34(s, p, o, m) MSGPACK_PP_IF(p(35, s), m, MSGPACK_PP_TUPLE_EAT_2)(35, s) MSGPACK_PP_IF(p(35, s), MSGPACK_PP_FOR_35, MSGPACK_PP_TUPLE_EAT_4)(o(35, s), p, o, m)
# define MSGPACK_PP_FOR_35(s, p, o, m) MSGPACK_PP_IF(p(36, s), m, MSGPACK_PP_TUPLE_EAT_2)(36, s) MSGPACK_PP_IF(p(36, s), MSGPACK_PP_FOR_36, MSGPACK_PP_TUPLE_EAT_4)(o(36, s), p, o, m)
# define MSGPACK_PP_FOR_36(s, p, o, m) MSGPACK_PP_IF(p(37, s), m, MSGPACK_PP_TUPLE_EAT_2)(37, s) MSGPACK_PP_IF(p(37, s), MSGPACK_PP_FOR_37, MSGPACK_PP_TUPLE_EAT_4)(o(37, s), p, o, m)
# define MSGPACK_PP_FOR_37(s, p, o, m) MSGPACK_PP_IF(p(38, s), m, MSGPACK_PP_TUPLE_EAT_2)(38, s) MSGPACK_PP_IF(p(38, s), MSGPACK_PP_FOR_38, MSGPACK_PP_TUPLE_EAT_4)(o(38, s), p, o, m)
# define MSGPACK_PP_FOR_38(s, p, o, m) MSGPACK_PP_IF(p(39, s), m, MSGPACK_PP_TUPLE_EAT_2)(39, s) MSGPACK_PP_IF(p(39, s), MSGPACK_PP_FOR_39, MSGPACK_PP_TUPLE_EAT_4)(o(39, s), p, o, m)
# define MSGPACK_PP_FOR_39(s, p, o, m) MSGPACK_PP_IF(p(40, s), m, MSGPACK_PP_TUPLE_EAT_2)(40, s) MSGPACK_PP_IF(p(40, s), MSGPACK_PP_FOR_40, MSGPACK_PP_TUPLE_EAT_4)(o(40, s), p, o, m)
# define MSGPACK_PP_FOR_40(s, p, o, m) MSGPACK_PP_IF(p(41, s), m, MSGPACK_PP_TUPLE_EAT_2)(41, s) MSGPACK_PP_IF(p(41, s), MSGPACK_PP_FOR_41, MSGPACK_PP_TUPLE_EAT_4)(o(41, s), p, o, m)
# define MSGPACK_PP_FOR_41(s, p, o, m) MSGPACK_PP_IF(p(42, s), m, MSGPACK_PP_TUPLE_EAT_2)(42, s) MSGPACK_PP_IF(p(42, s), MSGPACK_PP_FOR_42, MSGPACK_PP_TUPLE_EAT_4)(o(42, s), p, o, m)
# define MSGPACK_PP_FOR_42(s, p, o, m) MSGPACK_PP_IF(p(43, s), m, MSGPACK_PP_TUPLE_EAT_2)(43, s) MSGPACK_PP_IF(p(43, s), MSGPACK_PP_FOR_43, MSGPACK_PP_TUPLE_EAT_4)(o(43, s), p, o, m)
# define MSGPACK_PP_FOR_43(s, p, o, m) MSGPACK_PP_IF(p(44, s), m, MSGPACK_PP_TUPLE_EAT_2)(44, s) MSGPACK_PP_IF(p(44, s), MSGPACK_PP_FOR_44, MSGPACK_PP_TUPLE_EAT_4)(o(44, s), p, o, m)
# define MSGPACK_PP_FOR_44(s, p, o, m) MSGPACK_PP_IF(p(45, s), m, MSGPACK_PP_TUPLE_EAT_2)(45, s) MSGPACK_PP_IF(p(45, s), MSGPACK_PP_FOR_45, MSGPACK_PP_TUPLE_EAT_4)(o(45, s), p, o, m)
# define MSGPACK_PP_FOR_45(s, p, o, m) MSGPACK_PP_IF(p(46, s), m, MSGPACK_PP_TUPLE_EAT_2)(46, s) MSGPACK_PP_IF(p(46, s), MSGPACK_PP_FOR_46, MSGPACK_PP_TUPLE_EAT_4)(o(46, s), p, o, m)
# define MSGPACK_PP_FOR_46(s, p, o, m) MSGPACK_PP_IF(p(47, s), m, MSGPACK_PP_TUPLE_EAT_2)(47, s) MSGPACK_PP_IF(p(47, s), MSGPACK_PP_FOR_47, MSGPACK_PP_TUPLE_EAT_4)(o(47, s), p, o, m)
# define MSGPACK_PP_FOR_47(s, p, o, m) MSGPACK_PP_IF(p(48, s), m, MSGPACK_PP_TUPLE_EAT_2)(48, s) MSGPACK_PP_IF(p(48, s), MSGPACK_PP_FOR_48, MSGPACK_PP_TUPLE_EAT_4)(o(48, s), p, o, m)
# define MSGPACK_PP_FOR_48(s, p, o, m) MSGPACK_PP_IF(p(49, s), m, MSGPACK_PP_TUPLE_EAT_2)(49, s) MSGPACK_PP_IF(p(49, s), MSGPACK_PP_FOR_49, MSGPACK_PP_TUPLE_EAT_4)(o(49, s), p, o, m)
# define MSGPACK_PP_FOR_49(s, p, o, m) MSGPACK_PP_IF(p(50, s), m, MSGPACK_PP_TUPLE_EAT_2)(50, s) MSGPACK_PP_IF(p(50, s), MSGPACK_PP_FOR_50, MSGPACK_PP_TUPLE_EAT_4)(o(50, s), p, o, m)
# define MSGPACK_PP_FOR_50(s, p, o, m) MSGPACK_PP_IF(p(51, s), m, MSGPACK_PP_TUPLE_EAT_2)(51, s) MSGPACK_PP_IF(p(51, s), MSGPACK_PP_FOR_51, MSGPACK_PP_TUPLE_EAT_4)(o(51, s), p, o, m)
# define MSGPACK_PP_FOR_51(s, p, o, m) MSGPACK_PP_IF(p(52, s), m, MSGPACK_PP_TUPLE_EAT_2)(52, s) MSGPACK_PP_IF(p(52, s), MSGPACK_PP_FOR_52, MSGPACK_PP_TUPLE_EAT_4)(o(52, s), p, o, m)
# define MSGPACK_PP_FOR_52(s, p, o, m) MSGPACK_PP_IF(p(53, s), m, MSGPACK_PP_TUPLE_EAT_2)(53, s) MSGPACK_PP_IF(p(53, s), MSGPACK_PP_FOR_53, MSGPACK_PP_TUPLE_EAT_4)(o(53, s), p, o, m)
# define MSGPACK_PP_FOR_53(s, p, o, m) MSGPACK_PP_IF(p(54, s), m, MSGPACK_PP_TUPLE_EAT_2)(54, s) MSGPACK_PP_IF(p(54, s), MSGPACK_PP_FOR_54, MSGPACK_PP_TUPLE_EAT_4)(o(54, s), p, o, m)
# define MSGPACK_PP_FOR_54(s, p, o, m) MSGPACK_PP_IF(p(55, s), m, MSGPACK_PP_TUPLE_EAT_2)(55, s) MSGPACK_PP_IF(p(55, s), MSGPACK_PP_FOR_55, MSGPACK_PP_TUPLE_EAT_4)(o(55, s), p, o, m)
# define MSGPACK_PP_FOR_55(s, p, o, m) MSGPACK_PP_IF(p(56, s), m, MSGPACK_PP_TUPLE_EAT_2)(56, s) MSGPACK_PP_IF(p(56, s), MSGPACK_PP_FOR_56, MSGPACK_PP_TUPLE_EAT_4)(o(56, s), p, o, m)
# define MSGPACK_PP_FOR_56(s, p, o, m) MSGPACK_PP_IF(p(57, s), m, MSGPACK_PP_TUPLE_EAT_2)(57, s) MSGPACK_PP_IF(p(57, s), MSGPACK_PP_FOR_57, MSGPACK_PP_TUPLE_EAT_4)(o(57, s), p, o, m)
# define MSGPACK_PP_FOR_57(s, p, o, m) MSGPACK_PP_IF(p(58, s), m, MSGPACK_PP_TUPLE_EAT_2)(58, s) MSGPACK_PP_IF(p(58, s), MSGPACK_PP_FOR_58, MSGPACK_PP_TUPLE_EAT_4)(o(58, s), p, o, m)
# define MSGPACK_PP_FOR_58(s, p, o, m) MSGPACK_PP_IF(p(59, s), m, MSGPACK_PP_TUPLE_EAT_2)(59, s) MSGPACK_PP_IF(p(59, s), MSGPACK_PP_FOR_59, MSGPACK_PP_TUPLE_EAT_4)(o(59, s), p, o, m)
# define MSGPACK_PP_FOR_59(s, p, o, m) MSGPACK_PP_IF(p(60, s), m, MSGPACK_PP_TUPLE_EAT_2)(60, s) MSGPACK_PP_IF(p(60, s), MSGPACK_PP_FOR_60, MSGPACK_PP_TUPLE_EAT_4)(o(60, s), p, o, m)
# define MSGPACK_PP_FOR_60(s, p, o, m) MSGPACK_PP_IF(p(61, s), m, MSGPACK_PP_TUPLE_EAT_2)(61, s) MSGPACK_PP_IF(p(61, s), MSGPACK_PP_FOR_61, MSGPACK_PP_TUPLE_EAT_4)(o(61, s), p, o, m)
# define MSGPACK_PP_FOR_61(s, p, o, m) MSGPACK_PP_IF(p(62, s), m, MSGPACK_PP_TUPLE_EAT_2)(62, s) MSGPACK_PP_IF(p(62, s), MSGPACK_PP_FOR_62, MSGPACK_PP_TUPLE_EAT_4)(o(62, s), p, o, m)
# define MSGPACK_PP_FOR_62(s, p, o, m) MSGPACK_PP_IF(p(63, s), m, MSGPACK_PP_TUPLE_EAT_2)(63, s) MSGPACK_PP_IF(p(63, s), MSGPACK_PP_FOR_63, MSGPACK_PP_TUPLE_EAT_4)(o(63, s), p, o, m)
# define MSGPACK_PP_FOR_63(s, p, o, m) MSGPACK_PP_IF(p(64, s), m, MSGPACK_PP_TUPLE_EAT_2)(64, s) MSGPACK_PP_IF(p(64, s), MSGPACK_PP_FOR_64, MSGPACK_PP_TUPLE_EAT_4)(o(64, s), p, o, m)
# define MSGPACK_PP_FOR_64(s, p, o, m) MSGPACK_PP_IF(p(65, s), m, MSGPACK_PP_TUPLE_EAT_2)(65, s) MSGPACK_PP_IF(p(65, s), MSGPACK_PP_FOR_65, MSGPACK_PP_TUPLE_EAT_4)(o(65, s), p, o, m)
# define MSGPACK_PP_FOR_65(s, p, o, m) MSGPACK_PP_IF(p(66, s), m, MSGPACK_PP_TUPLE_EAT_2)(66, s) MSGPACK_PP_IF(p(66, s), MSGPACK_PP_FOR_66, MSGPACK_PP_TUPLE_EAT_4)(o(66, s), p, o, m)
# define MSGPACK_PP_FOR_66(s, p, o, m) MSGPACK_PP_IF(p(67, s), m, MSGPACK_PP_TUPLE_EAT_2)(67, s) MSGPACK_PP_IF(p(67, s), MSGPACK_PP_FOR_67, MSGPACK_PP_TUPLE_EAT_4)(o(67, s), p, o, m)
# define MSGPACK_PP_FOR_67(s, p, o, m) MSGPACK_PP_IF(p(68, s), m, MSGPACK_PP_TUPLE_EAT_2)(68, s) MSGPACK_PP_IF(p(68, s), MSGPACK_PP_FOR_68, MSGPACK_PP_TUPLE_EAT_4)(o(68, s), p, o, m)
# define MSGPACK_PP_FOR_68(s, p, o, m) MSGPACK_PP_IF(p(69, s), m, MSGPACK_PP_TUPLE_EAT_2)(69, s) MSGPACK_PP_IF(p(69, s), MSGPACK_PP_FOR_69, MSGPACK_PP_TUPLE_EAT_4)(o(69, s), p, o, m)
# define MSGPACK_PP_FOR_69(s, p, o, m) MSGPACK_PP_IF(p(70, s), m, MSGPACK_PP_TUPLE_EAT_2)(70, s) MSGPACK_PP_IF(p(70, s), MSGPACK_PP_FOR_70, MSGPACK_PP_TUPLE_EAT_4)(o(70, s), p, o, m)
# define MSGPACK_PP_FOR_70(s, p, o, m) MSGPACK_PP_IF(p(71, s), m, MSGPACK_PP_TUPLE_EAT_2)(71, s) MSGPACK_PP_IF(p(71, s), MSGPACK_PP_FOR_71, MSGPACK_PP_TUPLE_EAT_4)(o(71, s), p, o, m)
# define MSGPACK_PP_FOR_71(s, p, o, m) MSGPACK_PP_IF(p(72, s), m, MSGPACK_PP_TUPLE_EAT_2)(72, s) MSGPACK_PP_IF(p(72, s), MSGPACK_PP_FOR_72, MSGPACK_PP_TUPLE_EAT_4)(o(72, s), p, o, m)
# define MSGPACK_PP_FOR_72(s, p, o, m) MSGPACK_PP_IF(p(73, s), m, MSGPACK_PP_TUPLE_EAT_2)(73, s) MSGPACK_PP_IF(p(73, s), MSGPACK_PP_FOR_73, MSGPACK_PP_TUPLE_EAT_4)(o(73, s), p, o, m)
# define MSGPACK_PP_FOR_73(s, p, o, m) MSGPACK_PP_IF(p(74, s), m, MSGPACK_PP_TUPLE_EAT_2)(74, s) MSGPACK_PP_IF(p(74, s), MSGPACK_PP_FOR_74, MSGPACK_PP_TUPLE_EAT_4)(o(74, s), p, o, m)
# define MSGPACK_PP_FOR_74(s, p, o, m) MSGPACK_PP_IF(p(75, s), m, MSGPACK_PP_TUPLE_EAT_2)(75, s) MSGPACK_PP_IF(p(75, s), MSGPACK_PP_FOR_75, MSGPACK_PP_TUPLE_EAT_4)(o(75, s), p, o, m)
# define MSGPACK_PP_FOR_75(s, p, o, m) MSGPACK_PP_IF(p(76, s), m, MSGPACK_PP_TUPLE_EAT_2)(76, s) MSGPACK_PP_IF(p(76, s), MSGPACK_PP_FOR_76, MSGPACK_PP_TUPLE_EAT_4)(o(76, s), p, o, m)
# define MSGPACK_PP_FOR_76(s, p, o, m) MSGPACK_PP_IF(p(77, s), m, MSGPACK_PP_TUPLE_EAT_2)(77, s) MSGPACK_PP_IF(p(77, s), MSGPACK_PP_FOR_77, MSGPACK_PP_TUPLE_EAT_4)(o(77, s), p, o, m)
# define MSGPACK_PP_FOR_77(s, p, o, m) MSGPACK_PP_IF(p(78, s), m, MSGPACK_PP_TUPLE_EAT_2)(78, s) MSGPACK_PP_IF(p(78, s), MSGPACK_PP_FOR_78, MSGPACK_PP_TUPLE_EAT_4)(o(78, s), p, o, m)
# define MSGPACK_PP_FOR_78(s, p, o, m) MSGPACK_PP_IF(p(79, s), m, MSGPACK_PP_TUPLE_EAT_2)(79, s) MSGPACK_PP_IF(p(79, s), MSGPACK_PP_FOR_79, MSGPACK_PP_TUPLE_EAT_4)(o(79, s), p, o, m)
# define MSGPACK_PP_FOR_79(s, p, o, m) MSGPACK_PP_IF(p(80, s), m, MSGPACK_PP_TUPLE_EAT_2)(80, s) MSGPACK_PP_IF(p(80, s), MSGPACK_PP_FOR_80, MSGPACK_PP_TUPLE_EAT_4)(o(80, s), p, o, m)
# define MSGPACK_PP_FOR_80(s, p, o, m) MSGPACK_PP_IF(p(81, s), m, MSGPACK_PP_TUPLE_EAT_2)(81, s) MSGPACK_PP_IF(p(81, s), MSGPACK_PP_FOR_81, MSGPACK_PP_TUPLE_EAT_4)(o(81, s), p, o, m)
# define MSGPACK_PP_FOR_81(s, p, o, m) MSGPACK_PP_IF(p(82, s), m, MSGPACK_PP_TUPLE_EAT_2)(82, s) MSGPACK_PP_IF(p(82, s), MSGPACK_PP_FOR_82, MSGPACK_PP_TUPLE_EAT_4)(o(82, s), p, o, m)
# define MSGPACK_PP_FOR_82(s, p, o, m) MSGPACK_PP_IF(p(83, s), m, MSGPACK_PP_TUPLE_EAT_2)(83, s) MSGPACK_PP_IF(p(83, s), MSGPACK_PP_FOR_83, MSGPACK_PP_TUPLE_EAT_4)(o(83, s), p, o, m)
# define MSGPACK_PP_FOR_83(s, p, o, m) MSGPACK_PP_IF(p(84, s), m, MSGPACK_PP_TUPLE_EAT_2)(84, s) MSGPACK_PP_IF(p(84, s), MSGPACK_PP_FOR_84, MSGPACK_PP_TUPLE_EAT_4)(o(84, s), p, o, m)
# define MSGPACK_PP_FOR_84(s, p, o, m) MSGPACK_PP_IF(p(85, s), m, MSGPACK_PP_TUPLE_EAT_2)(85, s) MSGPACK_PP_IF(p(85, s), MSGPACK_PP_FOR_85, MSGPACK_PP_TUPLE_EAT_4)(o(85, s), p, o, m)
# define MSGPACK_PP_FOR_85(s, p, o, m) MSGPACK_PP_IF(p(86, s), m, MSGPACK_PP_TUPLE_EAT_2)(86, s) MSGPACK_PP_IF(p(86, s), MSGPACK_PP_FOR_86, MSGPACK_PP_TUPLE_EAT_4)(o(86, s), p, o, m)
# define MSGPACK_PP_FOR_86(s, p, o, m) MSGPACK_PP_IF(p(87, s), m, MSGPACK_PP_TUPLE_EAT_2)(87, s) MSGPACK_PP_IF(p(87, s), MSGPACK_PP_FOR_87, MSGPACK_PP_TUPLE_EAT_4)(o(87, s), p, o, m)
# define MSGPACK_PP_FOR_87(s, p, o, m) MSGPACK_PP_IF(p(88, s), m, MSGPACK_PP_TUPLE_EAT_2)(88, s) MSGPACK_PP_IF(p(88, s), MSGPACK_PP_FOR_88, MSGPACK_PP_TUPLE_EAT_4)(o(88, s), p, o, m)
# define MSGPACK_PP_FOR_88(s, p, o, m) MSGPACK_PP_IF(p(89, s), m, MSGPACK_PP_TUPLE_EAT_2)(89, s) MSGPACK_PP_IF(p(89, s), MSGPACK_PP_FOR_89, MSGPACK_PP_TUPLE_EAT_4)(o(89, s), p, o, m)
# define MSGPACK_PP_FOR_89(s, p, o, m) MSGPACK_PP_IF(p(90, s), m, MSGPACK_PP_TUPLE_EAT_2)(90, s) MSGPACK_PP_IF(p(90, s), MSGPACK_PP_FOR_90, MSGPACK_PP_TUPLE_EAT_4)(o(90, s), p, o, m)
# define MSGPACK_PP_FOR_90(s, p, o, m) MSGPACK_PP_IF(p(91, s), m, MSGPACK_PP_TUPLE_EAT_2)(91, s) MSGPACK_PP_IF(p(91, s), MSGPACK_PP_FOR_91, MSGPACK_PP_TUPLE_EAT_4)(o(91, s), p, o, m)
# define MSGPACK_PP_FOR_91(s, p, o, m) MSGPACK_PP_IF(p(92, s), m, MSGPACK_PP_TUPLE_EAT_2)(92, s) MSGPACK_PP_IF(p(92, s), MSGPACK_PP_FOR_92, MSGPACK_PP_TUPLE_EAT_4)(o(92, s), p, o, m)
# define MSGPACK_PP_FOR_92(s, p, o, m) MSGPACK_PP_IF(p(93, s), m, MSGPACK_PP_TUPLE_EAT_2)(93, s) MSGPACK_PP_IF(p(93, s), MSGPACK_PP_FOR_93, MSGPACK_PP_TUPLE_EAT_4)(o(93, s), p, o, m)
# define MSGPACK_PP_FOR_93(s, p, o, m) MSGPACK_PP_IF(p(94, s), m, MSGPACK_PP_TUPLE_EAT_2)(94, s) MSGPACK_PP_IF(p(94, s), MSGPACK_PP_FOR_94, MSGPACK_PP_TUPLE_EAT_4)(o(94, s), p, o, m)
# define MSGPACK_PP_FOR_94(s, p, o, m) MSGPACK_PP_IF(p(95, s), m, MSGPACK_PP_TUPLE_EAT_2)(95, s) MSGPACK_PP_IF(p(95, s), MSGPACK_PP_FOR_95, MSGPACK_PP_TUPLE_EAT_4)(o(95, s), p, o, m)
# define MSGPACK_PP_FOR_95(s, p, o, m) MSGPACK_PP_IF(p(96, s), m, MSGPACK_PP_TUPLE_EAT_2)(96, s) MSGPACK_PP_IF(p(96, s), MSGPACK_PP_FOR_96, MSGPACK_PP_TUPLE_EAT_4)(o(96, s), p, o, m)
# define MSGPACK_PP_FOR_96(s, p, o, m) MSGPACK_PP_IF(p(97, s), m, MSGPACK_PP_TUPLE_EAT_2)(97, s) MSGPACK_PP_IF(p(97, s), MSGPACK_PP_FOR_97, MSGPACK_PP_TUPLE_EAT_4)(o(97, s), p, o, m)
# define MSGPACK_PP_FOR_97(s, p, o, m) MSGPACK_PP_IF(p(98, s), m, MSGPACK_PP_TUPLE_EAT_2)(98, s) MSGPACK_PP_IF(p(98, s), MSGPACK_PP_FOR_98, MSGPACK_PP_TUPLE_EAT_4)(o(98, s), p, o, m)
# define MSGPACK_PP_FOR_98(s, p, o, m) MSGPACK_PP_IF(p(99, s), m, MSGPACK_PP_TUPLE_EAT_2)(99, s) MSGPACK_PP_IF(p(99, s), MSGPACK_PP_FOR_99, MSGPACK_PP_TUPLE_EAT_4)(o(99, s), p, o, m)
# define MSGPACK_PP_FOR_99(s, p, o, m) MSGPACK_PP_IF(p(100, s), m, MSGPACK_PP_TUPLE_EAT_2)(100, s) MSGPACK_PP_IF(p(100, s), MSGPACK_PP_FOR_100, MSGPACK_PP_TUPLE_EAT_4)(o(100, s), p, o, m)
# define MSGPACK_PP_FOR_100(s, p, o, m) MSGPACK_PP_IF(p(101, s), m, MSGPACK_PP_TUPLE_EAT_2)(101, s) MSGPACK_PP_IF(p(101, s), MSGPACK_PP_FOR_101, MSGPACK_PP_TUPLE_EAT_4)(o(101, s), p, o, m)
# define MSGPACK_PP_FOR_101(s, p, o, m) MSGPACK_PP_IF(p(102, s), m, MSGPACK_PP_TUPLE_EAT_2)(102, s) MSGPACK_PP_IF(p(102, s), MSGPACK_PP_FOR_102, MSGPACK_PP_TUPLE_EAT_4)(o(102, s), p, o, m)
# define MSGPACK_PP_FOR_102(s, p, o, m) MSGPACK_PP_IF(p(103, s), m, MSGPACK_PP_TUPLE_EAT_2)(103, s) MSGPACK_PP_IF(p(103, s), MSGPACK_PP_FOR_103, MSGPACK_PP_TUPLE_EAT_4)(o(103, s), p, o, m)
# define MSGPACK_PP_FOR_103(s, p, o, m) MSGPACK_PP_IF(p(104, s), m, MSGPACK_PP_TUPLE_EAT_2)(104, s) MSGPACK_PP_IF(p(104, s), MSGPACK_PP_FOR_104, MSGPACK_PP_TUPLE_EAT_4)(o(104, s), p, o, m)
# define MSGPACK_PP_FOR_104(s, p, o, m) MSGPACK_PP_IF(p(105, s), m, MSGPACK_PP_TUPLE_EAT_2)(105, s) MSGPACK_PP_IF(p(105, s), MSGPACK_PP_FOR_105, MSGPACK_PP_TUPLE_EAT_4)(o(105, s), p, o, m)
# define MSGPACK_PP_FOR_105(s, p, o, m) MSGPACK_PP_IF(p(106, s), m, MSGPACK_PP_TUPLE_EAT_2)(106, s) MSGPACK_PP_IF(p(106, s), MSGPACK_PP_FOR_106, MSGPACK_PP_TUPLE_EAT_4)(o(106, s), p, o, m)
# define MSGPACK_PP_FOR_106(s, p, o, m) MSGPACK_PP_IF(p(107, s), m, MSGPACK_PP_TUPLE_EAT_2)(107, s) MSGPACK_PP_IF(p(107, s), MSGPACK_PP_FOR_107, MSGPACK_PP_TUPLE_EAT_4)(o(107, s), p, o, m)
# define MSGPACK_PP_FOR_107(s, p, o, m) MSGPACK_PP_IF(p(108, s), m, MSGPACK_PP_TUPLE_EAT_2)(108, s) MSGPACK_PP_IF(p(108, s), MSGPACK_PP_FOR_108, MSGPACK_PP_TUPLE_EAT_4)(o(108, s), p, o, m)
# define MSGPACK_PP_FOR_108(s, p, o, m) MSGPACK_PP_IF(p(109, s), m, MSGPACK_PP_TUPLE_EAT_2)(109, s) MSGPACK_PP_IF(p(109, s), MSGPACK_PP_FOR_109, MSGPACK_PP_TUPLE_EAT_4)(o(109, s), p, o, m)
# define MSGPACK_PP_FOR_109(s, p, o, m) MSGPACK_PP_IF(p(110, s), m, MSGPACK_PP_TUPLE_EAT_2)(110, s) MSGPACK_PP_IF(p(110, s), MSGPACK_PP_FOR_110, MSGPACK_PP_TUPLE_EAT_4)(o(110, s), p, o, m)
# define MSGPACK_PP_FOR_110(s, p, o, m) MSGPACK_PP_IF(p(111, s), m, MSGPACK_PP_TUPLE_EAT_2)(111, s) MSGPACK_PP_IF(p(111, s), MSGPACK_PP_FOR_111, MSGPACK_PP_TUPLE_EAT_4)(o(111, s), p, o, m)
# define MSGPACK_PP_FOR_111(s, p, o, m) MSGPACK_PP_IF(p(112, s), m, MSGPACK_PP_TUPLE_EAT_2)(112, s) MSGPACK_PP_IF(p(112, s), MSGPACK_PP_FOR_112, MSGPACK_PP_TUPLE_EAT_4)(o(112, s), p, o, m)
# define MSGPACK_PP_FOR_112(s, p, o, m) MSGPACK_PP_IF(p(113, s), m, MSGPACK_PP_TUPLE_EAT_2)(113, s) MSGPACK_PP_IF(p(113, s), MSGPACK_PP_FOR_113, MSGPACK_PP_TUPLE_EAT_4)(o(113, s), p, o, m)
# define MSGPACK_PP_FOR_113(s, p, o, m) MSGPACK_PP_IF(p(114, s), m, MSGPACK_PP_TUPLE_EAT_2)(114, s) MSGPACK_PP_IF(p(114, s), MSGPACK_PP_FOR_114, MSGPACK_PP_TUPLE_EAT_4)(o(114, s), p, o, m)
# define MSGPACK_PP_FOR_114(s, p, o, m) MSGPACK_PP_IF(p(115, s), m, MSGPACK_PP_TUPLE_EAT_2)(115, s) MSGPACK_PP_IF(p(115, s), MSGPACK_PP_FOR_115, MSGPACK_PP_TUPLE_EAT_4)(o(115, s), p, o, m)
# define MSGPACK_PP_FOR_115(s, p, o, m) MSGPACK_PP_IF(p(116, s), m, MSGPACK_PP_TUPLE_EAT_2)(116, s) MSGPACK_PP_IF(p(116, s), MSGPACK_PP_FOR_116, MSGPACK_PP_TUPLE_EAT_4)(o(116, s), p, o, m)
# define MSGPACK_PP_FOR_116(s, p, o, m) MSGPACK_PP_IF(p(117, s), m, MSGPACK_PP_TUPLE_EAT_2)(117, s) MSGPACK_PP_IF(p(117, s), MSGPACK_PP_FOR_117, MSGPACK_PP_TUPLE_EAT_4)(o(117, s), p, o, m)
# define MSGPACK_PP_FOR_117(s, p, o, m) MSGPACK_PP_IF(p(118, s), m, MSGPACK_PP_TUPLE_EAT_2)(118, s) MSGPACK_PP_IF(p(118, s), MSGPACK_PP_FOR_118, MSGPACK_PP_TUPLE_EAT_4)(o(118, s), p, o, m)
# define MSGPACK_PP_FOR_118(s, p, o, m) MSGPACK_PP_IF(p(119, s), m, MSGPACK_PP_TUPLE_EAT_2)(119, s) MSGPACK_PP_IF(p(119, s), MSGPACK_PP_FOR_119, MSGPACK_PP_TUPLE_EAT_4)(o(119, s), p, o, m)
# define MSGPACK_PP_FOR_119(s, p, o, m) MSGPACK_PP_IF(p(120, s), m, MSGPACK_PP_TUPLE_EAT_2)(120, s) MSGPACK_PP_IF(p(120, s), MSGPACK_PP_FOR_120, MSGPACK_PP_TUPLE_EAT_4)(o(120, s), p, o, m)
# define MSGPACK_PP_FOR_120(s, p, o, m) MSGPACK_PP_IF(p(121, s), m, MSGPACK_PP_TUPLE_EAT_2)(121, s) MSGPACK_PP_IF(p(121, s), MSGPACK_PP_FOR_121, MSGPACK_PP_TUPLE_EAT_4)(o(121, s), p, o, m)
# define MSGPACK_PP_FOR_121(s, p, o, m) MSGPACK_PP_IF(p(122, s), m, MSGPACK_PP_TUPLE_EAT_2)(122, s) MSGPACK_PP_IF(p(122, s), MSGPACK_PP_FOR_122, MSGPACK_PP_TUPLE_EAT_4)(o(122, s), p, o, m)
# define MSGPACK_PP_FOR_122(s, p, o, m) MSGPACK_PP_IF(p(123, s), m, MSGPACK_PP_TUPLE_EAT_2)(123, s) MSGPACK_PP_IF(p(123, s), MSGPACK_PP_FOR_123, MSGPACK_PP_TUPLE_EAT_4)(o(123, s), p, o, m)
# define MSGPACK_PP_FOR_123(s, p, o, m) MSGPACK_PP_IF(p(124, s), m, MSGPACK_PP_TUPLE_EAT_2)(124, s) MSGPACK_PP_IF(p(124, s), MSGPACK_PP_FOR_124, MSGPACK_PP_TUPLE_EAT_4)(o(124, s), p, o, m)
# define MSGPACK_PP_FOR_124(s, p, o, m) MSGPACK_PP_IF(p(125, s), m, MSGPACK_PP_TUPLE_EAT_2)(125, s) MSGPACK_PP_IF(p(125, s), MSGPACK_PP_FOR_125, MSGPACK_PP_TUPLE_EAT_4)(o(125, s), p, o, m)
# define MSGPACK_PP_FOR_125(s, p, o, m) MSGPACK_PP_IF(p(126, s), m, MSGPACK_PP_TUPLE_EAT_2)(126, s) MSGPACK_PP_IF(p(126, s), MSGPACK_PP_FOR_126, MSGPACK_PP_TUPLE_EAT_4)(o(126, s), p, o, m)
# define MSGPACK_PP_FOR_126(s, p, o, m) MSGPACK_PP_IF(p(127, s), m, MSGPACK_PP_TUPLE_EAT_2)(127, s) MSGPACK_PP_IF(p(127, s), MSGPACK_PP_FOR_127, MSGPACK_PP_TUPLE_EAT_4)(o(127, s), p, o, m)
# define MSGPACK_PP_FOR_127(s, p, o, m) MSGPACK_PP_IF(p(128, s), m, MSGPACK_PP_TUPLE_EAT_2)(128, s) MSGPACK_PP_IF(p(128, s), MSGPACK_PP_FOR_128, MSGPACK_PP_TUPLE_EAT_4)(o(128, s), p, o, m)
# define MSGPACK_PP_FOR_128(s, p, o, m) MSGPACK_PP_IF(p(129, s), m, MSGPACK_PP_TUPLE_EAT_2)(129, s) MSGPACK_PP_IF(p(129, s), MSGPACK_PP_FOR_129, MSGPACK_PP_TUPLE_EAT_4)(o(129, s), p, o, m)
# define MSGPACK_PP_FOR_129(s, p, o, m) MSGPACK_PP_IF(p(130, s), m, MSGPACK_PP_TUPLE_EAT_2)(130, s) MSGPACK_PP_IF(p(130, s), MSGPACK_PP_FOR_130, MSGPACK_PP_TUPLE_EAT_4)(o(130, s), p, o, m)
# define MSGPACK_PP_FOR_130(s, p, o, m) MSGPACK_PP_IF(p(131, s), m, MSGPACK_PP_TUPLE_EAT_2)(131, s) MSGPACK_PP_IF(p(131, s), MSGPACK_PP_FOR_131, MSGPACK_PP_TUPLE_EAT_4)(o(131, s), p, o, m)
# define MSGPACK_PP_FOR_131(s, p, o, m) MSGPACK_PP_IF(p(132, s), m, MSGPACK_PP_TUPLE_EAT_2)(132, s) MSGPACK_PP_IF(p(132, s), MSGPACK_PP_FOR_132, MSGPACK_PP_TUPLE_EAT_4)(o(132, s), p, o, m)
# define MSGPACK_PP_FOR_132(s, p, o, m) MSGPACK_PP_IF(p(133, s), m, MSGPACK_PP_TUPLE_EAT_2)(133, s) MSGPACK_PP_IF(p(133, s), MSGPACK_PP_FOR_133, MSGPACK_PP_TUPLE_EAT_4)(o(133, s), p, o, m)
# define MSGPACK_PP_FOR_133(s, p, o, m) MSGPACK_PP_IF(p(134, s), m, MSGPACK_PP_TUPLE_EAT_2)(134, s) MSGPACK_PP_IF(p(134, s), MSGPACK_PP_FOR_134, MSGPACK_PP_TUPLE_EAT_4)(o(134, s), p, o, m)
# define MSGPACK_PP_FOR_134(s, p, o, m) MSGPACK_PP_IF(p(135, s), m, MSGPACK_PP_TUPLE_EAT_2)(135, s) MSGPACK_PP_IF(p(135, s), MSGPACK_PP_FOR_135, MSGPACK_PP_TUPLE_EAT_4)(o(135, s), p, o, m)
# define MSGPACK_PP_FOR_135(s, p, o, m) MSGPACK_PP_IF(p(136, s), m, MSGPACK_PP_TUPLE_EAT_2)(136, s) MSGPACK_PP_IF(p(136, s), MSGPACK_PP_FOR_136, MSGPACK_PP_TUPLE_EAT_4)(o(136, s), p, o, m)
# define MSGPACK_PP_FOR_136(s, p, o, m) MSGPACK_PP_IF(p(137, s), m, MSGPACK_PP_TUPLE_EAT_2)(137, s) MSGPACK_PP_IF(p(137, s), MSGPACK_PP_FOR_137, MSGPACK_PP_TUPLE_EAT_4)(o(137, s), p, o, m)
# define MSGPACK_PP_FOR_137(s, p, o, m) MSGPACK_PP_IF(p(138, s), m, MSGPACK_PP_TUPLE_EAT_2)(138, s) MSGPACK_PP_IF(p(138, s), MSGPACK_PP_FOR_138, MSGPACK_PP_TUPLE_EAT_4)(o(138, s), p, o, m)
# define MSGPACK_PP_FOR_138(s, p, o, m) MSGPACK_PP_IF(p(139, s), m, MSGPACK_PP_TUPLE_EAT_2)(139, s) MSGPACK_PP_IF(p(139, s), MSGPACK_PP_FOR_139, MSGPACK_PP_TUPLE_EAT_4)(o(139, s), p, o, m)
# define MSGPACK_PP_FOR_139(s, p, o, m) MSGPACK_PP_IF(p(140, s), m, MSGPACK_PP_TUPLE_EAT_2)(140, s) MSGPACK_PP_IF(p(140, s), MSGPACK_PP_FOR_140, MSGPACK_PP_TUPLE_EAT_4)(o(140, s), p, o, m)
# define MSGPACK_PP_FOR_140(s, p, o, m) MSGPACK_PP_IF(p(141, s), m, MSGPACK_PP_TUPLE_EAT_2)(141, s) MSGPACK_PP_IF(p(141, s), MSGPACK_PP_FOR_141, MSGPACK_PP_TUPLE_EAT_4)(o(141, s), p, o, m)
# define MSGPACK_PP_FOR_141(s, p, o, m) MSGPACK_PP_IF(p(142, s), m, MSGPACK_PP_TUPLE_EAT_2)(142, s) MSGPACK_PP_IF(p(142, s), MSGPACK_PP_FOR_142, MSGPACK_PP_TUPLE_EAT_4)(o(142, s), p, o, m)
# define MSGPACK_PP_FOR_142(s, p, o, m) MSGPACK_PP_IF(p(143, s), m, MSGPACK_PP_TUPLE_EAT_2)(143, s) MSGPACK_PP_IF(p(143, s), MSGPACK_PP_FOR_143, MSGPACK_PP_TUPLE_EAT_4)(o(143, s), p, o, m)
# define MSGPACK_PP_FOR_143(s, p, o, m) MSGPACK_PP_IF(p(144, s), m, MSGPACK_PP_TUPLE_EAT_2)(144, s) MSGPACK_PP_IF(p(144, s), MSGPACK_PP_FOR_144, MSGPACK_PP_TUPLE_EAT_4)(o(144, s), p, o, m)
# define MSGPACK_PP_FOR_144(s, p, o, m) MSGPACK_PP_IF(p(145, s), m, MSGPACK_PP_TUPLE_EAT_2)(145, s) MSGPACK_PP_IF(p(145, s), MSGPACK_PP_FOR_145, MSGPACK_PP_TUPLE_EAT_4)(o(145, s), p, o, m)
# define MSGPACK_PP_FOR_145(s, p, o, m) MSGPACK_PP_IF(p(146, s), m, MSGPACK_PP_TUPLE_EAT_2)(146, s) MSGPACK_PP_IF(p(146, s), MSGPACK_PP_FOR_146, MSGPACK_PP_TUPLE_EAT_4)(o(146, s), p, o, m)
# define MSGPACK_PP_FOR_146(s, p, o, m) MSGPACK_PP_IF(p(147, s), m, MSGPACK_PP_TUPLE_EAT_2)(147, s) MSGPACK_PP_IF(p(147, s), MSGPACK_PP_FOR_147, MSGPACK_PP_TUPLE_EAT_4)(o(147, s), p, o, m)
# define MSGPACK_PP_FOR_147(s, p, o, m) MSGPACK_PP_IF(p(148, s), m, MSGPACK_PP_TUPLE_EAT_2)(148, s) MSGPACK_PP_IF(p(148, s), MSGPACK_PP_FOR_148, MSGPACK_PP_TUPLE_EAT_4)(o(148, s), p, o, m)
# define MSGPACK_PP_FOR_148(s, p, o, m) MSGPACK_PP_IF(p(149, s), m, MSGPACK_PP_TUPLE_EAT_2)(149, s) MSGPACK_PP_IF(p(149, s), MSGPACK_PP_FOR_149, MSGPACK_PP_TUPLE_EAT_4)(o(149, s), p, o, m)
# define MSGPACK_PP_FOR_149(s, p, o, m) MSGPACK_PP_IF(p(150, s), m, MSGPACK_PP_TUPLE_EAT_2)(150, s) MSGPACK_PP_IF(p(150, s), MSGPACK_PP_FOR_150, MSGPACK_PP_TUPLE_EAT_4)(o(150, s), p, o, m)
# define MSGPACK_PP_FOR_150(s, p, o, m) MSGPACK_PP_IF(p(151, s), m, MSGPACK_PP_TUPLE_EAT_2)(151, s) MSGPACK_PP_IF(p(151, s), MSGPACK_PP_FOR_151, MSGPACK_PP_TUPLE_EAT_4)(o(151, s), p, o, m)
# define MSGPACK_PP_FOR_151(s, p, o, m) MSGPACK_PP_IF(p(152, s), m, MSGPACK_PP_TUPLE_EAT_2)(152, s) MSGPACK_PP_IF(p(152, s), MSGPACK_PP_FOR_152, MSGPACK_PP_TUPLE_EAT_4)(o(152, s), p, o, m)
# define MSGPACK_PP_FOR_152(s, p, o, m) MSGPACK_PP_IF(p(153, s), m, MSGPACK_PP_TUPLE_EAT_2)(153, s) MSGPACK_PP_IF(p(153, s), MSGPACK_PP_FOR_153, MSGPACK_PP_TUPLE_EAT_4)(o(153, s), p, o, m)
# define MSGPACK_PP_FOR_153(s, p, o, m) MSGPACK_PP_IF(p(154, s), m, MSGPACK_PP_TUPLE_EAT_2)(154, s) MSGPACK_PP_IF(p(154, s), MSGPACK_PP_FOR_154, MSGPACK_PP_TUPLE_EAT_4)(o(154, s), p, o, m)
# define MSGPACK_PP_FOR_154(s, p, o, m) MSGPACK_PP_IF(p(155, s), m, MSGPACK_PP_TUPLE_EAT_2)(155, s) MSGPACK_PP_IF(p(155, s), MSGPACK_PP_FOR_155, MSGPACK_PP_TUPLE_EAT_4)(o(155, s), p, o, m)
# define MSGPACK_PP_FOR_155(s, p, o, m) MSGPACK_PP_IF(p(156, s), m, MSGPACK_PP_TUPLE_EAT_2)(156, s) MSGPACK_PP_IF(p(156, s), MSGPACK_PP_FOR_156, MSGPACK_PP_TUPLE_EAT_4)(o(156, s), p, o, m)
# define MSGPACK_PP_FOR_156(s, p, o, m) MSGPACK_PP_IF(p(157, s), m, MSGPACK_PP_TUPLE_EAT_2)(157, s) MSGPACK_PP_IF(p(157, s), MSGPACK_PP_FOR_157, MSGPACK_PP_TUPLE_EAT_4)(o(157, s), p, o, m)
# define MSGPACK_PP_FOR_157(s, p, o, m) MSGPACK_PP_IF(p(158, s), m, MSGPACK_PP_TUPLE_EAT_2)(158, s) MSGPACK_PP_IF(p(158, s), MSGPACK_PP_FOR_158, MSGPACK_PP_TUPLE_EAT_4)(o(158, s), p, o, m)
# define MSGPACK_PP_FOR_158(s, p, o, m) MSGPACK_PP_IF(p(159, s), m, MSGPACK_PP_TUPLE_EAT_2)(159, s) MSGPACK_PP_IF(p(159, s), MSGPACK_PP_FOR_159, MSGPACK_PP_TUPLE_EAT_4)(o(159, s), p, o, m)
# define MSGPACK_PP_FOR_159(s, p, o, m) MSGPACK_PP_IF(p(160, s), m, MSGPACK_PP_TUPLE_EAT_2)(160, s) MSGPACK_PP_IF(p(160, s), MSGPACK_PP_FOR_160, MSGPACK_PP_TUPLE_EAT_4)(o(160, s), p, o, m)
# define MSGPACK_PP_FOR_160(s, p, o, m) MSGPACK_PP_IF(p(161, s), m, MSGPACK_PP_TUPLE_EAT_2)(161, s) MSGPACK_PP_IF(p(161, s), MSGPACK_PP_FOR_161, MSGPACK_PP_TUPLE_EAT_4)(o(161, s), p, o, m)
# define MSGPACK_PP_FOR_161(s, p, o, m) MSGPACK_PP_IF(p(162, s), m, MSGPACK_PP_TUPLE_EAT_2)(162, s) MSGPACK_PP_IF(p(162, s), MSGPACK_PP_FOR_162, MSGPACK_PP_TUPLE_EAT_4)(o(162, s), p, o, m)
# define MSGPACK_PP_FOR_162(s, p, o, m) MSGPACK_PP_IF(p(163, s), m, MSGPACK_PP_TUPLE_EAT_2)(163, s) MSGPACK_PP_IF(p(163, s), MSGPACK_PP_FOR_163, MSGPACK_PP_TUPLE_EAT_4)(o(163, s), p, o, m)
# define MSGPACK_PP_FOR_163(s, p, o, m) MSGPACK_PP_IF(p(164, s), m, MSGPACK_PP_TUPLE_EAT_2)(164, s) MSGPACK_PP_IF(p(164, s), MSGPACK_PP_FOR_164, MSGPACK_PP_TUPLE_EAT_4)(o(164, s), p, o, m)
# define MSGPACK_PP_FOR_164(s, p, o, m) MSGPACK_PP_IF(p(165, s), m, MSGPACK_PP_TUPLE_EAT_2)(165, s) MSGPACK_PP_IF(p(165, s), MSGPACK_PP_FOR_165, MSGPACK_PP_TUPLE_EAT_4)(o(165, s), p, o, m)
# define MSGPACK_PP_FOR_165(s, p, o, m) MSGPACK_PP_IF(p(166, s), m, MSGPACK_PP_TUPLE_EAT_2)(166, s) MSGPACK_PP_IF(p(166, s), MSGPACK_PP_FOR_166, MSGPACK_PP_TUPLE_EAT_4)(o(166, s), p, o, m)
# define MSGPACK_PP_FOR_166(s, p, o, m) MSGPACK_PP_IF(p(167, s), m, MSGPACK_PP_TUPLE_EAT_2)(167, s) MSGPACK_PP_IF(p(167, s), MSGPACK_PP_FOR_167, MSGPACK_PP_TUPLE_EAT_4)(o(167, s), p, o, m)
# define MSGPACK_PP_FOR_167(s, p, o, m) MSGPACK_PP_IF(p(168, s), m, MSGPACK_PP_TUPLE_EAT_2)(168, s) MSGPACK_PP_IF(p(168, s), MSGPACK_PP_FOR_168, MSGPACK_PP_TUPLE_EAT_4)(o(168, s), p, o, m)
# define MSGPACK_PP_FOR_168(s, p, o, m) MSGPACK_PP_IF(p(169, s), m, MSGPACK_PP_TUPLE_EAT_2)(169, s) MSGPACK_PP_IF(p(169, s), MSGPACK_PP_FOR_169, MSGPACK_PP_TUPLE_EAT_4)(o(169, s), p, o, m)
# define MSGPACK_PP_FOR_169(s, p, o, m) MSGPACK_PP_IF(p(170, s), m, MSGPACK_PP_TUPLE_EAT_2)(170, s) MSGPACK_PP_IF(p(170, s), MSGPACK_PP_FOR_170, MSGPACK_PP_TUPLE_EAT_4)(o(170, s), p, o, m)
# define MSGPACK_PP_FOR_170(s, p, o, m) MSGPACK_PP_IF(p(171, s), m, MSGPACK_PP_TUPLE_EAT_2)(171, s) MSGPACK_PP_IF(p(171, s), MSGPACK_PP_FOR_171, MSGPACK_PP_TUPLE_EAT_4)(o(171, s), p, o, m)
# define MSGPACK_PP_FOR_171(s, p, o, m) MSGPACK_PP_IF(p(172, s), m, MSGPACK_PP_TUPLE_EAT_2)(172, s) MSGPACK_PP_IF(p(172, s), MSGPACK_PP_FOR_172, MSGPACK_PP_TUPLE_EAT_4)(o(172, s), p, o, m)
# define MSGPACK_PP_FOR_172(s, p, o, m) MSGPACK_PP_IF(p(173, s), m, MSGPACK_PP_TUPLE_EAT_2)(173, s) MSGPACK_PP_IF(p(173, s), MSGPACK_PP_FOR_173, MSGPACK_PP_TUPLE_EAT_4)(o(173, s), p, o, m)
# define MSGPACK_PP_FOR_173(s, p, o, m) MSGPACK_PP_IF(p(174, s), m, MSGPACK_PP_TUPLE_EAT_2)(174, s) MSGPACK_PP_IF(p(174, s), MSGPACK_PP_FOR_174, MSGPACK_PP_TUPLE_EAT_4)(o(174, s), p, o, m)
# define MSGPACK_PP_FOR_174(s, p, o, m) MSGPACK_PP_IF(p(175, s), m, MSGPACK_PP_TUPLE_EAT_2)(175, s) MSGPACK_PP_IF(p(175, s), MSGPACK_PP_FOR_175, MSGPACK_PP_TUPLE_EAT_4)(o(175, s), p, o, m)
# define MSGPACK_PP_FOR_175(s, p, o, m) MSGPACK_PP_IF(p(176, s), m, MSGPACK_PP_TUPLE_EAT_2)(176, s) MSGPACK_PP_IF(p(176, s), MSGPACK_PP_FOR_176, MSGPACK_PP_TUPLE_EAT_4)(o(176, s), p, o, m)
# define MSGPACK_PP_FOR_176(s, p, o, m) MSGPACK_PP_IF(p(177, s), m, MSGPACK_PP_TUPLE_EAT_2)(177, s) MSGPACK_PP_IF(p(177, s), MSGPACK_PP_FOR_177, MSGPACK_PP_TUPLE_EAT_4)(o(177, s), p, o, m)
# define MSGPACK_PP_FOR_177(s, p, o, m) MSGPACK_PP_IF(p(178, s), m, MSGPACK_PP_TUPLE_EAT_2)(178, s) MSGPACK_PP_IF(p(178, s), MSGPACK_PP_FOR_178, MSGPACK_PP_TUPLE_EAT_4)(o(178, s), p, o, m)
# define MSGPACK_PP_FOR_178(s, p, o, m) MSGPACK_PP_IF(p(179, s), m, MSGPACK_PP_TUPLE_EAT_2)(179, s) MSGPACK_PP_IF(p(179, s), MSGPACK_PP_FOR_179, MSGPACK_PP_TUPLE_EAT_4)(o(179, s), p, o, m)
# define MSGPACK_PP_FOR_179(s, p, o, m) MSGPACK_PP_IF(p(180, s), m, MSGPACK_PP_TUPLE_EAT_2)(180, s) MSGPACK_PP_IF(p(180, s), MSGPACK_PP_FOR_180, MSGPACK_PP_TUPLE_EAT_4)(o(180, s), p, o, m)
# define MSGPACK_PP_FOR_180(s, p, o, m) MSGPACK_PP_IF(p(181, s), m, MSGPACK_PP_TUPLE_EAT_2)(181, s) MSGPACK_PP_IF(p(181, s), MSGPACK_PP_FOR_181, MSGPACK_PP_TUPLE_EAT_4)(o(181, s), p, o, m)
# define MSGPACK_PP_FOR_181(s, p, o, m) MSGPACK_PP_IF(p(182, s), m, MSGPACK_PP_TUPLE_EAT_2)(182, s) MSGPACK_PP_IF(p(182, s), MSGPACK_PP_FOR_182, MSGPACK_PP_TUPLE_EAT_4)(o(182, s), p, o, m)
# define MSGPACK_PP_FOR_182(s, p, o, m) MSGPACK_PP_IF(p(183, s), m, MSGPACK_PP_TUPLE_EAT_2)(183, s) MSGPACK_PP_IF(p(183, s), MSGPACK_PP_FOR_183, MSGPACK_PP_TUPLE_EAT_4)(o(183, s), p, o, m)
# define MSGPACK_PP_FOR_183(s, p, o, m) MSGPACK_PP_IF(p(184, s), m, MSGPACK_PP_TUPLE_EAT_2)(184, s) MSGPACK_PP_IF(p(184, s), MSGPACK_PP_FOR_184, MSGPACK_PP_TUPLE_EAT_4)(o(184, s), p, o, m)
# define MSGPACK_PP_FOR_184(s, p, o, m) MSGPACK_PP_IF(p(185, s), m, MSGPACK_PP_TUPLE_EAT_2)(185, s) MSGPACK_PP_IF(p(185, s), MSGPACK_PP_FOR_185, MSGPACK_PP_TUPLE_EAT_4)(o(185, s), p, o, m)
# define MSGPACK_PP_FOR_185(s, p, o, m) MSGPACK_PP_IF(p(186, s), m, MSGPACK_PP_TUPLE_EAT_2)(186, s) MSGPACK_PP_IF(p(186, s), MSGPACK_PP_FOR_186, MSGPACK_PP_TUPLE_EAT_4)(o(186, s), p, o, m)
# define MSGPACK_PP_FOR_186(s, p, o, m) MSGPACK_PP_IF(p(187, s), m, MSGPACK_PP_TUPLE_EAT_2)(187, s) MSGPACK_PP_IF(p(187, s), MSGPACK_PP_FOR_187, MSGPACK_PP_TUPLE_EAT_4)(o(187, s), p, o, m)
# define MSGPACK_PP_FOR_187(s, p, o, m) MSGPACK_PP_IF(p(188, s), m, MSGPACK_PP_TUPLE_EAT_2)(188, s) MSGPACK_PP_IF(p(188, s), MSGPACK_PP_FOR_188, MSGPACK_PP_TUPLE_EAT_4)(o(188, s), p, o, m)
# define MSGPACK_PP_FOR_188(s, p, o, m) MSGPACK_PP_IF(p(189, s), m, MSGPACK_PP_TUPLE_EAT_2)(189, s) MSGPACK_PP_IF(p(189, s), MSGPACK_PP_FOR_189, MSGPACK_PP_TUPLE_EAT_4)(o(189, s), p, o, m)
# define MSGPACK_PP_FOR_189(s, p, o, m) MSGPACK_PP_IF(p(190, s), m, MSGPACK_PP_TUPLE_EAT_2)(190, s) MSGPACK_PP_IF(p(190, s), MSGPACK_PP_FOR_190, MSGPACK_PP_TUPLE_EAT_4)(o(190, s), p, o, m)
# define MSGPACK_PP_FOR_190(s, p, o, m) MSGPACK_PP_IF(p(191, s), m, MSGPACK_PP_TUPLE_EAT_2)(191, s) MSGPACK_PP_IF(p(191, s), MSGPACK_PP_FOR_191, MSGPACK_PP_TUPLE_EAT_4)(o(191, s), p, o, m)
# define MSGPACK_PP_FOR_191(s, p, o, m) MSGPACK_PP_IF(p(192, s), m, MSGPACK_PP_TUPLE_EAT_2)(192, s) MSGPACK_PP_IF(p(192, s), MSGPACK_PP_FOR_192, MSGPACK_PP_TUPLE_EAT_4)(o(192, s), p, o, m)
# define MSGPACK_PP_FOR_192(s, p, o, m) MSGPACK_PP_IF(p(193, s), m, MSGPACK_PP_TUPLE_EAT_2)(193, s) MSGPACK_PP_IF(p(193, s), MSGPACK_PP_FOR_193, MSGPACK_PP_TUPLE_EAT_4)(o(193, s), p, o, m)
# define MSGPACK_PP_FOR_193(s, p, o, m) MSGPACK_PP_IF(p(194, s), m, MSGPACK_PP_TUPLE_EAT_2)(194, s) MSGPACK_PP_IF(p(194, s), MSGPACK_PP_FOR_194, MSGPACK_PP_TUPLE_EAT_4)(o(194, s), p, o, m)
# define MSGPACK_PP_FOR_194(s, p, o, m) MSGPACK_PP_IF(p(195, s), m, MSGPACK_PP_TUPLE_EAT_2)(195, s) MSGPACK_PP_IF(p(195, s), MSGPACK_PP_FOR_195, MSGPACK_PP_TUPLE_EAT_4)(o(195, s), p, o, m)
# define MSGPACK_PP_FOR_195(s, p, o, m) MSGPACK_PP_IF(p(196, s), m, MSGPACK_PP_TUPLE_EAT_2)(196, s) MSGPACK_PP_IF(p(196, s), MSGPACK_PP_FOR_196, MSGPACK_PP_TUPLE_EAT_4)(o(196, s), p, o, m)
# define MSGPACK_PP_FOR_196(s, p, o, m) MSGPACK_PP_IF(p(197, s), m, MSGPACK_PP_TUPLE_EAT_2)(197, s) MSGPACK_PP_IF(p(197, s), MSGPACK_PP_FOR_197, MSGPACK_PP_TUPLE_EAT_4)(o(197, s), p, o, m)
# define MSGPACK_PP_FOR_197(s, p, o, m) MSGPACK_PP_IF(p(198, s), m, MSGPACK_PP_TUPLE_EAT_2)(198, s) MSGPACK_PP_IF(p(198, s), MSGPACK_PP_FOR_198, MSGPACK_PP_TUPLE_EAT_4)(o(198, s), p, o, m)
# define MSGPACK_PP_FOR_198(s, p, o, m) MSGPACK_PP_IF(p(199, s), m, MSGPACK_PP_TUPLE_EAT_2)(199, s) MSGPACK_PP_IF(p(199, s), MSGPACK_PP_FOR_199, MSGPACK_PP_TUPLE_EAT_4)(o(199, s), p, o, m)
# define MSGPACK_PP_FOR_199(s, p, o, m) MSGPACK_PP_IF(p(200, s), m, MSGPACK_PP_TUPLE_EAT_2)(200, s) MSGPACK_PP_IF(p(200, s), MSGPACK_PP_FOR_200, MSGPACK_PP_TUPLE_EAT_4)(o(200, s), p, o, m)
# define MSGPACK_PP_FOR_200(s, p, o, m) MSGPACK_PP_IF(p(201, s), m, MSGPACK_PP_TUPLE_EAT_2)(201, s) MSGPACK_PP_IF(p(201, s), MSGPACK_PP_FOR_201, MSGPACK_PP_TUPLE_EAT_4)(o(201, s), p, o, m)
# define MSGPACK_PP_FOR_201(s, p, o, m) MSGPACK_PP_IF(p(202, s), m, MSGPACK_PP_TUPLE_EAT_2)(202, s) MSGPACK_PP_IF(p(202, s), MSGPACK_PP_FOR_202, MSGPACK_PP_TUPLE_EAT_4)(o(202, s), p, o, m)
# define MSGPACK_PP_FOR_202(s, p, o, m) MSGPACK_PP_IF(p(203, s), m, MSGPACK_PP_TUPLE_EAT_2)(203, s) MSGPACK_PP_IF(p(203, s), MSGPACK_PP_FOR_203, MSGPACK_PP_TUPLE_EAT_4)(o(203, s), p, o, m)
# define MSGPACK_PP_FOR_203(s, p, o, m) MSGPACK_PP_IF(p(204, s), m, MSGPACK_PP_TUPLE_EAT_2)(204, s) MSGPACK_PP_IF(p(204, s), MSGPACK_PP_FOR_204, MSGPACK_PP_TUPLE_EAT_4)(o(204, s), p, o, m)
# define MSGPACK_PP_FOR_204(s, p, o, m) MSGPACK_PP_IF(p(205, s), m, MSGPACK_PP_TUPLE_EAT_2)(205, s) MSGPACK_PP_IF(p(205, s), MSGPACK_PP_FOR_205, MSGPACK_PP_TUPLE_EAT_4)(o(205, s), p, o, m)
# define MSGPACK_PP_FOR_205(s, p, o, m) MSGPACK_PP_IF(p(206, s), m, MSGPACK_PP_TUPLE_EAT_2)(206, s) MSGPACK_PP_IF(p(206, s), MSGPACK_PP_FOR_206, MSGPACK_PP_TUPLE_EAT_4)(o(206, s), p, o, m)
# define MSGPACK_PP_FOR_206(s, p, o, m) MSGPACK_PP_IF(p(207, s), m, MSGPACK_PP_TUPLE_EAT_2)(207, s) MSGPACK_PP_IF(p(207, s), MSGPACK_PP_FOR_207, MSGPACK_PP_TUPLE_EAT_4)(o(207, s), p, o, m)
# define MSGPACK_PP_FOR_207(s, p, o, m) MSGPACK_PP_IF(p(208, s), m, MSGPACK_PP_TUPLE_EAT_2)(208, s) MSGPACK_PP_IF(p(208, s), MSGPACK_PP_FOR_208, MSGPACK_PP_TUPLE_EAT_4)(o(208, s), p, o, m)
# define MSGPACK_PP_FOR_208(s, p, o, m) MSGPACK_PP_IF(p(209, s), m, MSGPACK_PP_TUPLE_EAT_2)(209, s) MSGPACK_PP_IF(p(209, s), MSGPACK_PP_FOR_209, MSGPACK_PP_TUPLE_EAT_4)(o(209, s), p, o, m)
# define MSGPACK_PP_FOR_209(s, p, o, m) MSGPACK_PP_IF(p(210, s), m, MSGPACK_PP_TUPLE_EAT_2)(210, s) MSGPACK_PP_IF(p(210, s), MSGPACK_PP_FOR_210, MSGPACK_PP_TUPLE_EAT_4)(o(210, s), p, o, m)
# define MSGPACK_PP_FOR_210(s, p, o, m) MSGPACK_PP_IF(p(211, s), m, MSGPACK_PP_TUPLE_EAT_2)(211, s) MSGPACK_PP_IF(p(211, s), MSGPACK_PP_FOR_211, MSGPACK_PP_TUPLE_EAT_4)(o(211, s), p, o, m)
# define MSGPACK_PP_FOR_211(s, p, o, m) MSGPACK_PP_IF(p(212, s), m, MSGPACK_PP_TUPLE_EAT_2)(212, s) MSGPACK_PP_IF(p(212, s), MSGPACK_PP_FOR_212, MSGPACK_PP_TUPLE_EAT_4)(o(212, s), p, o, m)
# define MSGPACK_PP_FOR_212(s, p, o, m) MSGPACK_PP_IF(p(213, s), m, MSGPACK_PP_TUPLE_EAT_2)(213, s) MSGPACK_PP_IF(p(213, s), MSGPACK_PP_FOR_213, MSGPACK_PP_TUPLE_EAT_4)(o(213, s), p, o, m)
# define MSGPACK_PP_FOR_213(s, p, o, m) MSGPACK_PP_IF(p(214, s), m, MSGPACK_PP_TUPLE_EAT_2)(214, s) MSGPACK_PP_IF(p(214, s), MSGPACK_PP_FOR_214, MSGPACK_PP_TUPLE_EAT_4)(o(214, s), p, o, m)
# define MSGPACK_PP_FOR_214(s, p, o, m) MSGPACK_PP_IF(p(215, s), m, MSGPACK_PP_TUPLE_EAT_2)(215, s) MSGPACK_PP_IF(p(215, s), MSGPACK_PP_FOR_215, MSGPACK_PP_TUPLE_EAT_4)(o(215, s), p, o, m)
# define MSGPACK_PP_FOR_215(s, p, o, m) MSGPACK_PP_IF(p(216, s), m, MSGPACK_PP_TUPLE_EAT_2)(216, s) MSGPACK_PP_IF(p(216, s), MSGPACK_PP_FOR_216, MSGPACK_PP_TUPLE_EAT_4)(o(216, s), p, o, m)
# define MSGPACK_PP_FOR_216(s, p, o, m) MSGPACK_PP_IF(p(217, s), m, MSGPACK_PP_TUPLE_EAT_2)(217, s) MSGPACK_PP_IF(p(217, s), MSGPACK_PP_FOR_217, MSGPACK_PP_TUPLE_EAT_4)(o(217, s), p, o, m)
# define MSGPACK_PP_FOR_217(s, p, o, m) MSGPACK_PP_IF(p(218, s), m, MSGPACK_PP_TUPLE_EAT_2)(218, s) MSGPACK_PP_IF(p(218, s), MSGPACK_PP_FOR_218, MSGPACK_PP_TUPLE_EAT_4)(o(218, s), p, o, m)
# define MSGPACK_PP_FOR_218(s, p, o, m) MSGPACK_PP_IF(p(219, s), m, MSGPACK_PP_TUPLE_EAT_2)(219, s) MSGPACK_PP_IF(p(219, s), MSGPACK_PP_FOR_219, MSGPACK_PP_TUPLE_EAT_4)(o(219, s), p, o, m)
# define MSGPACK_PP_FOR_219(s, p, o, m) MSGPACK_PP_IF(p(220, s), m, MSGPACK_PP_TUPLE_EAT_2)(220, s) MSGPACK_PP_IF(p(220, s), MSGPACK_PP_FOR_220, MSGPACK_PP_TUPLE_EAT_4)(o(220, s), p, o, m)
# define MSGPACK_PP_FOR_220(s, p, o, m) MSGPACK_PP_IF(p(221, s), m, MSGPACK_PP_TUPLE_EAT_2)(221, s) MSGPACK_PP_IF(p(221, s), MSGPACK_PP_FOR_221, MSGPACK_PP_TUPLE_EAT_4)(o(221, s), p, o, m)
# define MSGPACK_PP_FOR_221(s, p, o, m) MSGPACK_PP_IF(p(222, s), m, MSGPACK_PP_TUPLE_EAT_2)(222, s) MSGPACK_PP_IF(p(222, s), MSGPACK_PP_FOR_222, MSGPACK_PP_TUPLE_EAT_4)(o(222, s), p, o, m)
# define MSGPACK_PP_FOR_222(s, p, o, m) MSGPACK_PP_IF(p(223, s), m, MSGPACK_PP_TUPLE_EAT_2)(223, s) MSGPACK_PP_IF(p(223, s), MSGPACK_PP_FOR_223, MSGPACK_PP_TUPLE_EAT_4)(o(223, s), p, o, m)
# define MSGPACK_PP_FOR_223(s, p, o, m) MSGPACK_PP_IF(p(224, s), m, MSGPACK_PP_TUPLE_EAT_2)(224, s) MSGPACK_PP_IF(p(224, s), MSGPACK_PP_FOR_224, MSGPACK_PP_TUPLE_EAT_4)(o(224, s), p, o, m)
# define MSGPACK_PP_FOR_224(s, p, o, m) MSGPACK_PP_IF(p(225, s), m, MSGPACK_PP_TUPLE_EAT_2)(225, s) MSGPACK_PP_IF(p(225, s), MSGPACK_PP_FOR_225, MSGPACK_PP_TUPLE_EAT_4)(o(225, s), p, o, m)
# define MSGPACK_PP_FOR_225(s, p, o, m) MSGPACK_PP_IF(p(226, s), m, MSGPACK_PP_TUPLE_EAT_2)(226, s) MSGPACK_PP_IF(p(226, s), MSGPACK_PP_FOR_226, MSGPACK_PP_TUPLE_EAT_4)(o(226, s), p, o, m)
# define MSGPACK_PP_FOR_226(s, p, o, m) MSGPACK_PP_IF(p(227, s), m, MSGPACK_PP_TUPLE_EAT_2)(227, s) MSGPACK_PP_IF(p(227, s), MSGPACK_PP_FOR_227, MSGPACK_PP_TUPLE_EAT_4)(o(227, s), p, o, m)
# define MSGPACK_PP_FOR_227(s, p, o, m) MSGPACK_PP_IF(p(228, s), m, MSGPACK_PP_TUPLE_EAT_2)(228, s) MSGPACK_PP_IF(p(228, s), MSGPACK_PP_FOR_228, MSGPACK_PP_TUPLE_EAT_4)(o(228, s), p, o, m)
# define MSGPACK_PP_FOR_228(s, p, o, m) MSGPACK_PP_IF(p(229, s), m, MSGPACK_PP_TUPLE_EAT_2)(229, s) MSGPACK_PP_IF(p(229, s), MSGPACK_PP_FOR_229, MSGPACK_PP_TUPLE_EAT_4)(o(229, s), p, o, m)
# define MSGPACK_PP_FOR_229(s, p, o, m) MSGPACK_PP_IF(p(230, s), m, MSGPACK_PP_TUPLE_EAT_2)(230, s) MSGPACK_PP_IF(p(230, s), MSGPACK_PP_FOR_230, MSGPACK_PP_TUPLE_EAT_4)(o(230, s), p, o, m)
# define MSGPACK_PP_FOR_230(s, p, o, m) MSGPACK_PP_IF(p(231, s), m, MSGPACK_PP_TUPLE_EAT_2)(231, s) MSGPACK_PP_IF(p(231, s), MSGPACK_PP_FOR_231, MSGPACK_PP_TUPLE_EAT_4)(o(231, s), p, o, m)
# define MSGPACK_PP_FOR_231(s, p, o, m) MSGPACK_PP_IF(p(232, s), m, MSGPACK_PP_TUPLE_EAT_2)(232, s) MSGPACK_PP_IF(p(232, s), MSGPACK_PP_FOR_232, MSGPACK_PP_TUPLE_EAT_4)(o(232, s), p, o, m)
# define MSGPACK_PP_FOR_232(s, p, o, m) MSGPACK_PP_IF(p(233, s), m, MSGPACK_PP_TUPLE_EAT_2)(233, s) MSGPACK_PP_IF(p(233, s), MSGPACK_PP_FOR_233, MSGPACK_PP_TUPLE_EAT_4)(o(233, s), p, o, m)
# define MSGPACK_PP_FOR_233(s, p, o, m) MSGPACK_PP_IF(p(234, s), m, MSGPACK_PP_TUPLE_EAT_2)(234, s) MSGPACK_PP_IF(p(234, s), MSGPACK_PP_FOR_234, MSGPACK_PP_TUPLE_EAT_4)(o(234, s), p, o, m)
# define MSGPACK_PP_FOR_234(s, p, o, m) MSGPACK_PP_IF(p(235, s), m, MSGPACK_PP_TUPLE_EAT_2)(235, s) MSGPACK_PP_IF(p(235, s), MSGPACK_PP_FOR_235, MSGPACK_PP_TUPLE_EAT_4)(o(235, s), p, o, m)
# define MSGPACK_PP_FOR_235(s, p, o, m) MSGPACK_PP_IF(p(236, s), m, MSGPACK_PP_TUPLE_EAT_2)(236, s) MSGPACK_PP_IF(p(236, s), MSGPACK_PP_FOR_236, MSGPACK_PP_TUPLE_EAT_4)(o(236, s), p, o, m)
# define MSGPACK_PP_FOR_236(s, p, o, m) MSGPACK_PP_IF(p(237, s), m, MSGPACK_PP_TUPLE_EAT_2)(237, s) MSGPACK_PP_IF(p(237, s), MSGPACK_PP_FOR_237, MSGPACK_PP_TUPLE_EAT_4)(o(237, s), p, o, m)
# define MSGPACK_PP_FOR_237(s, p, o, m) MSGPACK_PP_IF(p(238, s), m, MSGPACK_PP_TUPLE_EAT_2)(238, s) MSGPACK_PP_IF(p(238, s), MSGPACK_PP_FOR_238, MSGPACK_PP_TUPLE_EAT_4)(o(238, s), p, o, m)
# define MSGPACK_PP_FOR_238(s, p, o, m) MSGPACK_PP_IF(p(239, s), m, MSGPACK_PP_TUPLE_EAT_2)(239, s) MSGPACK_PP_IF(p(239, s), MSGPACK_PP_FOR_239, MSGPACK_PP_TUPLE_EAT_4)(o(239, s), p, o, m)
# define MSGPACK_PP_FOR_239(s, p, o, m) MSGPACK_PP_IF(p(240, s), m, MSGPACK_PP_TUPLE_EAT_2)(240, s) MSGPACK_PP_IF(p(240, s), MSGPACK_PP_FOR_240, MSGPACK_PP_TUPLE_EAT_4)(o(240, s), p, o, m)
# define MSGPACK_PP_FOR_240(s, p, o, m) MSGPACK_PP_IF(p(241, s), m, MSGPACK_PP_TUPLE_EAT_2)(241, s) MSGPACK_PP_IF(p(241, s), MSGPACK_PP_FOR_241, MSGPACK_PP_TUPLE_EAT_4)(o(241, s), p, o, m)
# define MSGPACK_PP_FOR_241(s, p, o, m) MSGPACK_PP_IF(p(242, s), m, MSGPACK_PP_TUPLE_EAT_2)(242, s) MSGPACK_PP_IF(p(242, s), MSGPACK_PP_FOR_242, MSGPACK_PP_TUPLE_EAT_4)(o(242, s), p, o, m)
# define MSGPACK_PP_FOR_242(s, p, o, m) MSGPACK_PP_IF(p(243, s), m, MSGPACK_PP_TUPLE_EAT_2)(243, s) MSGPACK_PP_IF(p(243, s), MSGPACK_PP_FOR_243, MSGPACK_PP_TUPLE_EAT_4)(o(243, s), p, o, m)
# define MSGPACK_PP_FOR_243(s, p, o, m) MSGPACK_PP_IF(p(244, s), m, MSGPACK_PP_TUPLE_EAT_2)(244, s) MSGPACK_PP_IF(p(244, s), MSGPACK_PP_FOR_244, MSGPACK_PP_TUPLE_EAT_4)(o(244, s), p, o, m)
# define MSGPACK_PP_FOR_244(s, p, o, m) MSGPACK_PP_IF(p(245, s), m, MSGPACK_PP_TUPLE_EAT_2)(245, s) MSGPACK_PP_IF(p(245, s), MSGPACK_PP_FOR_245, MSGPACK_PP_TUPLE_EAT_4)(o(245, s), p, o, m)
# define MSGPACK_PP_FOR_245(s, p, o, m) MSGPACK_PP_IF(p(246, s), m, MSGPACK_PP_TUPLE_EAT_2)(246, s) MSGPACK_PP_IF(p(246, s), MSGPACK_PP_FOR_246, MSGPACK_PP_TUPLE_EAT_4)(o(246, s), p, o, m)
# define MSGPACK_PP_FOR_246(s, p, o, m) MSGPACK_PP_IF(p(247, s), m, MSGPACK_PP_TUPLE_EAT_2)(247, s) MSGPACK_PP_IF(p(247, s), MSGPACK_PP_FOR_247, MSGPACK_PP_TUPLE_EAT_4)(o(247, s), p, o, m)
# define MSGPACK_PP_FOR_247(s, p, o, m) MSGPACK_PP_IF(p(248, s), m, MSGPACK_PP_TUPLE_EAT_2)(248, s) MSGPACK_PP_IF(p(248, s), MSGPACK_PP_FOR_248, MSGPACK_PP_TUPLE_EAT_4)(o(248, s), p, o, m)
# define MSGPACK_PP_FOR_248(s, p, o, m) MSGPACK_PP_IF(p(249, s), m, MSGPACK_PP_TUPLE_EAT_2)(249, s) MSGPACK_PP_IF(p(249, s), MSGPACK_PP_FOR_249, MSGPACK_PP_TUPLE_EAT_4)(o(249, s), p, o, m)
# define MSGPACK_PP_FOR_249(s, p, o, m) MSGPACK_PP_IF(p(250, s), m, MSGPACK_PP_TUPLE_EAT_2)(250, s) MSGPACK_PP_IF(p(250, s), MSGPACK_PP_FOR_250, MSGPACK_PP_TUPLE_EAT_4)(o(250, s), p, o, m)
# define MSGPACK_PP_FOR_250(s, p, o, m) MSGPACK_PP_IF(p(251, s), m, MSGPACK_PP_TUPLE_EAT_2)(251, s) MSGPACK_PP_IF(p(251, s), MSGPACK_PP_FOR_251, MSGPACK_PP_TUPLE_EAT_4)(o(251, s), p, o, m)
# define MSGPACK_PP_FOR_251(s, p, o, m) MSGPACK_PP_IF(p(252, s), m, MSGPACK_PP_TUPLE_EAT_2)(252, s) MSGPACK_PP_IF(p(252, s), MSGPACK_PP_FOR_252, MSGPACK_PP_TUPLE_EAT_4)(o(252, s), p, o, m)
# define MSGPACK_PP_FOR_252(s, p, o, m) MSGPACK_PP_IF(p(253, s), m, MSGPACK_PP_TUPLE_EAT_2)(253, s) MSGPACK_PP_IF(p(253, s), MSGPACK_PP_FOR_253, MSGPACK_PP_TUPLE_EAT_4)(o(253, s), p, o, m)
# define MSGPACK_PP_FOR_253(s, p, o, m) MSGPACK_PP_IF(p(254, s), m, MSGPACK_PP_TUPLE_EAT_2)(254, s) MSGPACK_PP_IF(p(254, s), MSGPACK_PP_FOR_254, MSGPACK_PP_TUPLE_EAT_4)(o(254, s), p, o, m)
# define MSGPACK_PP_FOR_254(s, p, o, m) MSGPACK_PP_IF(p(255, s), m, MSGPACK_PP_TUPLE_EAT_2)(255, s) MSGPACK_PP_IF(p(255, s), MSGPACK_PP_FOR_255, MSGPACK_PP_TUPLE_EAT_4)(o(255, s), p, o, m)
# define MSGPACK_PP_FOR_255(s, p, o, m) MSGPACK_PP_IF(p(256, s), m, MSGPACK_PP_TUPLE_EAT_2)(256, s) MSGPACK_PP_IF(p(256, s), MSGPACK_PP_FOR_256, MSGPACK_PP_TUPLE_EAT_4)(o(256, s), p, o, m)
# define MSGPACK_PP_FOR_256(s, p, o, m) MSGPACK_PP_IF(p(257, s), m, MSGPACK_PP_TUPLE_EAT_2)(257, s) MSGPACK_PP_IF(p(257, s), MSGPACK_PP_FOR_257, MSGPACK_PP_TUPLE_EAT_4)(o(257, s), p, o, m)
#
# endif
