<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::TypeTraits&lt; complex&lt; float &gt; &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html">TypeTraits&lt; complex&lt; float &gt; &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="type__traits_8h_source.html">type_traits.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a4e6d27a12f835434a16b0f7bc92ae0d2"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a4e6d27a12f835434a16b0f7bc92ae0d2">host_type</a></td></tr>
<tr class="separator:a4e6d27a12f835434a16b0f7bc92ae0d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a226f54f55c3a2154e89632ca81a99789"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a226f54f55c3a2154e89632ca81a99789">device_type</a></td></tr>
<tr class="separator:a226f54f55c3a2154e89632ca81a99789"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67120bdef9cad92ac289bf26fa8008d6"><td class="memItemLeft" align="right" valign="top">typedef int64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a67120bdef9cad92ac289bf26fa8008d6">integer_type</a></td></tr>
<tr class="separator:a67120bdef9cad92ac289bf26fa8008d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35bedaea951cee4a763a5ddb6a1a2995"><td class="memItemLeft" align="right" valign="top">typedef uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a35bedaea951cee4a763a5ddb6a1a2995">unsigned_type</a></td></tr>
<tr class="separator:a35bedaea951cee4a763a5ddb6a1a2995"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a9c5132250bbcfbe3e7747277b875c6bf"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a9c5132250bbcfbe3e7747277b875c6bf">remove_negative_zero</a> (<a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; x)</td></tr>
<tr class="separator:a9c5132250bbcfbe3e7747277b875c6bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41e52f63a778b0f71de346f685a62295"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a41e52f63a778b0f71de346f685a62295">to_print</a> (<a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; x)</td></tr>
<tr class="separator:a41e52f63a778b0f71de346f685a62295"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae948b70522a237f498935a5864ebac7f"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a226f54f55c3a2154e89632ca81a99789">device_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#ae948b70522a237f498935a5864ebac7f">to_device</a> (<a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; x)</td></tr>
<tr class="separator:ae948b70522a237f498935a5864ebac7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a6885f2871ac12091946d8f9a833efc0e"><td class="memItemLeft" align="right" valign="top">static cudaDataType_t const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a6885f2871ac12091946d8f9a833efc0e">cublas_type</a> = CUDA_C_32F</td></tr>
<tr class="separator:a6885f2871ac12091946d8f9a833efc0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a226f54f55c3a2154e89632ca81a99789"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt; <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a226f54f55c3a2154e89632ca81a99789">device_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4e6d27a12f835434a16b0f7bc92ae0d2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt; <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a4e6d27a12f835434a16b0f7bc92ae0d2">host_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a67120bdef9cad92ac289bf26fa8008d6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef int64_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a67120bdef9cad92ac289bf26fa8008d6">integer_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a35bedaea951cee4a763a5ddb6a1a2995"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef uint64_t <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::<a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a35bedaea951cee4a763a5ddb6a1a2995">unsigned_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a9c5132250bbcfbe3e7747277b875c6bf"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt; <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::remove_negative_zero </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae948b70522a237f498935a5864ebac7f"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a226f54f55c3a2154e89632ca81a99789">device_type</a> <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::to_device </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a41e52f63a778b0f71de346f685a62295"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt; <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::to_print </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a6885f2871ac12091946d8f9a833efc0e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">cudaDataType_t const <a class="el" href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; float &gt; &gt;::cublas_type = CUDA_C_32F</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="type__traits_8h_source.html">type_traits.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
