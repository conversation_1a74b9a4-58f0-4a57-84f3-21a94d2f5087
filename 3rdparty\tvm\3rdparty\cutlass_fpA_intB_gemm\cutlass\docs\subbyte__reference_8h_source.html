<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: subbyte_reference.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">subbyte_reference.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="subbyte__reference_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  <span class="keyword">typename</span> Element_,              </div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">typename</span> Storage_ = uint8_t     </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;&gt;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html">   60</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> {</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">   63</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> = Element_;</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">   64</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> = Storage_;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">   65</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a> = <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> <span class="keyword">const</span> *;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> &lt;= <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a>,</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;    <span class="stringliteral">&quot;Size of Element must not be greater than Storage.&quot;</span>);</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(!(<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a> % <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>),</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <span class="stringliteral">&quot;Storage must be divisible by Element&quot;</span>);</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerVector = <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a> / <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> <span class="keyword">const</span> kMask = </div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    ((<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> &lt; <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a>) ? </div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;      (<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>(1) &lt;&lt; <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>) - <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>(1) :</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;      ~<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>(0));</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a> ptr_;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  <span class="keywordtype">int</span> offset_;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#aa00016fe6dafa323e9875be4287fbfe5">   97</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#aa00016fe6dafa323e9875be4287fbfe5">ConstSubbyteReference</a>(): ptr_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>), offset_(0) { }</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a158ae5a484751f274c083807b4a37868">  101</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a158ae5a484751f274c083807b4a37868">ConstSubbyteReference</a>(</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> <span class="keyword">const</span> *ptr,           </div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    int64_t offset          </div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  ): </div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    ptr_(reinterpret_cast&lt;<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a>&gt;(ptr)),</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    offset_(0) {</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    int64_t offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    int64_t offset_in_elements = offset % kElementsPerVector;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    ptr_ += offset_in_vectors;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;    offset_ = int(offset_in_elements);</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  }</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#adfefff5e63632fcdc4f59e21dccea16d">  117</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#adfefff5e63632fcdc4f59e21dccea16d">ConstSubbyteReference</a>(</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> *ptr = <span class="keyword">nullptr</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  ): <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>(ptr, 0) { }</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00123"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#aa76e4dd207d7405868ebba3f2e121c1e">  123</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#aa76e4dd207d7405868ebba3f2e121c1e">storage_pointer</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    <span class="keywordflow">return</span> ptr_;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  }</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00129"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a7ff8cfd6a308811ae197b0eb704bdc24">  129</a></span>&#160;  <span class="keywordtype">int</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a7ff8cfd6a308811ae197b0eb704bdc24">element_offset</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    <span class="keywordflow">return</span> offset_;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  }</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00135"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#ae5af3bf12950795fdc96c1e65db31776">  135</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> <span class="keyword">get</span>() <span class="keyword">const</span> {</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> item = <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>((*ptr_ &gt;&gt; (offset_ * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>)) &amp; kMask);</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(item);</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  }</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a908a341f883db06f529536fae196a7c9">  142</a></span>&#160;  <span class="keyword">operator</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">get</span>();</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  }</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#ad7e5cf02325b590fffa2fc5bfcb9da09">  148</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#ad7e5cf02325b590fffa2fc5bfcb9da09">operator+=</a>(<span class="keywordtype">int</span> offset) {</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    offset += offset_;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    </div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    <span class="keywordtype">int</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = offset % kElementsPerVector;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    ptr_ += offset_in_vectors;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    offset_ = offset_in_elements;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;  }</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a712b16abc1305ae5fb1c57bd25f89a6b">  163</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a712b16abc1305ae5fb1c57bd25f89a6b">operator+=</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset) {</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    offset += offset_;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    </div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    <span class="keywordtype">long</span> <span class="keywordtype">long</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = int(offset % kElementsPerVector);</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    ptr_ += offset_in_vectors;</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    offset_ = offset_in_elements;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  }</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a2cb617780fc42c735fa0f997926936aa">  178</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a2cb617780fc42c735fa0f997926936aa">operator-=</a>(<span class="keywordtype">int</span> offset) {</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;    </div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <span class="keywordtype">int</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = offset % kElementsPerVector;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    ptr_ -= offset_in_vectors;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    offset_ -= offset_in_elements;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    <span class="keywordflow">if</span> (offset_ &lt; 0) {</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;      offset_ += kElementsPerVector;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;      --ptr_;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    }</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;  }</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00196"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#abc062e38c9eede21a770f22ac957dec0">  196</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#abc062e38c9eede21a770f22ac957dec0">operator-=</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset) {</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;    </div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    <span class="keywordtype">long</span> <span class="keywordtype">long</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = int(offset % kElementsPerVector);</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;    ptr_ -= offset_in_vectors;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;    offset_ -= offset_in_elements;</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    <span class="keywordflow">if</span> (offset_ &lt; 0) {</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;      offset_ += kElementsPerVector;</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;      --ptr_;</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    }</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;  }</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00214"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a0afaee4126a794f9db58ed4bd079b792">  214</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a0afaee4126a794f9db58ed4bd079b792">operator+</a>(<span class="keywordtype">int</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;    ref += offset;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;  }</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a3a035824f267fecb8cfc0848904cc4ab">  224</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3a035824f267fecb8cfc0848904cc4ab">operator+</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    </div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    ref += offset;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;  }</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00234"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#aeb37aceee94bbef99217d011b28d89f8">  234</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#aeb37aceee94bbef99217d011b28d89f8">operator-</a>(<span class="keywordtype">int</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;    <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    ref -= offset;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  }</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00244"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a621c30a0ac6469084dc16c930e0d0213">  244</a></span>&#160;  <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a621c30a0ac6469084dc16c930e0d0213">operator-=</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;    <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    ref -= offset;</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;  }</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00254"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a161e7783a83a271735f753f21348314c">  254</a></span>&#160;  ptrdiff_t <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a161e7783a83a271735f753f21348314c">operator-</a>(<a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> ref)<span class="keyword"> const </span>{</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    <span class="keywordflow">return</span> (ptr_ - ref.ptr_) * kElementsPerVector + (offset_ - ref.offset_);</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;  }</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00260"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#abe87d46c924861d6a9e2b06d2d4d69cc">  260</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> int()<span class="keyword"> const </span>{</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    <span class="keywordflow">return</span> int(<span class="keyword">get</span>());</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;  }</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00266"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#ad47d3da6dca44d5a8c821f63ca37ded7">  266</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> int64_t()<span class="keyword"> const </span>{</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;    <span class="keywordflow">return</span> int64_t(<span class="keyword">get</span>());</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;  }</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00272"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#abd60b998269ea5f771d2f51ed736d3c1">  272</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> uint64_t()<span class="keyword"> const </span>{</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;    <span class="keywordflow">return</span> uint64_t(<span class="keyword">get</span>());</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;  }</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00278"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#ae794d96fa19581472489bece95fcf344">  278</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> float()<span class="keyword"> const </span>{</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;    <span class="keywordflow">return</span> float(<span class="keyword">get</span>());</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;  }</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00284"></a><span class="lineno"><a class="line" href="classcutlass_1_1ConstSubbyteReference.html#a5e735e155826e5c2a0e33b0a456cc5ab">  284</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> double()<span class="keyword"> const </span>{</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;    <span class="keywordflow">return</span> double(<span class="keyword">get</span>());</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;  }</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;};</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;  <span class="keyword">typename</span> Element_,              </div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;  <span class="keyword">typename</span> Storage_ = uint8_t     </div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;&gt;</div><div class="line"><a name="l00294"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html">  294</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> {</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;</div><div class="line"><a name="l00297"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">  297</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">Element</a> = Element_;</div><div class="line"><a name="l00298"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">  298</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a> = Storage_;</div><div class="line"><a name="l00299"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#ac6362bcab5ecefd93fe0c18dac575ab7">  299</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1SubbyteReference.html#ac6362bcab5ecefd93fe0c18dac575ab7">StoragePointer</a> = <a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a> *;</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> &lt;= <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a>,</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    <span class="stringliteral">&quot;Size of Element must not be greater than Storage.&quot;</span>);</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(!(<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a> % <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>),</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;    <span class="stringliteral">&quot;Storage must be divisible by Element&quot;</span>);</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;  <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerVector = <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a> / <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>;</div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a> <span class="keyword">const</span> kMask = </div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    ((<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> &lt; <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Storage&gt;::value</a>) ? </div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;      (<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>(1) &lt;&lt; <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>) - <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>(1) :</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;      ~<a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a>(0));</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html#ac6362bcab5ecefd93fe0c18dac575ab7">StoragePointer</a> ptr_;</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;  <span class="keywordtype">int</span> offset_;</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00331"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a13d822702d6f45bee2fec18a00ffce7f">  331</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html#a13d822702d6f45bee2fec18a00ffce7f">SubbyteReference</a>(): ptr_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>), offset_(0) { }</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00335"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a70eb04ae3bf4ef29b77ff15f3a028d9f">  335</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html#a70eb04ae3bf4ef29b77ff15f3a028d9f">SubbyteReference</a>(</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">Element</a> *ptr,           </div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;    int64_t offset          </div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;  ): </div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;    ptr_(reinterpret_cast&lt;<a class="code" href="classcutlass_1_1SubbyteReference.html#ac6362bcab5ecefd93fe0c18dac575ab7">StoragePointer</a>&gt;(ptr)),</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;    offset_(0) {</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;    int64_t offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;    int64_t offset_in_elements = offset % kElementsPerVector;</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;    ptr_ += offset_in_vectors;</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;    offset_ = int(offset_in_elements);</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  }</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00351"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a5b4772a1b1a4e17a8d7ac7987fcfa0e3">  351</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html#a5b4772a1b1a4e17a8d7ac7987fcfa0e3">SubbyteReference</a>(</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">Element</a> *ptr = <span class="keyword">nullptr</span></div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;  ): <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a>(ptr, 0) { }</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00357"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a724014edaf1dc888343215d22a1ef6f3">  357</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html#ac6362bcab5ecefd93fe0c18dac575ab7">StoragePointer</a> <a class="code" href="classcutlass_1_1SubbyteReference.html#a724014edaf1dc888343215d22a1ef6f3">storage_pointer</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;    <span class="keywordflow">return</span> ptr_;</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  }</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00363"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a7183c6e7f6e1da815da827d789d012c6">  363</a></span>&#160;  <span class="keywordtype">int</span> <a class="code" href="classcutlass_1_1SubbyteReference.html#a7183c6e7f6e1da815da827d789d012c6">element_offset</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;    <span class="keywordflow">return</span> offset_;</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  }</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00369"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a284ab4f025b7ae2d1b0cbff5e79b6f98">  369</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">Element</a> <span class="keyword">get</span>() <span class="keyword">const</span> {</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a> item = <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>((*ptr_ &gt;&gt; (offset_ * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>)) &amp; kMask);</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">Element</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(item);</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;  }</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00376"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a6473e57520d8ee7afbd95c1e1641e05a">  376</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp; <span class="keyword">set</span>(<a class="code" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">Element</a> <span class="keyword">const</span> &amp;x) {</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a> item = (<span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(x) &amp; kMask);</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">Storage</a> kUpdateMask = <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>(~(kMask &lt;&lt; (offset_ * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a>)));</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;    *ptr_ = <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>((*ptr_ &amp; kUpdateMask) | <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a>(item &lt;&lt; (offset_ * sizeof_bits&lt;Element&gt;::value)));</div><div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  }</div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00388"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a3989d8d0402257fa1fa723bc730a8219">  388</a></span>&#160;  <span class="keyword">operator</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">get</span>();</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  }</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;</div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00394"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a6eb29d35b4536cfa7e0d351bd49cf04c">  394</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1SubbyteReference.html#a6eb29d35b4536cfa7e0d351bd49cf04c">operator=</a>(<a class="code" href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">Element</a> <span class="keyword">const</span> &amp; x) {</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">set</span>(x);</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  }</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00400"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a28ebc60e1bd1245b7778f37b26e1db83">  400</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1SubbyteReference.html#a28ebc60e1bd1245b7778f37b26e1db83">operator=</a>(<a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> <span class="keyword">const</span> &amp; x) {</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">set</span>(x.<a class="code" href="classcutlass_1_1SubbyteReference.html#a284ab4f025b7ae2d1b0cbff5e79b6f98">get</a>());</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;  }</div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div><div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00406"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a6ef119bf3509b6a103b9eac705341cb4">  406</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1SubbyteReference.html#a6ef119bf3509b6a103b9eac705341cb4">operator=</a>(</div><div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;      <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference&lt;Element, Storage&gt;</a> <span class="keyword">const</span> &amp;x) {</div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">set</span>(x.<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#ae5af3bf12950795fdc96c1e65db31776">get</a>());</div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;  }</div><div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;</div><div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00413"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#ac580da95d9109736c3a091ee3b0340f9">  413</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1SubbyteReference.html#ac580da95d9109736c3a091ee3b0340f9">operator+=</a>(<span class="keywordtype">int</span> offset) {</div><div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;    offset += offset_;</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;    </div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;    <span class="keywordtype">int</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = offset % kElementsPerVector;</div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;</div><div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;    ptr_ += offset_in_vectors;</div><div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;    offset_ = offset_in_elements;</div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;  }</div><div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;</div><div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00428"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a791be893706aa8f89485526c6c8b46aa">  428</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1SubbyteReference.html#a791be893706aa8f89485526c6c8b46aa">operator+=</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset) {</div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div><div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;    offset += offset_;</div><div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;    </div><div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;    <span class="keywordtype">long</span> <span class="keywordtype">long</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = int(offset % kElementsPerVector);</div><div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;</div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;    ptr_ += offset_in_vectors;</div><div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;    offset_ = offset_in_elements;</div><div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;</div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;  }</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00443"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a13f9f982b1ef3fa1bc2929488a4799c6">  443</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1SubbyteReference.html#a13f9f982b1ef3fa1bc2929488a4799c6">operator-=</a>(<span class="keywordtype">int</span> offset) {</div><div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;    </div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;    <span class="keywordtype">int</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = offset % kElementsPerVector;</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;</div><div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;    ptr_ -= offset_in_vectors;</div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;    offset_ -= offset_in_elements;</div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;</div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;    <span class="keywordflow">if</span> (offset_ &lt; 0) {</div><div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;      offset_ += kElementsPerVector;</div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;      --ptr_;</div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;    }</div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;</div><div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;  }</div><div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;</div><div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00461"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a1f3a56c15363b7287665c2bd93a66ffd">  461</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> &amp;<a class="code" href="classcutlass_1_1SubbyteReference.html#a1f3a56c15363b7287665c2bd93a66ffd">operator-=</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset) {</div><div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;    </div><div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;    <span class="keywordtype">long</span> <span class="keywordtype">long</span> offset_in_vectors = offset / kElementsPerVector;</div><div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;    <span class="keywordtype">int</span> offset_in_elements = int(offset % kElementsPerVector);</div><div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;</div><div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;    ptr_ -= offset_in_vectors;</div><div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;    offset_ -= offset_in_elements;</div><div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;</div><div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;    <span class="keywordflow">if</span> (offset_ &lt; 0) {</div><div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;      offset_ += kElementsPerVector;</div><div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;      --ptr_;</div><div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;    }</div><div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;</div><div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;  }</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;</div><div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00479"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a3b58e357232b7c5222628fe871f23efe">  479</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> <a class="code" href="classcutlass_1_1SubbyteReference.html#a3b58e357232b7c5222628fe871f23efe">operator+</a>(<span class="keywordtype">int</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;</div><div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;    ref += offset;</div><div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;</div><div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;  }</div><div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;</div><div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00489"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a78f12f3b851925e37a8342f23b760139">  489</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> <a class="code" href="classcutlass_1_1SubbyteReference.html#a78f12f3b851925e37a8342f23b760139">operator+</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;    </div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;    ref += offset;</div><div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;</div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;  }</div><div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00499"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a7b548b80615296b7298d3911a5521032">  499</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> <a class="code" href="classcutlass_1_1SubbyteReference.html#a7b548b80615296b7298d3911a5521032">operator-</a>(<span class="keywordtype">int</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;</div><div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;    ref -= offset;</div><div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;</div><div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;  }</div><div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;</div><div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00509"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a99470c63f4f85471cf265ea0271475b3">  509</a></span>&#160;  <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> <a class="code" href="classcutlass_1_1SubbyteReference.html#a99470c63f4f85471cf265ea0271475b3">operator-=</a>(<span class="keywordtype">long</span> <span class="keywordtype">long</span> offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;</div><div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> ref(ptr_, offset_);</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;    ref -= offset;</div><div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;</div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;    <span class="keywordflow">return</span> ref;</div><div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;  }</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00519"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a2bc07d79e2e34076254ed88663809eed">  519</a></span>&#160;  ptrdiff_t <a class="code" href="classcutlass_1_1SubbyteReference.html#a2bc07d79e2e34076254ed88663809eed">operator-</a>(<a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference</a> ref)<span class="keyword"> const </span>{</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;    <span class="keywordflow">return</span> (ptr_ - ref.ptr_) * kElementsPerVector + (offset_ - ref.offset_);</div><div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;  }</div><div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;</div><div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00525"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#aa7cf6337b97cf7d07ee2f7efef1c6c82">  525</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> int()<span class="keyword"> const </span>{</div><div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;    <span class="keywordflow">return</span> int(<span class="keyword">get</span>());</div><div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;  }</div><div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;</div><div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00531"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#aac7fd7d470166f68da0a882c3f965e5c">  531</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> int64_t()<span class="keyword"> const </span>{</div><div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;    <span class="keywordflow">return</span> int64_t(<span class="keyword">get</span>());</div><div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;  }</div><div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;</div><div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00537"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a31b1157e88da26c11944ea8aa0d33a14">  537</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> uint64_t()<span class="keyword"> const </span>{</div><div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;    <span class="keywordflow">return</span> uint64_t(<span class="keyword">get</span>());</div><div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;  }</div><div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;</div><div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00543"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a31c36b7beb7e70079e9264be19c74544">  543</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> float()<span class="keyword"> const </span>{</div><div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;    <span class="keywordflow">return</span> float(<span class="keyword">get</span>());</div><div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;  }</div><div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;</div><div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00549"></a><span class="lineno"><a class="line" href="classcutlass_1_1SubbyteReference.html#a7b2d1ddc31b0e9a8f128c775e99b1ba9">  549</a></span>&#160;  <span class="keyword">explicit</span> <span class="keyword">operator</span> double()<span class="keyword"> const </span>{</div><div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;    <span class="keywordflow">return</span> double(<span class="keyword">get</span>());</div><div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;  }</div><div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;};</div><div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;</div><div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;</div><div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;template &lt;typename Element, bool subbyte = (sizeof_bits&lt;Element&gt;::value &lt; 8)&gt;</div><div class="line"><a name="l00557"></a><span class="lineno"><a class="line" href="structcutlass_1_1ReferenceFactory.html">  557</a></span>&#160;<span class="keyword">struct</span> <a class="code" href="structcutlass_1_1ReferenceFactory.html">ReferenceFactory</a>;</div><div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;</div><div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Element&gt;</div><div class="line"><a name="l00560"></a><span class="lineno"><a class="line" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01false_01_4.html">  560</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1ReferenceFactory.html">ReferenceFactory</a>&lt;<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a>, false&gt; {</div><div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00562"></a><span class="lineno"><a class="line" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01false_01_4.html#a476101ee27000f24d9d86b2080bdd551">  562</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> &amp;<span class="keyword">get</span>(<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> *ptr, int64_t offset) {</div><div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;    <span class="keywordflow">return</span> ptr[offset];</div><div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;  }</div><div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;</div><div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00567"></a><span class="lineno"><a class="line" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01false_01_4.html#a64b79849b476b2926fb5d152f01eb2a8">  567</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> <span class="keyword">const</span> &amp;<span class="keyword">get</span>(<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> <span class="keyword">const</span> *ptr, int64_t offset) {</div><div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;    <span class="keywordflow">return</span> ptr[offset];</div><div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;  }</div><div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;};</div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;</div><div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Element&gt;</div><div class="line"><a name="l00573"></a><span class="lineno"><a class="line" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01true_01_4.html">  573</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1ReferenceFactory.html">ReferenceFactory</a>&lt;<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a>, true&gt; {</div><div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00575"></a><span class="lineno"><a class="line" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01true_01_4.html#a33d06a48e057013200ef7f806535bad7">  575</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference&lt;Element&gt;</a> <span class="keyword">get</span>(<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> *ptr, int64_t offset) {</div><div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference&lt;Element&gt;</a>(ptr, offset);</div><div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;  }</div><div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;</div><div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00580"></a><span class="lineno"><a class="line" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01true_01_4.html#a82e2df24803b3cf769400115ca232b14">  580</a></span>&#160;  <span class="keyword">static</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference&lt;Element&gt;</a> <span class="keyword">get</span>(<a class="code" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> <span class="keyword">const</span> *ptr,</div><div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;                                             int64_t offset) {</div><div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference&lt;Element&gt;</a>(ptr, offset);</div><div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;  }</div><div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;};</div><div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;</div><div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;</div><div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a></div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:60</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a7183c6e7f6e1da815da827d789d012c6"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a7183c6e7f6e1da815da827d789d012c6">cutlass::SubbyteReference::element_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE int element_offset() const </div><div class="ttdoc">Gets element offset within storage vector. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:363</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a6eb29d35b4536cfa7e0d351bd49cf04c"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a6eb29d35b4536cfa7e0d351bd49cf04c">cutlass::SubbyteReference::operator=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference &amp; operator=(Element const &amp;x)</div><div class="ttdoc">Stores an element to memory. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:394</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a13d822702d6f45bee2fec18a00ffce7f"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a13d822702d6f45bee2fec18a00ffce7f">cutlass::SubbyteReference::SubbyteReference</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference()</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:331</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a99470c63f4f85471cf265ea0271475b3"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a99470c63f4f85471cf265ea0271475b3">cutlass::SubbyteReference::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference operator-=(long long offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:509</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a70eb04ae3bf4ef29b77ff15f3a028d9f"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a70eb04ae3bf4ef29b77ff15f3a028d9f">cutlass::SubbyteReference::SubbyteReference</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference(Element *ptr, int64_t offset)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:335</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a3b58e357232b7c5222628fe871f23efe"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a3b58e357232b7c5222628fe871f23efe">cutlass::SubbyteReference::operator+</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference operator+(int offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:479</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a621c30a0ac6469084dc16c930e0d0213"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a621c30a0ac6469084dc16c930e0d0213">cutlass::ConstSubbyteReference::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference operator-=(long long offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:244</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_af6eaafa32796df610701bdd3c9e5aa45"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">cutlass::ConstSubbyteReference::Storage</a></div><div class="ttdeci">Storage_ Storage</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:64</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a712b16abc1305ae5fb1c57bd25f89a6b"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a712b16abc1305ae5fb1c57bd25f89a6b">cutlass::ConstSubbyteReference::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference &amp; operator+=(long long offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:163</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a78f12f3b851925e37a8342f23b760139"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a78f12f3b851925e37a8342f23b760139">cutlass::SubbyteReference::operator+</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference operator+(long long offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:489</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a161e7783a83a271735f753f21348314c"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a161e7783a83a271735f753f21348314c">cutlass::ConstSubbyteReference::operator-</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ptrdiff_t operator-(ConstSubbyteReference ref) const </div><div class="ttdoc">Computes the difference in elements between references. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:254</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_ad7e5cf02325b590fffa2fc5bfcb9da09"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#ad7e5cf02325b590fffa2fc5bfcb9da09">cutlass::ConstSubbyteReference::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference &amp; operator+=(int offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:148</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a1f3a56c15363b7287665c2bd93a66ffd"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a1f3a56c15363b7287665c2bd93a66ffd">cutlass::SubbyteReference::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference &amp; operator-=(long long offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:461</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html">cutlass::sizeof_bits</a></div><div class="ttdoc">Defines the size of an element in bits. </div><div class="ttdef"><b>Definition:</b> numeric_types.h:42</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_ae5af3bf12950795fdc96c1e65db31776"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#ae5af3bf12950795fdc96c1e65db31776">cutlass::ConstSubbyteReference::get</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Element get() const </div><div class="ttdoc">Unpacks an element from memory. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:135</div></div>
<div class="ttc" id="platform_8h_html_ab979d9d4b4923f7c54d6caa6e1a61936"><div class="ttname"><a href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a></div><div class="ttdeci">#define nullptr</div><div class="ttdoc">nullptr </div><div class="ttdef"><b>Definition:</b> platform.h:144</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_af307e4fecb7092a7c77ccdb66dcd63ff"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#af307e4fecb7092a7c77ccdb66dcd63ff">cutlass::SubbyteReference::Element</a></div><div class="ttdeci">Element_ Element</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:297</div></div>
<div class="ttc" id="structcutlass_1_1ReferenceFactory_html"><div class="ttname"><a href="structcutlass_1_1ReferenceFactory.html">cutlass::ReferenceFactory</a></div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:557</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_aa76e4dd207d7405868ebba3f2e121c1e"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#aa76e4dd207d7405868ebba3f2e121c1e">cutlass::ConstSubbyteReference::storage_pointer</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE StoragePointer storage_pointer() const </div><div class="ttdoc">Gets storage pointer. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:123</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a791be893706aa8f89485526c6c8b46aa"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a791be893706aa8f89485526c6c8b46aa">cutlass::SubbyteReference::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference &amp; operator+=(long long offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:428</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a724014edaf1dc888343215d22a1ef6f3"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a724014edaf1dc888343215d22a1ef6f3">cutlass::SubbyteReference::storage_pointer</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE StoragePointer storage_pointer() const </div><div class="ttdoc">Gets storage pointer. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:357</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a158ae5a484751f274c083807b4a37868"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a158ae5a484751f274c083807b4a37868">cutlass::ConstSubbyteReference::ConstSubbyteReference</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference(Element const *ptr, int64_t offset)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:101</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a0afaee4126a794f9db58ed4bd079b792"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a0afaee4126a794f9db58ed4bd079b792">cutlass::ConstSubbyteReference::operator+</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference operator+(int offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:214</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a2cb617780fc42c735fa0f997926936aa"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a2cb617780fc42c735fa0f997926936aa">cutlass::ConstSubbyteReference::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference &amp; operator-=(int offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:178</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_aa00016fe6dafa323e9875be4287fbfe5"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#aa00016fe6dafa323e9875be4287fbfe5">cutlass::ConstSubbyteReference::ConstSubbyteReference</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference()</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:97</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a3a035824f267fecb8cfc0848904cc4ab"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a3a035824f267fecb8cfc0848904cc4ab">cutlass::ConstSubbyteReference::operator+</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference operator+(long long offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:224</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a28ebc60e1bd1245b7778f37b26e1db83"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a28ebc60e1bd1245b7778f37b26e1db83">cutlass::SubbyteReference::operator=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference &amp; operator=(SubbyteReference const &amp;x)</div><div class="ttdoc">Stores an element to memory. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:400</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a2e4e5d5c300066b8a7e4d48805f294a2"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a2e4e5d5c300066b8a7e4d48805f294a2">cutlass::SubbyteReference::Storage</a></div><div class="ttdeci">Storage_ Storage</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:298</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_adfefff5e63632fcdc4f59e21dccea16d"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#adfefff5e63632fcdc4f59e21dccea16d">cutlass::ConstSubbyteReference::ConstSubbyteReference</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference(Element *ptr=nullptr)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:117</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html">cutlass::SubbyteReference</a></div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:294</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a7ff8cfd6a308811ae197b0eb704bdc24"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a7ff8cfd6a308811ae197b0eb704bdc24">cutlass::ConstSubbyteReference::element_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE int element_offset() const </div><div class="ttdoc">Gets element offset within storage vector. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:129</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a284ab4f025b7ae2d1b0cbff5e79b6f98"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a284ab4f025b7ae2d1b0cbff5e79b6f98">cutlass::SubbyteReference::get</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Element get() const </div><div class="ttdoc">Unpacks an element from memory. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:369</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a6ef119bf3509b6a103b9eac705341cb4"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a6ef119bf3509b6a103b9eac705341cb4">cutlass::SubbyteReference::operator=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference &amp; operator=(ConstSubbyteReference&lt; Element, Storage &gt; const &amp;x)</div><div class="ttdoc">Stores an element to memory. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:406</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_ac6362bcab5ecefd93fe0c18dac575ab7"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#ac6362bcab5ecefd93fe0c18dac575ab7">cutlass::SubbyteReference::StoragePointer</a></div><div class="ttdeci">Storage * StoragePointer</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:299</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_afe79398d7625d244f130867a9a25dddc"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">cutlass::ConstSubbyteReference::StoragePointer</a></div><div class="ttdeci">Storage const * StoragePointer</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:65</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a5b4772a1b1a4e17a8d7ac7987fcfa0e3"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a5b4772a1b1a4e17a8d7ac7987fcfa0e3">cutlass::SubbyteReference::SubbyteReference</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference(Element *ptr=nullptr)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:351</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a13f9f982b1ef3fa1bc2929488a4799c6"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a13f9f982b1ef3fa1bc2929488a4799c6">cutlass::SubbyteReference::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference &amp; operator-=(int offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:443</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_a3f143b914d4d7a1dbe724d64d30bf60c"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">cutlass::ConstSubbyteReference::Element</a></div><div class="ttdeci">Element_ Element</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:63</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_abc062e38c9eede21a770f22ac957dec0"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#abc062e38c9eede21a770f22ac957dec0">cutlass::ConstSubbyteReference::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference &amp; operator-=(long long offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:196</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a7b548b80615296b7298d3911a5521032"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a7b548b80615296b7298d3911a5521032">cutlass::SubbyteReference::operator-</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference operator-(int offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:499</div></div>
<div class="ttc" id="classcutlass_1_1ConstSubbyteReference_html_aeb37aceee94bbef99217d011b28d89f8"><div class="ttname"><a href="classcutlass_1_1ConstSubbyteReference.html#aeb37aceee94bbef99217d011b28d89f8">cutlass::ConstSubbyteReference::operator-</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstSubbyteReference operator-(int offset) const </div><div class="ttdoc">Returns a reference to an element with a given offset from the current reference. ...</div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:234</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_a2bc07d79e2e34076254ed88663809eed"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#a2bc07d79e2e34076254ed88663809eed">cutlass::SubbyteReference::operator-</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ptrdiff_t operator-(SubbyteReference ref) const </div><div class="ttdoc">Computes the difference in elements between references. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:519</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html_ac580da95d9109736c3a091ee3b0340f9"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html#ac580da95d9109736c3a091ee3b0340f9">cutlass::SubbyteReference::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE SubbyteReference &amp; operator+=(int offset)</div><div class="ttdoc">Adds an offset in units of elements to the reference. </div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:413</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
