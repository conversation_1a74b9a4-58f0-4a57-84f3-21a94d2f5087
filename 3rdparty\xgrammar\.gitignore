/tmp/
*.bak
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

.DS_Store

*.S
# C extensions
*.so

build/

*.ll
.npm
# Distribution / packaging
.Python
env/
build/
build-*/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

.conda/
# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Generated by python/gen_requirements.py
python/requirements/*.txt

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
/Testing/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/
docs/_staging/

# PyBuilder
target/
/target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject
*~
*.pyc
*~
config.mk
/config.cmake
Win32
*.dir
perf
*.wasm
.emscripten

## IOS
DerivedData/

## Java
*.class
*.worksheet
*.idea
*.iml
*.classpath
*.project
*.settings
*/node_modules/

## Various settings
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
.pkl_memoize_*

.emscripten*
.m2

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

## Other
*.moved-aside
*.xccheckout
*.xcscmblueprint
.DS_Store
tags
cscope*
*.lock

# vim temporary files
*.swp
*.swo

.bash_history
# *.json
*.params
*.ro
*.onnx
*.h5

# Mac OS X
.DS_Store

# Jetbrain
.idea
.ipython
.jupyter
.nv
.pylint.d
.python_history
.pytest_cache
.local
cmake-build-debug

# Visual Studio
.vs

# Visual Studio Code
.vscode

# tmp file
.nfs*

# keys
*.pem
*.p12
*.pfx
*.cer
*.crt
*.der

# patch sentinel
patched.txt

# Python type checking
.mypy_cache/
.pyre/

# pipenv files
Pipfile
Pipfile.lock

# conda package artifacts
conda/Dockerfile.cuda*
conda/pkg
.node_repl_history
# nix files
.envrc
*.nix

# Docker files
.sudo_as_admin_successful

# Local docs build
_docs/
.config/configstore/
.ci-py-scripts/

# Used in CI to communicate between Python and Jenkins
.docker-image-names/

# GDB history file
.gdb_history
