# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_CONTROL_HPP
# define MSGPACK_PREPROCESSOR_CONTROL_HPP
#
# include <msgpack/preprocessor/control/deduce_d.hpp>
# include <msgpack/preprocessor/control/expr_if.hpp>
# include <msgpack/preprocessor/control/expr_iif.hpp>
# include <msgpack/preprocessor/control/if.hpp>
# include <msgpack/preprocessor/control/iif.hpp>
# include <msgpack/preprocessor/control/while.hpp>
#
# endif
