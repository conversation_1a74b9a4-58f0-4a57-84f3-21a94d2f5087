<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::Tensor4DCoord Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1Tensor4DCoord-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::Tensor4DCoord Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Defines a canonical 4D coordinate used by tensor operations.  
</p>

<p><code>#include &lt;<a class="el" href="tensor__coord_8h_source.html">tensor_coord.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for cutlass::Tensor4DCoord:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1Tensor4DCoord__inherit__graph.png" border="0" usemap="#cutlass_1_1Tensor4DCoord_inherit__map" alt="Inheritance graph"/></div>
<map name="cutlass_1_1Tensor4DCoord_inherit__map" id="cutlass_1_1Tensor4DCoord_inherit__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<div class="dynheader">
Collaboration diagram for cutlass::Tensor4DCoord:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1Tensor4DCoord__coll__graph.png" border="0" usemap="#cutlass_1_1Tensor4DCoord_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1Tensor4DCoord_coll__map" id="cutlass_1_1Tensor4DCoord_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a367dd7e307c7e8e2aa7396d027e8891e"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> = <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 4 &gt;</td></tr>
<tr class="memdesc:a367dd7e307c7e8e2aa7396d027e8891e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Base class.  <a href="#a367dd7e307c7e8e2aa7396d027e8891e">More...</a><br /></td></tr>
<tr class="separator:a367dd7e307c7e8e2aa7396d027e8891e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f63c0cc6b642e80624beafb6c3390a1"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> = typename <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Base::Index</a></td></tr>
<tr class="memdesc:a4f63c0cc6b642e80624beafb6c3390a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Index type.  <a href="#a4f63c0cc6b642e80624beafb6c3390a1">More...</a><br /></td></tr>
<tr class="separator:a4f63c0cc6b642e80624beafb6c3390a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a779bf9ea896ac4ae9d4def10cd23eb45"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a779bf9ea896ac4ae9d4def10cd23eb45">LongIndex</a> = typename <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">Base::LongIndex</a></td></tr>
<tr class="memdesc:a779bf9ea896ac4ae9d4def10cd23eb45"><td class="mdescLeft">&#160;</td><td class="mdescRight">LongIndex type.  <a href="#a779bf9ea896ac4ae9d4def10cd23eb45">More...</a><br /></td></tr>
<tr class="separator:a779bf9ea896ac4ae9d4def10cd23eb45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_types_structcutlass_1_1Coord"><td colspan="2" onclick="javascript:toggleInherit('pub_types_structcutlass_1_1Coord')"><img src="closed.png" alt="-"/>&#160;Public Types inherited from <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 4 &gt;</a></td></tr>
<tr class="memitem:a7a89e5661ef391dd9f4fe81f0c982b75 inherit pub_types_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> = int</td></tr>
<tr class="memdesc:a7a89e5661ef391dd9f4fe81f0c982b75 inherit pub_types_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Index type used to store elements.  <a href="#a7a89e5661ef391dd9f4fe81f0c982b75">More...</a><br /></td></tr>
<tr class="separator:a7a89e5661ef391dd9f4fe81f0c982b75 inherit pub_types_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab61db7c2bfacaf0b7ce465e70d48c44f inherit pub_types_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> = int64_t</td></tr>
<tr class="memdesc:ab61db7c2bfacaf0b7ce465e70d48c44f inherit pub_types_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type used to represent linear offsets.  <a href="#ab61db7c2bfacaf0b7ce465e70d48c44f">More...</a><br /></td></tr>
<tr class="separator:ab61db7c2bfacaf0b7ce465e70d48c44f inherit pub_types_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9a9c773b2bfec43d1722fd7a490fd436"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">Tensor4DCoord</a> ()</td></tr>
<tr class="memdesc:a9a9c773b2bfec43d1722fd7a490fd436"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default ctor.  <a href="#a9a9c773b2bfec43d1722fd7a490fd436">More...</a><br /></td></tr>
<tr class="separator:a9a9c773b2bfec43d1722fd7a490fd436"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afac3bfcfde4408c922ca25c965a40cd7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#afac3bfcfde4408c922ca25c965a40cd7">Tensor4DCoord</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 4 &gt; const &amp;coord)</td></tr>
<tr class="memdesc:afac3bfcfde4408c922ca25c965a40cd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs from <a class="el" href="structcutlass_1_1Coord.html">Coord&lt;4&gt;</a>  <a href="#afac3bfcfde4408c922ca25c965a40cd7">More...</a><br /></td></tr>
<tr class="separator:afac3bfcfde4408c922ca25c965a40cd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac1d0a33901e2bfb88eb277a594bcd0c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#aac1d0a33901e2bfb88eb277a594bcd0c">Tensor4DCoord</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>, <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>, <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>, <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>)</td></tr>
<tr class="memdesc:aac1d0a33901e2bfb88eb277a594bcd0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Helper to construct from N, H, W, and C.  <a href="#aac1d0a33901e2bfb88eb277a594bcd0c">More...</a><br /></td></tr>
<tr class="separator:aac1d0a33901e2bfb88eb277a594bcd0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fa718218c21df006b71d9325f1ddb5a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a> () const </td></tr>
<tr class="memdesc:a2fa718218c21df006b71d9325f1ddb5a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the batch of the coordinate.  <a href="#a2fa718218c21df006b71d9325f1ddb5a">More...</a><br /></td></tr>
<tr class="separator:a2fa718218c21df006b71d9325f1ddb5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f3d209442262c674f0bde0257ef1792"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a8f3d209442262c674f0bde0257ef1792">n</a> ()</td></tr>
<tr class="memdesc:a8f3d209442262c674f0bde0257ef1792"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the batch of the coordinate.  <a href="#a8f3d209442262c674f0bde0257ef1792">More...</a><br /></td></tr>
<tr class="separator:a8f3d209442262c674f0bde0257ef1792"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a71dda571a04037e564f238bb9a76f213"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a> () const </td></tr>
<tr class="memdesc:a71dda571a04037e564f238bb9a76f213"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the row of the coordinate.  <a href="#a71dda571a04037e564f238bb9a76f213">More...</a><br /></td></tr>
<tr class="separator:a71dda571a04037e564f238bb9a76f213"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae399c4159fb4e799c42bd882df2ccce7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#ae399c4159fb4e799c42bd882df2ccce7">h</a> ()</td></tr>
<tr class="memdesc:ae399c4159fb4e799c42bd882df2ccce7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the row of the coordinate.  <a href="#ae399c4159fb4e799c42bd882df2ccce7">More...</a><br /></td></tr>
<tr class="separator:ae399c4159fb4e799c42bd882df2ccce7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3136dc898c4ef079e73b51b1850ba7e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a> () const </td></tr>
<tr class="memdesc:ae3136dc898c4ef079e73b51b1850ba7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the column of the coordinate.  <a href="#ae3136dc898c4ef079e73b51b1850ba7e">More...</a><br /></td></tr>
<tr class="separator:ae3136dc898c4ef079e73b51b1850ba7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b391bf3ec3db6eec31eb23d5ff7fd21"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a3b391bf3ec3db6eec31eb23d5ff7fd21">w</a> ()</td></tr>
<tr class="memdesc:a3b391bf3ec3db6eec31eb23d5ff7fd21"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the column of the coordinate.  <a href="#a3b391bf3ec3db6eec31eb23d5ff7fd21">More...</a><br /></td></tr>
<tr class="separator:a3b391bf3ec3db6eec31eb23d5ff7fd21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0f58e5f54b42534fca77a662c78c7ad"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a> () const </td></tr>
<tr class="memdesc:ab0f58e5f54b42534fca77a662c78c7ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the channel of the coordinate.  <a href="#ab0f58e5f54b42534fca77a662c78c7ad">More...</a><br /></td></tr>
<tr class="separator:ab0f58e5f54b42534fca77a662c78c7ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a494c8f38161b2d767f9497e751467699"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a494c8f38161b2d767f9497e751467699">c</a> ()</td></tr>
<tr class="memdesc:a494c8f38161b2d767f9497e751467699"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the channel of the coordinate.  <a href="#a494c8f38161b2d767f9497e751467699">More...</a><br /></td></tr>
<tr class="separator:a494c8f38161b2d767f9497e751467699"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28448ff7ebd10f76954d012e7ae9bcd8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a28448ff7ebd10f76954d012e7ae9bcd8">operator+</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:a28448ff7ebd10f76954d012e7ae9bcd8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise addition.  <a href="#a28448ff7ebd10f76954d012e7ae9bcd8">More...</a><br /></td></tr>
<tr class="separator:a28448ff7ebd10f76954d012e7ae9bcd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f850c2e2a7b4cda1e58a04884d8be47"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a9f850c2e2a7b4cda1e58a04884d8be47">operator-</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:a9f850c2e2a7b4cda1e58a04884d8be47"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise subtraction.  <a href="#a9f850c2e2a7b4cda1e58a04884d8be47">More...</a><br /></td></tr>
<tr class="separator:a9f850c2e2a7b4cda1e58a04884d8be47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9fd53334c3a6fdcce9b83896355d429"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#aa9fd53334c3a6fdcce9b83896355d429">operator*</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:aa9fd53334c3a6fdcce9b83896355d429"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise multiplication.  <a href="#aa9fd53334c3a6fdcce9b83896355d429">More...</a><br /></td></tr>
<tr class="separator:aa9fd53334c3a6fdcce9b83896355d429"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab2fd81b93d9130ff969b783dbaab54b2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#ab2fd81b93d9130ff969b783dbaab54b2">operator/</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:ab2fd81b93d9130ff969b783dbaab54b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise division.  <a href="#ab2fd81b93d9130ff969b783dbaab54b2">More...</a><br /></td></tr>
<tr class="separator:ab2fd81b93d9130ff969b783dbaab54b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5f312484425767c77f0192cc89eef3d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#af5f312484425767c77f0192cc89eef3d">operator+=</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:af5f312484425767c77f0192cc89eef3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place addition.  <a href="#af5f312484425767c77f0192cc89eef3d">More...</a><br /></td></tr>
<tr class="separator:af5f312484425767c77f0192cc89eef3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a05798b41f0fbaa92f766902bac286609"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a05798b41f0fbaa92f766902bac286609">operator-=</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:a05798b41f0fbaa92f766902bac286609"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place subtraction.  <a href="#a05798b41f0fbaa92f766902bac286609">More...</a><br /></td></tr>
<tr class="separator:a05798b41f0fbaa92f766902bac286609"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab56a0b2352264f7a3753b621d1d850d6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#ab56a0b2352264f7a3753b621d1d850d6">operator*=</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:ab56a0b2352264f7a3753b621d1d850d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place multiplication.  <a href="#ab56a0b2352264f7a3753b621d1d850d6">More...</a><br /></td></tr>
<tr class="separator:ab56a0b2352264f7a3753b621d1d850d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab591c052af780e65a77ea3e0f33d46aa"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#ab591c052af780e65a77ea3e0f33d46aa">operator/=</a> (<a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:ab591c052af780e65a77ea3e0f33d46aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place division.  <a href="#ab591c052af780e65a77ea3e0f33d46aa">More...</a><br /></td></tr>
<tr class="separator:ab591c052af780e65a77ea3e0f33d46aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_structcutlass_1_1Coord"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_structcutlass_1_1Coord')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 4 &gt;</a></td></tr>
<tr class="memitem:a5281db2419b5567db4265dead7ac02cc inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a5281db2419b5567db4265dead7ac02cc">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> value=<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>(0))</td></tr>
<tr class="memdesc:a5281db2419b5567db4265dead7ac02cc inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default ctor initializes uniformly.  <a href="#a5281db2419b5567db4265dead7ac02cc">More...</a><br /></td></tr>
<tr class="separator:a5281db2419b5567db4265dead7ac02cc inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7094975a4b7471315ca083ae575030a inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ab7094975a4b7471315ca083ae575030a">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const (&amp;_idx)[<a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a>])</td></tr>
<tr class="memdesc:ab7094975a4b7471315ca083ae575030a inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs from an array of integers.  <a href="#ab7094975a4b7471315ca083ae575030a">More...</a><br /></td></tr>
<tr class="separator:ab7094975a4b7471315ca083ae575030a inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42aefbb547e39b8cc7267c58a610c147 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a42aefbb547e39b8cc7267c58a610c147">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; <a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a>, <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>, <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> &gt; const &amp;coord)</td></tr>
<tr class="memdesc:a42aefbb547e39b8cc7267c58a610c147 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="#a42aefbb547e39b8cc7267c58a610c147">More...</a><br /></td></tr>
<tr class="separator:a42aefbb547e39b8cc7267c58a610c147 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a329f97d4a09ef34e8470fe55800871f8 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Slice &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a329f97d4a09ef34e8470fe55800871f8">slice</a> (int start=0, <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> identity=0) const</td></tr>
<tr class="separator:a329f97d4a09ef34e8470fe55800871f8 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae89e8a9fa3f07308f8938052ef1aa1fb inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ae89e8a9fa3f07308f8938052ef1aa1fb">min_dim_index</a> () const</td></tr>
<tr class="memdesc:ae89e8a9fa3f07308f8938052ef1aa1fb inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the index of the dimension with least value.  <a href="#ae89e8a9fa3f07308f8938052ef1aa1fb">More...</a><br /></td></tr>
<tr class="separator:ae89e8a9fa3f07308f8938052ef1aa1fb inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe58b7c8f153a6029c2adc173f340fe0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#abe58b7c8f153a6029c2adc173f340fe0">max_dim_index</a> () const</td></tr>
<tr class="memdesc:abe58b7c8f153a6029c2adc173f340fe0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the index of the dimension with greatest value.  <a href="#abe58b7c8f153a6029c2adc173f340fe0">More...</a><br /></td></tr>
<tr class="separator:abe58b7c8f153a6029c2adc173f340fe0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88096d051dd05111cf265a011a89f7f6 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a88096d051dd05111cf265a011a89f7f6">operator bool</a> () const</td></tr>
<tr class="memdesc:a88096d051dd05111cf265a011a89f7f6 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if Coord is non-zero.  <a href="#a88096d051dd05111cf265a011a89f7f6">More...</a><br /></td></tr>
<tr class="separator:a88096d051dd05111cf265a011a89f7f6 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa733c6fae0da553053530cba2dddcaa0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#aa733c6fae0da553053530cba2dddcaa0">operator!</a> () const</td></tr>
<tr class="memdesc:aa733c6fae0da553053530cba2dddcaa0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if Coord is uniformly zero.  <a href="#aa733c6fae0da553053530cba2dddcaa0">More...</a><br /></td></tr>
<tr class="separator:aa733c6fae0da553053530cba2dddcaa0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec4c529a728118c0df6a3f527daba746 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#aec4c529a728118c0df6a3f527daba746">operator+</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:aec4c529a728118c0df6a3f527daba746 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise addition.  <a href="#aec4c529a728118c0df6a3f527daba746">More...</a><br /></td></tr>
<tr class="separator:aec4c529a728118c0df6a3f527daba746 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e1a706629eae28128230a0fa34b84a0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a2e1a706629eae28128230a0fa34b84a0">operator-</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a2e1a706629eae28128230a0fa34b84a0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise subtraction.  <a href="#a2e1a706629eae28128230a0fa34b84a0">More...</a><br /></td></tr>
<tr class="separator:a2e1a706629eae28128230a0fa34b84a0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6b5fd8d0e5cb856d363fbff9a5b89dd inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ac6b5fd8d0e5cb856d363fbff9a5b89dd">operator*</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:ac6b5fd8d0e5cb856d363fbff9a5b89dd inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise multiplication.  <a href="#ac6b5fd8d0e5cb856d363fbff9a5b89dd">More...</a><br /></td></tr>
<tr class="separator:ac6b5fd8d0e5cb856d363fbff9a5b89dd inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ae9f189d1a7a5ce7bb5e4416559c79f inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a6ae9f189d1a7a5ce7bb5e4416559c79f">operator/</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a6ae9f189d1a7a5ce7bb5e4416559c79f inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise division.  <a href="#a6ae9f189d1a7a5ce7bb5e4416559c79f">More...</a><br /></td></tr>
<tr class="separator:a6ae9f189d1a7a5ce7bb5e4416559c79f inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb799faf60a17b708d0802f9e23c812f inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#acb799faf60a17b708d0802f9e23c812f">operator+=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:acb799faf60a17b708d0802f9e23c812f inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place addition.  <a href="#acb799faf60a17b708d0802f9e23c812f">More...</a><br /></td></tr>
<tr class="separator:acb799faf60a17b708d0802f9e23c812f inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15ac170c861b34d418432aeb62ea86e0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a15ac170c861b34d418432aeb62ea86e0">operator-=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:a15ac170c861b34d418432aeb62ea86e0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place subtraction.  <a href="#a15ac170c861b34d418432aeb62ea86e0">More...</a><br /></td></tr>
<tr class="separator:a15ac170c861b34d418432aeb62ea86e0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00e618bc944d355badf67c0edd791412 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a00e618bc944d355badf67c0edd791412">operator*=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:a00e618bc944d355badf67c0edd791412 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place multiplication.  <a href="#a00e618bc944d355badf67c0edd791412">More...</a><br /></td></tr>
<tr class="separator:a00e618bc944d355badf67c0edd791412 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af515e669363986dbbd60951ea6b69e14 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#af515e669363986dbbd60951ea6b69e14">operator/=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:af515e669363986dbbd60951ea6b69e14 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place division.  <a href="#af515e669363986dbbd60951ea6b69e14">More...</a><br /></td></tr>
<tr class="separator:af515e669363986dbbd60951ea6b69e14 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6eee93e5fdbe147f751ec108b28275a1 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a6eee93e5fdbe147f751ec108b28275a1">operator[]</a> (int dim)</td></tr>
<tr class="memdesc:a6eee93e5fdbe147f751ec108b28275a1 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Member access operator.  <a href="#a6eee93e5fdbe147f751ec108b28275a1">More...</a><br /></td></tr>
<tr class="separator:a6eee93e5fdbe147f751ec108b28275a1 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accf5689f0d6a6f91965bff0cfd9ec296 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#accf5689f0d6a6f91965bff0cfd9ec296">operator[]</a> (int dim) const</td></tr>
<tr class="memdesc:accf5689f0d6a6f91965bff0cfd9ec296 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Member access operator.  <a href="#accf5689f0d6a6f91965bff0cfd9ec296">More...</a><br /></td></tr>
<tr class="separator:accf5689f0d6a6f91965bff0cfd9ec296 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a057a417a4d4a6e2f69e0b55a6f7ee902 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a057a417a4d4a6e2f69e0b55a6f7ee902">dot</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b, <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> <a class="el" href="structcutlass_1_1Coord.html#a49bb1a68198bd4c520d15efe3e84f757">sum</a>=<a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>(0)) const</td></tr>
<tr class="memdesc:a057a417a4d4a6e2f69e0b55a6f7ee902 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes the dot product with anotherCoord object.  <a href="#a057a417a4d4a6e2f69e0b55a6f7ee902">More...</a><br /></td></tr>
<tr class="separator:a057a417a4d4a6e2f69e0b55a6f7ee902 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a65128c86b236cd2bea875b85a34bc1 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a> ()</td></tr>
<tr class="memdesc:a8a65128c86b236cd2bea875b85a34bc1 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the index of a given Coord element.  <a href="#a8a65128c86b236cd2bea875b85a34bc1">More...</a><br /></td></tr>
<tr class="separator:a8a65128c86b236cd2bea875b85a34bc1 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a358dde78a1c2105a9aeb4adee8bb3d2d inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a358dde78a1c2105a9aeb4adee8bb3d2d">at</a> (int dim)</td></tr>
<tr class="memdesc:a358dde78a1c2105a9aeb4adee8bb3d2d inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access via index; may limit unrolling potential.  <a href="#a358dde78a1c2105a9aeb4adee8bb3d2d">More...</a><br /></td></tr>
<tr class="separator:a358dde78a1c2105a9aeb4adee8bb3d2d inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7379275d7431ead927af7966b6fa0ec inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ac7379275d7431ead927af7966b6fa0ec">at</a> () const</td></tr>
<tr class="memdesc:ac7379275d7431ead927af7966b6fa0ec inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the index of a given Coord element.  <a href="#ac7379275d7431ead927af7966b6fa0ec">More...</a><br /></td></tr>
<tr class="separator:ac7379275d7431ead927af7966b6fa0ec inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06046c22b877abfb277d3f0fe4f8578a inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a06046c22b877abfb277d3f0fe4f8578a">at</a> (int dim) const</td></tr>
<tr class="memdesc:a06046c22b877abfb277d3f0fe4f8578a inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access via index; may limit unrolling potential.  <a href="#a06046c22b877abfb277d3f0fe4f8578a">More...</a><br /></td></tr>
<tr class="separator:a06046c22b877abfb277d3f0fe4f8578a inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8424ccd74e7e0ff1cf358ef571779cba inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8424ccd74e7e0ff1cf358ef571779cba">operator==</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a8424ccd74e7e0ff1cf358ef571779cba inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determines if two Coord&lt;&gt; objects are equal.  <a href="#a8424ccd74e7e0ff1cf358ef571779cba">More...</a><br /></td></tr>
<tr class="separator:a8424ccd74e7e0ff1cf358ef571779cba inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8183b9203a213d4b6381ad7dc120deea inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8183b9203a213d4b6381ad7dc120deea">operator!=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a8183b9203a213d4b6381ad7dc120deea inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not equal.  <a href="#a8183b9203a213d4b6381ad7dc120deea">More...</a><br /></td></tr>
<tr class="separator:a8183b9203a213d4b6381ad7dc120deea inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40e145063833155c800b38f82cee7461 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a40e145063833155c800b38f82cee7461">clamp</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;max, <a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;min=<a class="el" href="structcutlass_1_1Coord.html">Coord</a>())</td></tr>
<tr class="memdesc:a40e145063833155c800b38f82cee7461 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clamps a coordinate to a range specified by maximum and minimum values.  <a href="#a40e145063833155c800b38f82cee7461">More...</a><br /></td></tr>
<tr class="separator:a40e145063833155c800b38f82cee7461 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49bb1a68198bd4c520d15efe3e84f757 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a49bb1a68198bd4c520d15efe3e84f757">sum</a> () const</td></tr>
<tr class="memdesc:a49bb1a68198bd4c520d15efe3e84f757 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the sum of all elements.  <a href="#a49bb1a68198bd4c520d15efe3e84f757">More...</a><br /></td></tr>
<tr class="separator:a49bb1a68198bd4c520d15efe3e84f757 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5a2fb5b6b57e0726624c2b6e7c6545c inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ad5a2fb5b6b57e0726624c2b6e7c6545c">product</a> () const</td></tr>
<tr class="memdesc:ad5a2fb5b6b57e0726624c2b6e7c6545c inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the product of all elements.  <a href="#ad5a2fb5b6b57e0726624c2b6e7c6545c">More...</a><br /></td></tr>
<tr class="separator:ad5a2fb5b6b57e0726624c2b6e7c6545c inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47ad37153eb8d291266a51b39ead5948 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a47ad37153eb8d291266a51b39ead5948">operator&lt;</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a47ad37153eb8d291266a51b39ead5948 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Less than operator.  <a href="#a47ad37153eb8d291266a51b39ead5948">More...</a><br /></td></tr>
<tr class="separator:a47ad37153eb8d291266a51b39ead5948 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e6405e081936a4fb23f15160e94ad08 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a0e6405e081936a4fb23f15160e94ad08">operator&lt;=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a0e6405e081936a4fb23f15160e94ad08 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Less than or equals operator.  <a href="#a0e6405e081936a4fb23f15160e94ad08">More...</a><br /></td></tr>
<tr class="separator:a0e6405e081936a4fb23f15160e94ad08 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08507ff13f518a93a7d16ea0018f8a53 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a08507ff13f518a93a7d16ea0018f8a53">operator&gt;</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a08507ff13f518a93a7d16ea0018f8a53 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Greater than operator.  <a href="#a08507ff13f518a93a7d16ea0018f8a53">More...</a><br /></td></tr>
<tr class="separator:a08507ff13f518a93a7d16ea0018f8a53 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae37243e9f51b2b92b5fd09de69392657 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ae37243e9f51b2b92b5fd09de69392657">operator&gt;=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:ae37243e9f51b2b92b5fd09de69392657 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Greater than or equals operator.  <a href="#ae37243e9f51b2b92b5fd09de69392657">More...</a><br /></td></tr>
<tr class="separator:ae37243e9f51b2b92b5fd09de69392657 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:acb0b48b015b75e2d7a226a69f5a2f3b8"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#acb0b48b015b75e2d7a226a69f5a2f3b8">kN</a> = 0</td></tr>
<tr class="memdesc:acb0b48b015b75e2d7a226a69f5a2f3b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Batch dimension.  <a href="#acb0b48b015b75e2d7a226a69f5a2f3b8">More...</a><br /></td></tr>
<tr class="separator:acb0b48b015b75e2d7a226a69f5a2f3b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4014fb6f869b2b5c16796f4435eb110"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#aa4014fb6f869b2b5c16796f4435eb110">kH</a> = 1</td></tr>
<tr class="memdesc:aa4014fb6f869b2b5c16796f4435eb110"><td class="mdescLeft">&#160;</td><td class="mdescRight">Height dimension.  <a href="#aa4014fb6f869b2b5c16796f4435eb110">More...</a><br /></td></tr>
<tr class="separator:aa4014fb6f869b2b5c16796f4435eb110"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01e55a99e690d697ca62cfaeb4bcde9f"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a01e55a99e690d697ca62cfaeb4bcde9f">kW</a> = 2</td></tr>
<tr class="memdesc:a01e55a99e690d697ca62cfaeb4bcde9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Width dimension.  <a href="#a01e55a99e690d697ca62cfaeb4bcde9f">More...</a><br /></td></tr>
<tr class="separator:a01e55a99e690d697ca62cfaeb4bcde9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a538809c6f5ee032adf4558cd004d988d"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a538809c6f5ee032adf4558cd004d988d">kC</a> = 3</td></tr>
<tr class="memdesc:a538809c6f5ee032adf4558cd004d988d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Channels dimension.  <a href="#a538809c6f5ee032adf4558cd004d988d">More...</a><br /></td></tr>
<tr class="separator:a538809c6f5ee032adf4558cd004d988d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_static_attribs_structcutlass_1_1Coord"><td colspan="2" onclick="javascript:toggleInherit('pub_static_attribs_structcutlass_1_1Coord')"><img src="closed.png" alt="-"/>&#160;Static Public Attributes inherited from <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 4 &gt;</a></td></tr>
<tr class="memitem:a2b07d7291d175920274c5e3346e5b68b inherit pub_static_attribs_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a></td></tr>
<tr class="memdesc:a2b07d7291d175920274c5e3346e5b68b inherit pub_static_attribs_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of elements in Coord.  <a href="#a2b07d7291d175920274c5e3346e5b68b">More...</a><br /></td></tr>
<tr class="separator:a2b07d7291d175920274c5e3346e5b68b inherit pub_static_attribs_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a367dd7e307c7e8e2aa7396d027e8891e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">cutlass::Tensor4DCoord::Base</a> =  <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;4&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4f63c0cc6b642e80624beafb6c3390a1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">cutlass::Tensor4DCoord::Index</a> =  typename <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Base::Index</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a779bf9ea896ac4ae9d4def10cd23eb45"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a779bf9ea896ac4ae9d4def10cd23eb45">cutlass::Tensor4DCoord::LongIndex</a> =  typename <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">Base::LongIndex</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a9a9c773b2bfec43d1722fd7a490fd436"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::Tensor4DCoord::Tensor4DCoord </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afac3bfcfde4408c922ca25c965a40cd7"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::Tensor4DCoord::Tensor4DCoord </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 4 &gt; const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aac1d0a33901e2bfb88eb277a594bcd0c"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::Tensor4DCoord::Tensor4DCoord </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&#160;</td>
          <td class="paramname"><em>n</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&#160;</td>
          <td class="paramname"><em>h</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&#160;</td>
          <td class="paramname"><em>w</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ab0f58e5f54b42534fca77a662c78c7ad"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const&amp; cutlass::Tensor4DCoord::c </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a494c8f38161b2d767f9497e751467699"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&amp; cutlass::Tensor4DCoord::c </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a71dda571a04037e564f238bb9a76f213"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const&amp; cutlass::Tensor4DCoord::h </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae399c4159fb4e799c42bd882df2ccce7"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&amp; cutlass::Tensor4DCoord::h </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2fa718218c21df006b71d9325f1ddb5a"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const&amp; cutlass::Tensor4DCoord::n </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8f3d209442262c674f0bde0257ef1792"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&amp; cutlass::Tensor4DCoord::n </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa9fd53334c3a6fdcce9b83896355d429"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> cutlass::Tensor4DCoord::operator* </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab56a0b2352264f7a3753b621d1d850d6"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; cutlass::Tensor4DCoord::operator*= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a28448ff7ebd10f76954d012e7ae9bcd8"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> cutlass::Tensor4DCoord::operator+ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af5f312484425767c77f0192cc89eef3d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; cutlass::Tensor4DCoord::operator+= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9f850c2e2a7b4cda1e58a04884d8be47"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> cutlass::Tensor4DCoord::operator- </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a05798b41f0fbaa92f766902bac286609"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; cutlass::Tensor4DCoord::operator-= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab2fd81b93d9130ff969b783dbaab54b2"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> cutlass::Tensor4DCoord::operator/ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab591c052af780e65a77ea3e0f33d46aa"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; cutlass::Tensor4DCoord::operator/= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae3136dc898c4ef079e73b51b1850ba7e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> const&amp; cutlass::Tensor4DCoord::w </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3b391bf3ec3db6eec31eb23d5ff7fd21"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a>&amp; cutlass::Tensor4DCoord::w </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a538809c6f5ee032adf4558cd004d988d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const cutlass::Tensor4DCoord::kC = 3</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa4014fb6f869b2b5c16796f4435eb110"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const cutlass::Tensor4DCoord::kH = 1</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acb0b48b015b75e2d7a226a69f5a2f3b8"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const cutlass::Tensor4DCoord::kN = 0</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a01e55a99e690d697ca62cfaeb4bcde9f"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const cutlass::Tensor4DCoord::kW = 2</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="tensor__coord_8h_source.html">tensor_coord.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
