#!/usr/bin/env python3
"""
Simple HTTP server to test MLC-LLM Web build
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

def start_server():
    """Start HTTP server for testing web build"""
    
    # Change to web directory
    web_dir = Path("web")
    if not web_dir.exists():
        print("❌ Web directory not found!")
        return False
    
    os.chdir(web_dir)
    
    # Check for required files
    required_files = [
        "dist/wasm/mlc_wasm_runtime.wasm",
        "demo.html"
    ]
    
    print("🔍 Checking Web build files...")
    for file_path in required_files:
        if Path(file_path).exists():
            size = Path(file_path).stat().st_size
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} - Missing!")
    
    # Start server
    PORT = 8080
    Handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", PORT), <PERSON><PERSON>) as httpd:
            print(f"\n🚀 Starting web server at http://localhost:{PORT}")
            print(f"📱 Demo page: http://localhost:{PORT}/demo.html")
            print(f"🔧 WASM runtime: http://localhost:{PORT}/dist/wasm/mlc_wasm_runtime.wasm")
            print("\nPress Ctrl+C to stop server...")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        return True
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False

if __name__ == "__main__":
    start_server()
