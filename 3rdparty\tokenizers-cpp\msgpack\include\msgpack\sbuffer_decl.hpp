//
// MessagePack for C++ simple buffer implementation
//
// Copyright (C) 2016 <PERSON>OND<PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_SBUFFER_DECL_HPP
#define MSGPACK_SBUFFER_DECL_HPP

#include "msgpack/v1/sbuffer_decl.hpp"
#include "msgpack/v2/sbuffer_decl.hpp"
#include "msgpack/v3/sbuffer_decl.hpp"


#endif // MSGPACK_SBUFFER_DECL_HPP
