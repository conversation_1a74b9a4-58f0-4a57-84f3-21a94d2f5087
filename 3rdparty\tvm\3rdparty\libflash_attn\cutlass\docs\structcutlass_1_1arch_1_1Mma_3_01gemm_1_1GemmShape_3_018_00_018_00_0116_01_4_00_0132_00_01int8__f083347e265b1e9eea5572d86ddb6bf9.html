<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 16 &gt;, 32, int8_t, layout::RowMajor, uint8_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAddSaturate &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1arch.html">arch</a></li><li class="navelem"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html">Mma&lt; gemm::GemmShape&lt; 8, 8, 16 &gt;, 32, int8_t, layout::RowMajor, uint8_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAddSaturate &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__a2362f92eed5bed99180572b30aba1e8.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 16 &gt;, 32, int8_t, layout::RowMajor, uint8_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAddSaturate &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Matrix multiply-add operation: S32 = S8 * U8 + S32.  
</p>

<p><code>#include &lt;<a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a86ef1dd0e7b8e925121e88afd49ab949"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a86ef1dd0e7b8e925121e88afd49ab949">Shape</a> = <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;</td></tr>
<tr class="separator:a86ef1dd0e7b8e925121e88afd49ab949"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad37643f96357467f284051614db642ee"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#ad37643f96357467f284051614db642ee">ElementA</a> = int8_t</td></tr>
<tr class="separator:ad37643f96357467f284051614db642ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a183902aaf411a80d9716b9499b3bf9e3"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a183902aaf411a80d9716b9499b3bf9e3">LayoutA</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a183902aaf411a80d9716b9499b3bf9e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0da851ba86ef7e405e487d4fe454a9e5"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a0da851ba86ef7e405e487d4fe454a9e5">FragmentA</a> = Array&lt; int8_t, 4 &gt;</td></tr>
<tr class="separator:a0da851ba86ef7e405e487d4fe454a9e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a06a7c64b22128ebed3b4fed11d4613"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a1a06a7c64b22128ebed3b4fed11d4613">ElementB</a> = uint8_t</td></tr>
<tr class="separator:a1a06a7c64b22128ebed3b4fed11d4613"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af24d458187fe031b34f5ad3a02b773bd"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#af24d458187fe031b34f5ad3a02b773bd">LayoutB</a> = <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td></tr>
<tr class="separator:af24d458187fe031b34f5ad3a02b773bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3fe248524914cefe81de5f1643a4c04"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#af3fe248524914cefe81de5f1643a4c04">FragmentB</a> = Array&lt; uint8_t, 4 &gt;</td></tr>
<tr class="separator:af3fe248524914cefe81de5f1643a4c04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18cc33fea35a2bbe7a3b8c3c88fa0b16"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a18cc33fea35a2bbe7a3b8c3c88fa0b16">ElementC</a> = int</td></tr>
<tr class="separator:a18cc33fea35a2bbe7a3b8c3c88fa0b16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34b3a513bb412f9c84d4051d3ff8fbd1"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a34b3a513bb412f9c84d4051d3ff8fbd1">LayoutC</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a34b3a513bb412f9c84d4051d3ff8fbd1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe9e13aa24236342941625a29f0bb6cf"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#afe9e13aa24236342941625a29f0bb6cf">FragmentC</a> = Array&lt; int, 2 &gt;</td></tr>
<tr class="separator:afe9e13aa24236342941625a29f0bb6cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a272bae17e194d541e90febbd924f0be0"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a272bae17e194d541e90febbd924f0be0">Operator</a> = OpMultiplyAddSaturate</td></tr>
<tr class="separator:a272bae17e194d541e90febbd924f0be0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ad4035659907d79a371ab13da41d306de"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#ad4035659907d79a371ab13da41d306de">operator()</a> (<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#afe9e13aa24236342941625a29f0bb6cf">FragmentC</a> &amp;d, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a0da851ba86ef7e405e487d4fe454a9e5">FragmentA</a> const &amp;a, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#af3fe248524914cefe81de5f1643a4c04">FragmentB</a> const &amp;b, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#afe9e13aa24236342941625a29f0bb6cf">FragmentC</a> const &amp;c) const </td></tr>
<tr class="memdesc:ad4035659907d79a371ab13da41d306de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes multiply-add.  <a href="#ad4035659907d79a371ab13da41d306de">More...</a><br /></td></tr>
<tr class="separator:ad4035659907d79a371ab13da41d306de"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ad37643f96357467f284051614db642ee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#ad37643f96357467f284051614db642ee">ElementA</a> =  int8_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1a06a7c64b22128ebed3b4fed11d4613"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a1a06a7c64b22128ebed3b4fed11d4613">ElementB</a> =  uint8_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a18cc33fea35a2bbe7a3b8c3c88fa0b16"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a18cc33fea35a2bbe7a3b8c3c88fa0b16">ElementC</a> =  int</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0da851ba86ef7e405e487d4fe454a9e5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a0da851ba86ef7e405e487d4fe454a9e5">FragmentA</a> =  Array&lt;int8_t, 4&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af3fe248524914cefe81de5f1643a4c04"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#af3fe248524914cefe81de5f1643a4c04">FragmentB</a> =  Array&lt;uint8_t, 4&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afe9e13aa24236342941625a29f0bb6cf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#afe9e13aa24236342941625a29f0bb6cf">FragmentC</a> =  Array&lt;int, 2&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a183902aaf411a80d9716b9499b3bf9e3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a183902aaf411a80d9716b9499b3bf9e3">LayoutA</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af24d458187fe031b34f5ad3a02b773bd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#af24d458187fe031b34f5ad3a02b773bd">LayoutB</a> =  <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a34b3a513bb412f9c84d4051d3ff8fbd1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a34b3a513bb412f9c84d4051d3ff8fbd1">LayoutC</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a272bae17e194d541e90febbd924f0be0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a272bae17e194d541e90febbd924f0be0">Operator</a> =  OpMultiplyAddSaturate</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a86ef1dd0e7b8e925121e88afd49ab949"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a86ef1dd0e7b8e925121e88afd49ab949">Shape</a> =  <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt;8,8,16&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ad4035659907d79a371ab13da41d306de"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, uint8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#afe9e13aa24236342941625a29f0bb6cf">FragmentC</a> &amp;&#160;</td>
          <td class="paramname"><em>d</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#a0da851ba86ef7e405e487d4fe454a9e5">FragmentA</a> const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#af3fe248524914cefe81de5f1643a4c04">FragmentB</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__f083347e265b1e9eea5572d86ddb6bf9.html#afe9e13aa24236342941625a29f0bb6cf">FragmentC</a> const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
