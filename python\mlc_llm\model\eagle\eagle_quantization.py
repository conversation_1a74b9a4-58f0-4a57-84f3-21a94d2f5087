"""This file specifies how MLC's Eagle parameters are quantized using group quantization
or other formats."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import AWQQuantize, FTQuantize, GroupQuantize, NoQuantize

from .eagle_model import <PERSON><PERSON>onfig, EagleForCasualLM


def group_quant(
    model_config: EagleConfig,
    quantization: GroupQuantize,
) -> <PERSON><PERSON>[nn.Module, QuantizeMapping]:
    """Quantize a Eagle-architecture model using group quantization."""
    model: nn.Module = EagleForCasualLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    quantization.tensor_parallel_shards = model_config.tensor_parallel_shards
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def ft_quant(
    model_config: EagleConfig,
    quantization: FTQuantize,
) -> <PERSON><PERSON>[nn.Module, QuantizeMapping]:
    """Quantize a Eagle-architecture model using FasterTransformer quantization."""
    model: nn.Module = EagleForCasualLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def awq_quant(
    model_config: EagleConfig,
    quantization: AWQQuantize,
) -> Tuple[nn.Module, QuantizeMapping]:
    """Quantize a Eagle-architecture model using Activation-aware Weight Quantization(AWQ)."""
    model: nn.Module = EagleForCasualLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def no_quant(
    model_config: EagleConfig,
    quantization: NoQuantize,
) -> Tuple[nn.Module, QuantizeMapping]:
    """Quantize a Eagle model without quantization."""
    model: nn.Module = EagleForCasualLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
