"""This file specifies how MLC's ChatGLM parameters are quantized using group quantization
or other formats."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import FTQuantize, GroupQuantize, NoQuantize

from .chatglm3_model import ChatG<PERSON>ForCausalLM, GLMConfig


def group_quant(
    model_config: GLMConfig,
    quantization: GroupQuantize,
) -> <PERSON><PERSON>[nn.Modu<PERSON>, QuantizeMapping]:
    """Quantize a ChatGLM-architecture model using group quantization."""
    model: nn.Module = ChatGLMForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    quantization.tensor_parallel_shards = model_config.tensor_parallel_shards
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def ft_quant(
    model_config: GLMConfig,
    quantization: FTQuantize,
) -> <PERSON><PERSON>[nn.<PERSON><PERSON><PERSON>, QuantizeMapping]:
    """Quantize a ChatGLM-architecture model using FasterTransformer quantization."""
    model: nn.Module = ChatGLMForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def no_quant(
    model_config: GLMConfig,
    quantization: NoQuantize,
) -> Tuple[nn.Module, QuantizeMapping]:
    """Quantize a ChatGLM model without quantization."""
    model: nn.Module = ChatGLMForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
