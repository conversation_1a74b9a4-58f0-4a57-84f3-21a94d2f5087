package ai.mlc.mlcllm;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Demo Activity for MLC-LLM Android
 * Shows how to use the MLCEngine in an Android application
 */
public class MLCDemoActivity extends Activity {
    private static final String TAG = "MLCDemoActivity";
    
    private MLCEngine mlcEngine;
    private ExecutorService executorService;
    private Handler mainHandler;
    
    private TextView statusText;
    private TextView systemInfoText;
    private EditText promptInput;
    private Button generateButton;
    private Button loadModelButton;
    private TextView outputText;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize components
        executorService = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
        
        // Create simple layout programmatically
        createLayout();
        
        // Initialize MLC Engine
        initializeMLCEngine();
        
        // Display system information
        displaySystemInfo();
    }
    
    private void createLayout() {
        // Create a simple vertical layout
        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(32, 32, 32, 32);
        
        // Title
        TextView title = new TextView(this);
        title.setText("MLC-LLM Android Demo");
        title.setTextSize(24);
        title.setPadding(0, 0, 0, 32);
        layout.addView(title);
        
        // Status text
        statusText = new TextView(this);
        statusText.setText("Initializing...");
        statusText.setTextSize(16);
        statusText.setPadding(0, 0, 0, 16);
        layout.addView(statusText);
        
        // System info text
        systemInfoText = new TextView(this);
        systemInfoText.setTextSize(12);
        systemInfoText.setPadding(0, 0, 0, 16);
        layout.addView(systemInfoText);
        
        // Load model button
        loadModelButton = new Button(this);
        loadModelButton.setText("Load Model");
        loadModelButton.setOnClickListener(this::onLoadModelClick);
        layout.addView(loadModelButton);
        
        // Prompt input
        promptInput = new EditText(this);
        promptInput.setHint("Enter your prompt here...");
        promptInput.setText("Hello, how are you?");
        promptInput.setPadding(16, 16, 16, 16);
        layout.addView(promptInput);
        
        // Generate button
        generateButton = new Button(this);
        generateButton.setText("Generate Text");
        generateButton.setOnClickListener(this::onGenerateClick);
        generateButton.setEnabled(false);
        layout.addView(generateButton);
        
        // Output text
        outputText = new TextView(this);
        outputText.setText("Output will appear here...");
        outputText.setTextSize(14);
        outputText.setPadding(16, 16, 16, 16);
        outputText.setBackgroundColor(0xFFF5F5F5);
        layout.addView(outputText);
        
        setContentView(layout);
    }
    
    private void initializeMLCEngine() {
        executorService.execute(() -> {
            try {
                mlcEngine = new MLCEngine();
                
                mainHandler.post(() -> {
                    if (mlcEngine.isReady()) {
                        statusText.setText("✅ MLC Engine initialized successfully");
                        statusText.setTextColor(0xFF4CAF50);
                    } else {
                        statusText.setText("⚠️ MLC Engine initialized (no model loaded)");
                        statusText.setTextColor(0xFFFF9800);
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize MLC Engine", e);
                mainHandler.post(() -> {
                    statusText.setText("❌ Failed to initialize MLC Engine");
                    statusText.setTextColor(0xFFF44336);
                });
            }
        });
    }
    
    private void displaySystemInfo() {
        String systemInfo = MLCUtils.getSystemInfo();
        systemInfo += "MLC Engine Version: " + (mlcEngine != null ? mlcEngine.getVersion() : "Unknown") + "\n";
        systemInfo += "Available Memory: " + MLCUtils.formatBytes(MLCUtils.getAvailableMemory(this));
        
        systemInfoText.setText(systemInfo);
        systemInfoText.setTextColor(0xFF666666);
    }
    
    private void onLoadModelClick(View view) {
        loadModelButton.setEnabled(false);
        
        executorService.execute(() -> {
            try {
                // Simulate model loading (in real app, you'd load an actual model)
                Thread.sleep(2000);
                
                // For demo purposes, we'll simulate a successful load
                boolean success = true; // mlcEngine.loadModel("/path/to/model");
                
                mainHandler.post(() -> {
                    if (success) {
                        statusText.setText("✅ Model loaded successfully");
                        statusText.setTextColor(0xFF4CAF50);
                        generateButton.setEnabled(true);
                        Toast.makeText(this, "Model loaded!", Toast.LENGTH_SHORT).show();
                    } else {
                        statusText.setText("❌ Failed to load model");
                        statusText.setTextColor(0xFFF44336);
                        Toast.makeText(this, "Model loading failed", Toast.LENGTH_SHORT).show();
                    }
                    loadModelButton.setEnabled(true);
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Model loading error", e);
                mainHandler.post(() -> {
                    statusText.setText("❌ Model loading error");
                    statusText.setTextColor(0xFFF44336);
                    loadModelButton.setEnabled(true);
                });
            }
        });
    }
    
    private void onGenerateClick(View view) {
        String prompt = promptInput.getText().toString().trim();
        if (prompt.isEmpty()) {
            Toast.makeText(this, "Please enter a prompt", Toast.LENGTH_SHORT).show();
            return;
        }
        
        generateButton.setEnabled(false);
        outputText.setText("Generating...");
        
        executorService.execute(() -> {
            try {
                // Simulate text generation (in real app, you'd use mlcEngine.generate())
                Thread.sleep(3000);
                
                // For demo purposes, we'll return a simulated response
                String response = "This is a simulated response to: \"" + prompt + "\"\n\n" +
                                "In a real implementation, this would be generated by the MLC-LLM model " +
                                "running on your Android device. The model would process your prompt and " +
                                "generate contextually relevant text based on its training.";
                
                mainHandler.post(() -> {
                    outputText.setText(response);
                    generateButton.setEnabled(true);
                    Toast.makeText(this, "Text generated!", Toast.LENGTH_SHORT).show();
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Text generation error", e);
                mainHandler.post(() -> {
                    outputText.setText("Error generating text: " + e.getMessage());
                    generateButton.setEnabled(true);
                });
            }
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        if (mlcEngine != null) {
            mlcEngine.release();
        }
        
        if (executorService != null) {
            executorService.shutdown();
        }
    }
}
