//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2016 <PERSON>ON<PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef MSGPACK_OBJECT_FWD_DECL_HPP
#define MSGPACK_OBJECT_FWD_DECL_HPP

#include "msgpack/v1/object_fwd_decl.hpp"
#include "msgpack/v2/object_fwd_decl.hpp"
#include "msgpack/v3/object_fwd_decl.hpp"

#endif // MSGPACK_OBJECT_FWD_DECL_HPP
