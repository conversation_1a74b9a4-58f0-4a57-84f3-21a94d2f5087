<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: array.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">array.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="array_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;  <span class="keyword">typename</span> T,</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;  <span class="keywordtype">int</span> N,</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;  <span class="keywordtype">bool</span> RegisterSized = <a class="code" href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">sizeof_bits&lt;T&gt;::value</a> &gt;= 32</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;&gt;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">class </span>Array;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N, <span class="keywordtype">bool</span> RegisterSized&gt;</div><div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="structcutlass_1_1sizeof__bits_3_01Array_3_01T_00_01N_00_01RegisterSized_01_4_01_4.html">   50</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;Array&lt;T, N, RegisterSized&gt; &gt; {</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="structcutlass_1_1sizeof__bits_3_01Array_3_01T_00_01N_00_01RegisterSized_01_4_01_4.html#a1b1eb49bd1b04198371b56e3850ea1fa">   51</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">value</a> =</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    <span class="keyword">sizeof</span>(<span class="keyword">typename</span> Array&lt;T, N, RegisterSized&gt;::Storage) * 8 * Array&lt;T, N, RegisterSized&gt;::kStorageElements;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;};</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a935aabfdc47cf03f87c67bb22533f97f">   59</a></span>&#160;<a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <span class="keywordtype">bool</span> <a class="code" href="namespacecutlass.html#a935aabfdc47cf03f87c67bb22533f97f">ispow2</a>(<span class="keywordtype">unsigned</span> x) {</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  <span class="keywordflow">return</span> !(x &amp; (x - 1));</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;}</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="namespacecutlass.html#ac16d8caf23537912eb02123c4bdacd14">   67</a></span>&#160;<a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <span class="keywordtype">unsigned</span> <a class="code" href="namespacecutlass.html#ac16d8caf23537912eb02123c4bdacd14">floor_pow_2</a>(<span class="keywordtype">unsigned</span> x) {</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="namespacecutlass.html#a935aabfdc47cf03f87c67bb22533f97f">ispow2</a>(x) ? x : <a class="code" href="namespacecutlass.html#ac16d8caf23537912eb02123c4bdacd14">floor_pow_2</a>(x &gt;&gt; 1);</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;}</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keyword">typename</span> T,</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="keywordtype">int</span> N</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;&gt;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html">   78</a></span>&#160;<span class="keyword">class </span>Array&lt;T, N, true&gt; {</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a70e53f314dc7b7bb6050486d18c14b31">   82</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a70e53f314dc7b7bb6050486d18c14b31">Storage</a> = T;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7bf5b693d01e004852c642400d0e9b89">   85</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7bf5b693d01e004852c642400d0e9b89">Element</a> = T;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="comment">//static std::size_t const kStorageElements = N;</span></div><div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aff4b09f36ec3f8861ebd2db338a298b2">   89</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">size_t</span> <span class="keyword">const</span> kStorageElements = N;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a59927c40660b5f39218f5867d4158e5e">   92</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">size_t</span> <span class="keyword">const</span> kElements = N;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;  <span class="comment">// C++ standard members</span></div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">   98</a></span>&#160;  <span class="keyword">typedef</span> T <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a>;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">   99</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">size_t</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a>;</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7ffe7541c2cadd34bc6e65ad351772ce">  100</a></span>&#160;  <span class="keyword">typedef</span> ptrdiff_t <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7ffe7541c2cadd34bc6e65ad351772ce">difference_type</a>;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">  101</a></span>&#160;  <span class="keyword">typedef</span> value_type &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a>;</div><div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">  102</a></span>&#160;  <span class="keyword">typedef</span> value_type <span class="keyword">const</span> &amp; <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a>;</div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">  103</a></span>&#160;  <span class="keyword">typedef</span> value_type *<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">pointer</a>;</div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">  104</a></span>&#160;  <span class="keyword">typedef</span> value_type <span class="keyword">const</span> * <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">const_pointer</a>;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <span class="comment">// Iterators</span></div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html">  111</a></span>&#160;  <span class="keyword">class </span>iterator {</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    T *ptr_;</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a0995262fdc623efc981e78c902487474">  119</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a0995262fdc623efc981e78c902487474">iterator</a>(): ptr_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { }</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#ac50cf5822b023f4752d80f71963990cb">  122</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#ac50cf5822b023f4752d80f71963990cb">iterator</a>(T *_ptr): ptr_(_ptr) { }</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00125"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a719a46d965659e78b9b4b72ea22aa705">  125</a></span>&#160;    iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a719a46d965659e78b9b4b72ea22aa705">operator++</a>() {</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;      ++ptr_;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    }</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00131"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a1bd27b5796ef75d377514d91ac8ab03c">  131</a></span>&#160;    iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a1bd27b5796ef75d377514d91ac8ab03c">operator--</a>() {</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;      --ptr_;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    }</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#aa5a61ef108e3a2e2d56c4f7d54a38a1d">  137</a></span>&#160;    iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#aa5a61ef108e3a2e2d56c4f7d54a38a1d">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;      iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;      ++ptr_;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    }</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00144"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a42030a25dd5723f3c1b3944c168b6e22">  144</a></span>&#160;    iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a42030a25dd5723f3c1b3944c168b6e22">operator--</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;      iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;      --ptr_;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    }</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00151"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a2e7e50d7d99fda71be7ba03e0ce80417">  151</a></span>&#160;    T &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a2e7e50d7d99fda71be7ba03e0ce80417">operator*</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;      <span class="keywordflow">return</span> *ptr_;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    }</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00156"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#aeab63d32f97e5046f55473d652a69bd7">  156</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#aeab63d32f97e5046f55473d652a69bd7">operator==</a>(iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;      <span class="keywordflow">return</span> ptr_ == other.ptr_;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    }</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a498750b21a735e4ed7cd3c5ff4512497">  161</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a498750b21a735e4ed7cd3c5ff4512497">operator!=</a>(iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;      <span class="keywordflow">return</span> ptr_ != other.ptr_;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    }</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  };</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html">  167</a></span>&#160;  <span class="keyword">class </span>const_iterator {</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    T *ptr_;</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a40f18ab5962efa95ac4ae4f5140c5d7b">  175</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a40f18ab5962efa95ac4ae4f5140c5d7b">const_iterator</a>(): ptr_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { }</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a56cb84bfcb97eeeae472f03fc203d759">  178</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a56cb84bfcb97eeeae472f03fc203d759">const_iterator</a>(T <span class="keyword">const</span> *_ptr): ptr_(_ptr) { }</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00181"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#ae148a1e543b22c1c4ec20374bc8929b3">  181</a></span>&#160;    const_iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#ae148a1e543b22c1c4ec20374bc8929b3">operator++</a>() {</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;      ++ptr_;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    }</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00187"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a39085604c1b7a0dee3f5a0b96776d297">  187</a></span>&#160;    const_iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a39085604c1b7a0dee3f5a0b96776d297">operator--</a>() {</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;      --ptr_;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;    }</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00193"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#aa9e22b7054da29fc4863051f2bb05ff7">  193</a></span>&#160;    const_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#aa9e22b7054da29fc4863051f2bb05ff7">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;      const_iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      ++ptr_;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;    }</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00200"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#afc73e87dfebf9990e76aa47de2d30311">  200</a></span>&#160;    const_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#afc73e87dfebf9990e76aa47de2d30311">operator--</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;      const_iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;      --ptr_;</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    }</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00207"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#af34c15c6d1d13db36ffe4b112bc75d47">  207</a></span>&#160;    T <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#af34c15c6d1d13db36ffe4b112bc75d47">operator*</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;      <span class="keywordflow">return</span> *ptr_;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;    }</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00212"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a25c770f60b9e8f8d7eb2e58efcb7c3e1">  212</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a25c770f60b9e8f8d7eb2e58efcb7c3e1">operator==</a>(const_iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;      <span class="keywordflow">return</span> ptr_ == other.ptr_;</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    }</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00217"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a6a6e0f4caab421bbe90a94d199df0281">  217</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a6a6e0f4caab421bbe90a94d199df0281">operator!=</a>(const_iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;      <span class="keywordflow">return</span> ptr_ != other.ptr_;</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;    }</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;  };</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00223"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html">  223</a></span>&#160;  <span class="keyword">class </span>reverse_iterator {</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    T *ptr_;</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00231"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af95b8971433b4acec7e25c00167adb6a">  231</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af95b8971433b4acec7e25c00167adb6a">reverse_iterator</a>(): ptr_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { }</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00234"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af5056ae2c80aadeb027fffb8ed35f719">  234</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af5056ae2c80aadeb027fffb8ed35f719">reverse_iterator</a>(T *_ptr): ptr_(_ptr) { }</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00237"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#ace56f663cafa0dbf3c8e123825bd0a2e">  237</a></span>&#160;    reverse_iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#ace56f663cafa0dbf3c8e123825bd0a2e">operator++</a>() {</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;      --ptr_;</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;    }</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00243"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#aa312eb12052d358ce7ce71d3eb1405cb">  243</a></span>&#160;    reverse_iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#aa312eb12052d358ce7ce71d3eb1405cb">operator--</a>() {</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;      ++ptr_;</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;    }</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00249"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a54132c8586c34347e9c607dbf8bf3c39">  249</a></span>&#160;    reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a54132c8586c34347e9c607dbf8bf3c39">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;      iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;      --ptr_;</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;    }</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00256"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a45521aa859c43140ac0dd95f5b161036">  256</a></span>&#160;    reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a45521aa859c43140ac0dd95f5b161036">operator--</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;      iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;      ++ptr_;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    }</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00263"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a1c8dca20abc1818ddab6973280394888">  263</a></span>&#160;    T &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a1c8dca20abc1818ddab6973280394888">operator*</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;      <span class="keywordflow">return</span> *(ptr_ - 1);</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    }</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00268"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a6837b73dd030c8d327041639fa8e8a27">  268</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a6837b73dd030c8d327041639fa8e8a27">operator==</a>(reverse_iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;      <span class="keywordflow">return</span> ptr_ == other.ptr_;</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    }</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00273"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#aa3d086a10cf92a20c213c4bedbaa60ff">  273</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#aa3d086a10cf92a20c213c4bedbaa60ff">operator!=</a>(reverse_iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;      <span class="keywordflow">return</span> ptr_ != other.ptr_;</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;    }</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  };</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;</div><div class="line"><a name="l00279"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html">  279</a></span>&#160;  <span class="keyword">class </span>const_reverse_iterator {</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;    T <span class="keyword">const</span> *ptr_;</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00287"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#aa839335a821fad9841eb31560d7520a2">  287</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#aa839335a821fad9841eb31560d7520a2">const_reverse_iterator</a>(): ptr_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { }</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00290"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a6e2a9b7f836704d9d39ad42c74502a07">  290</a></span>&#160;    <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a6e2a9b7f836704d9d39ad42c74502a07">const_reverse_iterator</a>(T <span class="keyword">const</span> *_ptr): ptr_(_ptr) { }</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00293"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a76905b3d4bcb451ce2f6bdc73fa958ca">  293</a></span>&#160;    const_reverse_iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a76905b3d4bcb451ce2f6bdc73fa958ca">operator++</a>() {</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;      --ptr_;</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;    }</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00299"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a16df2c0ef6340950eed8c080e18e062e">  299</a></span>&#160;    const_reverse_iterator &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a16df2c0ef6340950eed8c080e18e062e">operator--</a>() {</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;      ++ptr_;</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;      <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    }</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00305"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a08821e8f1c7e5c614e38ac0b14787806">  305</a></span>&#160;    const_reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a08821e8f1c7e5c614e38ac0b14787806">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;      const_reverse_iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;      --ptr_;</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;    }</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;</div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00312"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a9bbf80876fe3e72dbfe39ee880ec2f59">  312</a></span>&#160;    const_reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a9bbf80876fe3e72dbfe39ee880ec2f59">operator--</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;      const_reverse_iterator ret(*<span class="keyword">this</span>);</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;      ++ptr_;</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;      <span class="keywordflow">return</span> ret;</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;    }</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00319"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a49ed1c872c1640dca056fd4f8ced2261">  319</a></span>&#160;    T <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a49ed1c872c1640dca056fd4f8ced2261">operator*</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;      <span class="keywordflow">return</span> *(ptr_ - 1);</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;    }</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00324"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#af5f68e8bd235243c74e7dc256fa37408">  324</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#af5f68e8bd235243c74e7dc256fa37408">operator==</a>(const_iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;      <span class="keywordflow">return</span> ptr_ == other.ptr_;</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;    }</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00329"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#aec971b0beb331c02f2b6739bd78037dd">  329</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#aec971b0beb331c02f2b6739bd78037dd">operator!=</a>(const_iterator <span class="keyword">const</span> &amp;other)<span class="keyword"> const </span>{</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;      <span class="keywordflow">return</span> ptr_ != other.ptr_;</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;    }</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  };</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;  <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a70e53f314dc7b7bb6050486d18c14b31">Storage</a> storage[kElements];</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00342"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a168315948a76d6ae9d7491ad0e1ca302">  342</a></span>&#160;  <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a168315948a76d6ae9d7491ad0e1ca302">Array</a>() { }</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00345"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a22af701f6f542b29198c759b653d3fb0">  345</a></span>&#160;  <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a22af701f6f542b29198c759b653d3fb0">Array</a>(Array <span class="keyword">const</span> &amp;x) {</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kElements; ++i) {</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;      storage[i] = x.storage[i];</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;    }</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;  }</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00354"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae67b1d98a446384fc75a1c92474e719d">  354</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae67b1d98a446384fc75a1c92474e719d">clear</a>() {</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;    fill(T(0));</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;  }</div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00359"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aedd3e189bcbbb69ecd98978bcbbc3f1f">  359</a></span>&#160;  reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aedd3e189bcbbb69ecd98978bcbbc3f1f">at</a>(size_type pos) {</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>reference<span class="keyword">&gt;</span>(storage[pos]);</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;  }</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;</div><div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00364"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab504833fe30934eeb6e71e235e7942f1">  364</a></span>&#160;  const_reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab504833fe30934eeb6e71e235e7942f1">at</a>(size_type pos)<span class="keyword"> const </span>{</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>const_reference<span class="keyword">&gt;</span>(storage[pos]);</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;  }</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00369"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0fea9a8e9f9def4c0059bba750a95167">  369</a></span>&#160;  reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0fea9a8e9f9def4c0059bba750a95167">operator[]</a>(size_type pos) {</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>reference<span class="keyword">&gt;</span>(storage[pos]);</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;  }</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00374"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9812d796007116dbd8b20117976deb48">  374</a></span>&#160;  const_reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9812d796007116dbd8b20117976deb48">operator[]</a>(size_type pos)<span class="keyword"> const </span>{</div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>const_reference<span class="keyword">&gt;</span>(storage[pos]);</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;  }</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00379"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5adbb5bb00cca5e538cd1215d1de08a4">  379</a></span>&#160;  reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5adbb5bb00cca5e538cd1215d1de08a4">front</a>() {</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>reference<span class="keyword">&gt;</span>(storage[0]);</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;  }</div><div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00384"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0a692495c5f7a7d098e60b9292a07e4f">  384</a></span>&#160;  const_reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0a692495c5f7a7d098e60b9292a07e4f">front</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>const_reference<span class="keyword">&gt;</span>(storage[0]);</div><div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;  }</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00389"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aa193b8e73b93639f84224d1fea46330d">  389</a></span>&#160;  reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aa193b8e73b93639f84224d1fea46330d">back</a>() {</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>reference<span class="keyword">&gt;</span>(storage[kStorageElements - 1]);</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;  }</div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;</div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00394"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6c81a715431cf5a772c2273362df97fd">  394</a></span>&#160;  const_reference <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6c81a715431cf5a772c2273362df97fd">back</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>const_reference<span class="keyword">&gt;</span>(storage[kStorageElements - 1]);</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  }</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;</div><div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00399"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#af47ab51582aa1e4c811a9e111b594556">  399</a></span>&#160;  pointer <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#af47ab51582aa1e4c811a9e111b594556">data</a>() {</div><div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>pointer<span class="keyword">&gt;</span>(storage);</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;  }</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;</div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00404"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3d3d2637b7051145a2048cff1b55c0bf">  404</a></span>&#160;  const_pointer <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3d3d2637b7051145a2048cff1b55c0bf">data</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>const_pointer<span class="keyword">&gt;</span>(storage);</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;  }</div><div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;  </div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00409"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae4e76ed2b36a4deda6ef36b00fdda363">  409</a></span>&#160;  pointer <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae4e76ed2b36a4deda6ef36b00fdda363">raw_data</a>() {</div><div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>pointer<span class="keyword">&gt;</span>(storage);</div><div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;  }</div><div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;</div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00414"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a90aaac40587e3ae5622030e999995f40">  414</a></span>&#160;  const_pointer <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a90aaac40587e3ae5622030e999995f40">raw_data</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>const_pointer<span class="keyword">&gt;</span>(storage);</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;  }</div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;</div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;</div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00420"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5d1028cb678773f861add6b47f13de78">  420</a></span>&#160;  <a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5d1028cb678773f861add6b47f13de78">empty</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;    <span class="keywordflow">return</span> !kElements;</div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;  }</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;</div><div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00425"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ac01c21b1956b645165150cfd0d0b0277">  425</a></span>&#160;  <a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> size_type <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ac01c21b1956b645165150cfd0d0b0277">size</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;    <span class="keywordflow">return</span> kElements;</div><div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;  }</div><div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;</div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00430"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3391b79db2b9f3bac9576c9bc7af0402">  430</a></span>&#160;  <a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> size_type <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3391b79db2b9f3bac9576c9bc7af0402">max_size</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;    <span class="keywordflow">return</span> kElements;</div><div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;  }</div><div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;</div><div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00435"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0b3f29a6d79dd9cd55de367c96ecfc5c">  435</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0b3f29a6d79dd9cd55de367c96ecfc5c">fill</a>(T <span class="keyword">const</span> &amp;<a class="code" href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">value</a>) {</div><div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kElements; ++i) {</div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;      storage[i] = <span class="keyword">static_cast&lt;</span><a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a70e53f314dc7b7bb6050486d18c14b31">Storage</a><span class="keyword">&gt;</span>(<a class="code" href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">value</a>);</div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;    }</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;  }</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00443"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#acf5a84cce457d31be7d30c57ab52f64c">  443</a></span>&#160;  iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#acf5a84cce457d31be7d30c57ab52f64c">begin</a>() {</div><div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;    <span class="keywordflow">return</span> iterator(storage);</div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;  }</div><div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00448"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a815d434e9da9715a115896b3f6e64608">  448</a></span>&#160;  const_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a815d434e9da9715a115896b3f6e64608">cbegin</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;    <span class="keywordflow">return</span> const_iterator(storage);</div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;  }</div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;</div><div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00453"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a8e5c83ff2ad6bbfeb5ba0e3c04e3843a">  453</a></span>&#160;  iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a8e5c83ff2ad6bbfeb5ba0e3c04e3843a">end</a>() {</div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;    <span class="keywordflow">return</span> iterator(reinterpret_cast&lt;pointer&gt;(storage + kStorageElements));</div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;  }</div><div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;</div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00458"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a27e663ee5e22d4af436588a500a6cc0c">  458</a></span>&#160;  const_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a27e663ee5e22d4af436588a500a6cc0c">cend</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;    <span class="keywordflow">return</span> const_iterator(reinterpret_cast&lt;const_pointer&gt;(storage + kStorageElements));</div><div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;  }</div><div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;</div><div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00463"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad8ec17a6d004cb6ffd4450c0686cd924">  463</a></span>&#160;  reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad8ec17a6d004cb6ffd4450c0686cd924">rbegin</a>() {</div><div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;    <span class="keywordflow">return</span> reverse_iterator(reinterpret_cast&lt;pointer&gt;(storage + kStorageElements));</div><div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;  }</div><div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;</div><div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00468"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab1813941489bef9563cc0bc3f647b2ca">  468</a></span>&#160;  const_reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab1813941489bef9563cc0bc3f647b2ca">crbegin</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;    <span class="keywordflow">return</span> const_reverse_iterator(reinterpret_cast&lt;const_pointer&gt;(storage + kStorageElements));</div><div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;  }</div><div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;</div><div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00473"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6081f288dfc7b60da8d00913be8e83db">  473</a></span>&#160;  reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6081f288dfc7b60da8d00913be8e83db">rend</a>() {</div><div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;    <span class="keywordflow">return</span> reverse_iterator(reinterpret_cast&lt;pointer&gt;(storage));</div><div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;  }</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;</div><div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00478"></a><span class="lineno"><a class="line" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a76e1b5d728b155f9d967a43c0cc3b0dd">  478</a></span>&#160;  const_reverse_iterator <a class="code" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a76e1b5d728b155f9d967a43c0cc3b0dd">crend</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;    <span class="keywordflow">return</span> const_reverse_iterator(reinterpret_cast&lt;const_pointer&gt;(storage));</div><div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;  }</div><div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;</div><div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;  <span class="comment">// Comparison operators</span></div><div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;</div><div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;};</div><div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;</div><div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;</div><div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;</div><div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;</div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array__subbyte_8h.html">cutlass/array_subbyte.h</a>&quot;</span></div><div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;</div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;</div><div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;</div><div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;  <span class="keyword">typename</span> T,</div><div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;  <span class="keywordtype">int</span> N,</div><div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;  <span class="keywordtype">int</span> Alignment = <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;T&gt;::value</a> * N / 8</div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;&gt;</div><div class="line"><a name="l00511"></a><span class="lineno"><a class="line" href="classcutlass_1_1AlignedArray.html">  511</a></span>&#160;<span class="keyword">class </span>alignas(Alignment) <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray</a>: <span class="keyword">public</span> Array&lt;T, N&gt; {</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;</div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;};</div><div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;</div><div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;</div><div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;</div><div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a949beb7b21ad69d3a3bc394235dd8ec0"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">cutlass::Array&lt; T, N, true &gt;::pointer</a></div><div class="ttdeci">value_type * pointer</div><div class="ttdef"><b>Definition:</b> array.h:103</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_a498750b21a735e4ed7cd3c5ff4512497"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a498750b21a735e4ed7cd3c5ff4512497">cutlass::Array&lt; T, N, true &gt;::iterator::operator!=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator!=(iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:161</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_aa839335a821fad9841eb31560d7520a2"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#aa839335a821fad9841eb31560d7520a2">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::const_reverse_iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator()</div><div class="ttdef"><b>Definition:</b> array.h:287</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_a56cb84bfcb97eeeae472f03fc203d759"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a56cb84bfcb97eeeae472f03fc203d759">cutlass::Array&lt; T, N, true &gt;::const_iterator::const_iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator(T const *_ptr)</div><div class="ttdef"><b>Definition:</b> array.h:178</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_a40f18ab5962efa95ac4ae4f5140c5d7b"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a40f18ab5962efa95ac4ae4f5140c5d7b">cutlass::Array&lt; T, N, true &gt;::const_iterator::const_iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator()</div><div class="ttdef"><b>Definition:</b> array.h:175</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a5d1028cb678773f861add6b47f13de78"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5d1028cb678773f861add6b47f13de78">cutlass::Array&lt; T, N, true &gt;::empty</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr bool empty() const </div><div class="ttdef"><b>Definition:</b> array.h:420</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ab1813941489bef9563cc0bc3f647b2ca"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab1813941489bef9563cc0bc3f647b2ca">cutlass::Array&lt; T, N, true &gt;::crbegin</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator crbegin() const </div><div class="ttdef"><b>Definition:</b> array.h:468</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a7ffe7541c2cadd34bc6e65ad351772ce"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7ffe7541c2cadd34bc6e65ad351772ce">cutlass::Array&lt; T, N, true &gt;::difference_type</a></div><div class="ttdeci">ptrdiff_t difference_type</div><div class="ttdef"><b>Definition:</b> array.h:100</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_aa312eb12052d358ce7ce71d3eb1405cb"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#aa312eb12052d358ce7ce71d3eb1405cb">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator &amp; operator--()</div><div class="ttdef"><b>Definition:</b> array.h:243</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="platform_8h_html_a72f0657181cca64b44eb186b707eb380"><div class="ttname"><a href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a></div><div class="ttdeci">#define constexpr</div><div class="ttdef"><b>Definition:</b> platform.h:137</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html_aff47de86de21dae23ad36184c3d2bb12"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">cutlass::sizeof_bits::value</a></div><div class="ttdeci">static int const value</div><div class="ttdef"><b>Definition:</b> numeric_types.h:43</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_aa9e22b7054da29fc4863051f2bb05ff7"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#aa9e22b7054da29fc4863051f2bb05ff7">cutlass::Array&lt; T, N, true &gt;::const_iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator operator++(int)</div><div class="ttdef"><b>Definition:</b> array.h:193</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a0b3f29a6d79dd9cd55de367c96ecfc5c"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0b3f29a6d79dd9cd55de367c96ecfc5c">cutlass::Array&lt; T, N, true &gt;::fill</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void fill(T const &amp;value)</div><div class="ttdef"><b>Definition:</b> array.h:435</div></div>
<div class="ttc" id="array__subbyte_8h_html"><div class="ttname"><a href="array__subbyte_8h.html">array_subbyte.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a8e5c83ff2ad6bbfeb5ba0e3c04e3843a"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a8e5c83ff2ad6bbfeb5ba0e3c04e3843a">cutlass::Array&lt; T, N, true &gt;::end</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator end()</div><div class="ttdef"><b>Definition:</b> array.h:453</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_af47ab51582aa1e4c811a9e111b594556"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#af47ab51582aa1e4c811a9e111b594556">cutlass::Array&lt; T, N, true &gt;::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE pointer data()</div><div class="ttdef"><b>Definition:</b> array.h:399</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_acf5a84cce457d31be7d30c57ab52f64c"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#acf5a84cce457d31be7d30c57ab52f64c">cutlass::Array&lt; T, N, true &gt;::begin</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator begin()</div><div class="ttdef"><b>Definition:</b> array.h:443</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ad8ec17a6d004cb6ffd4450c0686cd924"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad8ec17a6d004cb6ffd4450c0686cd924">cutlass::Array&lt; T, N, true &gt;::rbegin</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator rbegin()</div><div class="ttdef"><b>Definition:</b> array.h:463</div></div>
<div class="ttc" id="classcutlass_1_1AlignedArray_html"><div class="ttname"><a href="classcutlass_1_1AlignedArray.html">cutlass::AlignedArray</a></div><div class="ttdoc">Aligned array type. </div><div class="ttdef"><b>Definition:</b> array.h:511</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a9812d796007116dbd8b20117976deb48"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9812d796007116dbd8b20117976deb48">cutlass::Array&lt; T, N, true &gt;::operator[]</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reference operator[](size_type pos) const </div><div class="ttdef"><b>Definition:</b> array.h:374</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ae4e76ed2b36a4deda6ef36b00fdda363"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae4e76ed2b36a4deda6ef36b00fdda363">cutlass::Array&lt; T, N, true &gt;::raw_data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE pointer raw_data()</div><div class="ttdef"><b>Definition:</b> array.h:409</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a6081f288dfc7b60da8d00913be8e83db"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6081f288dfc7b60da8d00913be8e83db">cutlass::Array&lt; T, N, true &gt;::rend</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator rend()</div><div class="ttdef"><b>Definition:</b> array.h:473</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a27e663ee5e22d4af436588a500a6cc0c"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a27e663ee5e22d4af436588a500a6cc0c">cutlass::Array&lt; T, N, true &gt;::cend</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator cend() const </div><div class="ttdef"><b>Definition:</b> array.h:458</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_a6e2a9b7f836704d9d39ad42c74502a07"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a6e2a9b7f836704d9d39ad42c74502a07">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::const_reverse_iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator(T const *_ptr)</div><div class="ttdef"><b>Definition:</b> array.h:290</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_a45521aa859c43140ac0dd95f5b161036"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a45521aa859c43140ac0dd95f5b161036">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator operator--(int)</div><div class="ttdef"><b>Definition:</b> array.h:256</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ad0117378d6f0eda984b974ca760ae984"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">cutlass::Array&lt; T, N, true &gt;::size_type</a></div><div class="ttdeci">size_t size_type</div><div class="ttdef"><b>Definition:</b> array.h:99</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_a76905b3d4bcb451ce2f6bdc73fa958ca"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a76905b3d4bcb451ce2f6bdc73fa958ca">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> array.h:293</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ac01c21b1956b645165150cfd0d0b0277"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ac01c21b1956b645165150cfd0d0b0277">cutlass::Array&lt; T, N, true &gt;::size</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr size_type size() const </div><div class="ttdef"><b>Definition:</b> array.h:425</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_aa193b8e73b93639f84224d1fea46330d"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aa193b8e73b93639f84224d1fea46330d">cutlass::Array&lt; T, N, true &gt;::back</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reference back()</div><div class="ttdef"><b>Definition:</b> array.h:389</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_aa3d086a10cf92a20c213c4bedbaa60ff"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#aa3d086a10cf92a20c213c4bedbaa60ff">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::operator!=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator!=(reverse_iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:273</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_a54132c8586c34347e9c607dbf8bf3c39"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a54132c8586c34347e9c607dbf8bf3c39">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator operator++(int)</div><div class="ttdef"><b>Definition:</b> array.h:249</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_a6a6e0f4caab421bbe90a94d199df0281"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a6a6e0f4caab421bbe90a94d199df0281">cutlass::Array&lt; T, N, true &gt;::const_iterator::operator!=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator!=(const_iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:217</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_aec971b0beb331c02f2b6739bd78037dd"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#aec971b0beb331c02f2b6739bd78037dd">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::operator!=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator!=(const_iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:329</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ad64094119b89bb538cd1c1ea979c7954"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">cutlass::Array&lt; T, N, true &gt;::const_reference</a></div><div class="ttdeci">value_type const &amp; const_reference</div><div class="ttdef"><b>Definition:</b> array.h:102</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_ace56f663cafa0dbf3c8e123825bd0a2e"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#ace56f663cafa0dbf3c8e123825bd0a2e">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> array.h:237</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_af34c15c6d1d13db36ffe4b112bc75d47"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#af34c15c6d1d13db36ffe4b112bc75d47">cutlass::Array&lt; T, N, true &gt;::const_iterator::operator*</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T const &amp; operator*() const </div><div class="ttdef"><b>Definition:</b> array.h:207</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a5827968c9c3deca639f5981ad895fe67"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">cutlass::Array&lt; T, N, true &gt;::reference</a></div><div class="ttdeci">value_type &amp; reference</div><div class="ttdef"><b>Definition:</b> array.h:101</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_aeab63d32f97e5046f55473d652a69bd7"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#aeab63d32f97e5046f55473d652a69bd7">cutlass::Array&lt; T, N, true &gt;::iterator::operator==</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator==(iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:156</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a76e1b5d728b155f9d967a43c0cc3b0dd"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a76e1b5d728b155f9d967a43c0cc3b0dd">cutlass::Array&lt; T, N, true &gt;::crend</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator crend() const </div><div class="ttdef"><b>Definition:</b> array.h:478</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_af95b8971433b4acec7e25c00167adb6a"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af95b8971433b4acec7e25c00167adb6a">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::reverse_iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator()</div><div class="ttdef"><b>Definition:</b> array.h:231</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_a2e7e50d7d99fda71be7ba03e0ce80417"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a2e7e50d7d99fda71be7ba03e0ce80417">cutlass::Array&lt; T, N, true &gt;::iterator::operator*</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T &amp; operator*() const </div><div class="ttdef"><b>Definition:</b> array.h:151</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html">cutlass::sizeof_bits</a></div><div class="ttdoc">Defines the size of an element in bits. </div><div class="ttdef"><b>Definition:</b> numeric_types.h:42</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_aa5a61ef108e3a2e2d56c4f7d54a38a1d"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#aa5a61ef108e3a2e2d56c4f7d54a38a1d">cutlass::Array&lt; T, N, true &gt;::iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator operator++(int)</div><div class="ttdef"><b>Definition:</b> array.h:137</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_a6837b73dd030c8d327041639fa8e8a27"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a6837b73dd030c8d327041639fa8e8a27">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::operator==</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator==(reverse_iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:268</div></div>
<div class="ttc" id="platform_8h_html_ab979d9d4b4923f7c54d6caa6e1a61936"><div class="ttname"><a href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a></div><div class="ttdeci">#define nullptr</div><div class="ttdoc">nullptr </div><div class="ttdef"><b>Definition:</b> platform.h:144</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_a719a46d965659e78b9b4b72ea22aa705"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a719a46d965659e78b9b4b72ea22aa705">cutlass::Array&lt; T, N, true &gt;::iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> array.h:125</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a70e53f314dc7b7bb6050486d18c14b31"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a70e53f314dc7b7bb6050486d18c14b31">cutlass::Array&lt; T, N, true &gt;::Storage</a></div><div class="ttdeci">T Storage</div><div class="ttdoc">Storage type. </div><div class="ttdef"><b>Definition:</b> array.h:82</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_af5056ae2c80aadeb027fffb8ed35f719"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#af5056ae2c80aadeb027fffb8ed35f719">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::reverse_iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reverse_iterator(T *_ptr)</div><div class="ttdef"><b>Definition:</b> array.h:234</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_ac50cf5822b023f4752d80f71963990cb"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#ac50cf5822b023f4752d80f71963990cb">cutlass::Array&lt; T, N, true &gt;::iterator::iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator(T *_ptr)</div><div class="ttdef"><b>Definition:</b> array.h:122</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_ae148a1e543b22c1c4ec20374bc8929b3"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#ae148a1e543b22c1c4ec20374bc8929b3">cutlass::Array&lt; T, N, true &gt;::const_iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> array.h:181</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a6c81a715431cf5a772c2273362df97fd"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6c81a715431cf5a772c2273362df97fd">cutlass::Array&lt; T, N, true &gt;::back</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reference back() const </div><div class="ttdef"><b>Definition:</b> array.h:394</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_a1bd27b5796ef75d377514d91ac8ab03c"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a1bd27b5796ef75d377514d91ac8ab03c">cutlass::Array&lt; T, N, true &gt;::iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator &amp; operator--()</div><div class="ttdef"><b>Definition:</b> array.h:131</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ab504833fe30934eeb6e71e235e7942f1"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab504833fe30934eeb6e71e235e7942f1">cutlass::Array&lt; T, N, true &gt;::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reference at(size_type pos) const </div><div class="ttdef"><b>Definition:</b> array.h:364</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator_html_a1c8dca20abc1818ddab6973280394888"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#a1c8dca20abc1818ddab6973280394888">cutlass::Array&lt; T, N, true &gt;::reverse_iterator::operator*</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T &amp; operator*() const </div><div class="ttdef"><b>Definition:</b> array.h:263</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_a25c770f60b9e8f8d7eb2e58efcb7c3e1"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a25c770f60b9e8f8d7eb2e58efcb7c3e1">cutlass::Array&lt; T, N, true &gt;::const_iterator::operator==</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator==(const_iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:212</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a5adbb5bb00cca5e538cd1215d1de08a4"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5adbb5bb00cca5e538cd1215d1de08a4">cutlass::Array&lt; T, N, true &gt;::front</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reference front()</div><div class="ttdef"><b>Definition:</b> array.h:379</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a7bf5b693d01e004852c642400d0e9b89"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7bf5b693d01e004852c642400d0e9b89">cutlass::Array&lt; T, N, true &gt;::Element</a></div><div class="ttdeci">T Element</div><div class="ttdoc">Element type. </div><div class="ttdef"><b>Definition:</b> array.h:85</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a90aaac40587e3ae5622030e999995f40"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a90aaac40587e3ae5622030e999995f40">cutlass::Array&lt; T, N, true &gt;::raw_data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_pointer raw_data() const </div><div class="ttdef"><b>Definition:</b> array.h:414</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a3391b79db2b9f3bac9576c9bc7af0402"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3391b79db2b9f3bac9576c9bc7af0402">cutlass::Array&lt; T, N, true &gt;::max_size</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr size_type max_size() const </div><div class="ttdef"><b>Definition:</b> array.h:430</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a3d3d2637b7051145a2048cff1b55c0bf"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3d3d2637b7051145a2048cff1b55c0bf">cutlass::Array&lt; T, N, true &gt;::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_pointer data() const </div><div class="ttdef"><b>Definition:</b> array.h:404</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_af5f68e8bd235243c74e7dc256fa37408"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#af5f68e8bd235243c74e7dc256fa37408">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::operator==</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool operator==(const_iterator const &amp;other) const </div><div class="ttdef"><b>Definition:</b> array.h:324</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_a0995262fdc623efc981e78c902487474"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a0995262fdc623efc981e78c902487474">cutlass::Array&lt; T, N, true &gt;::iterator::iterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator()</div><div class="ttdef"><b>Definition:</b> array.h:119</div></div>
<div class="ttc" id="namespacecutlass_html_a935aabfdc47cf03f87c67bb22533f97f"><div class="ttname"><a href="namespacecutlass.html#a935aabfdc47cf03f87c67bb22533f97f">cutlass::ispow2</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr bool ispow2(unsigned x)</div><div class="ttdoc">Returns true if the argument is a power of 2. </div><div class="ttdef"><b>Definition:</b> array.h:59</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a22af701f6f542b29198c759b653d3fb0"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a22af701f6f542b29198c759b653d3fb0">cutlass::Array&lt; T, N, true &gt;::Array</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array(Array const &amp;x)</div><div class="ttdef"><b>Definition:</b> array.h:345</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator_html_a42030a25dd5723f3c1b3944c168b6e22"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#a42030a25dd5723f3c1b3944c168b6e22">cutlass::Array&lt; T, N, true &gt;::iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE iterator operator--(int)</div><div class="ttdef"><b>Definition:</b> array.h:144</div></div>
<div class="ttc" id="namespacecutlass_html_ac16d8caf23537912eb02123c4bdacd14"><div class="ttname"><a href="namespacecutlass.html#ac16d8caf23537912eb02123c4bdacd14">cutlass::floor_pow_2</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr unsigned floor_pow_2(unsigned x)</div><div class="ttdoc">Returns the largest power of two not greater than the argument. </div><div class="ttdef"><b>Definition:</b> array.h:67</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_a9bbf80876fe3e72dbfe39ee880ec2f59"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a9bbf80876fe3e72dbfe39ee880ec2f59">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator operator--(int)</div><div class="ttdef"><b>Definition:</b> array.h:312</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a319dba33ebc8556e58f699f32c6a391b"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">cutlass::Array&lt; T, N, true &gt;::const_pointer</a></div><div class="ttdeci">value_type const * const_pointer</div><div class="ttdef"><b>Definition:</b> array.h:104</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_a16df2c0ef6340950eed8c080e18e062e"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a16df2c0ef6340950eed8c080e18e062e">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator &amp; operator--()</div><div class="ttdef"><b>Definition:</b> array.h:299</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a9109f9dc42faa978ac2f846b98b29eb9"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">cutlass::Array&lt; T, N, true &gt;::value_type</a></div><div class="ttdeci">T value_type</div><div class="ttdef"><b>Definition:</b> array.h:98</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a0fea9a8e9f9def4c0059bba750a95167"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0fea9a8e9f9def4c0059bba750a95167">cutlass::Array&lt; T, N, true &gt;::operator[]</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reference operator[](size_type pos)</div><div class="ttdef"><b>Definition:</b> array.h:369</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a815d434e9da9715a115896b3f6e64608"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a815d434e9da9715a115896b3f6e64608">cutlass::Array&lt; T, N, true &gt;::cbegin</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator cbegin() const </div><div class="ttdef"><b>Definition:</b> array.h:448</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a168315948a76d6ae9d7491ad0e1ca302"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a168315948a76d6ae9d7491ad0e1ca302">cutlass::Array&lt; T, N, true &gt;::Array</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array()</div><div class="ttdef"><b>Definition:</b> array.h:342</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_afc73e87dfebf9990e76aa47de2d30311"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#afc73e87dfebf9990e76aa47de2d30311">cutlass::Array&lt; T, N, true &gt;::const_iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator operator--(int)</div><div class="ttdef"><b>Definition:</b> array.h:200</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_a08821e8f1c7e5c614e38ac0b14787806"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a08821e8f1c7e5c614e38ac0b14787806">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reverse_iterator operator++(int)</div><div class="ttdef"><b>Definition:</b> array.h:305</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator_html_a49ed1c872c1640dca056fd4f8ced2261"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#a49ed1c872c1640dca056fd4f8ced2261">cutlass::Array&lt; T, N, true &gt;::const_reverse_iterator::operator*</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T const &amp; operator*() const </div><div class="ttdef"><b>Definition:</b> array.h:319</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_a0a692495c5f7a7d098e60b9292a07e4f"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0a692495c5f7a7d098e60b9292a07e4f">cutlass::Array&lt; T, N, true &gt;::front</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_reference front() const </div><div class="ttdef"><b>Definition:</b> array.h:384</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_aedd3e189bcbbb69ecd98978bcbbc3f1f"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aedd3e189bcbbb69ecd98978bcbbc3f1f">cutlass::Array&lt; T, N, true &gt;::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE reference at(size_type pos)</div><div class="ttdef"><b>Definition:</b> array.h:359</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator_html_a39085604c1b7a0dee3f5a0b96776d297"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#a39085604c1b7a0dee3f5a0b96776d297">cutlass::Array&lt; T, N, true &gt;::const_iterator::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_iterator &amp; operator--()</div><div class="ttdef"><b>Definition:</b> array.h:187</div></div>
<div class="ttc" id="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_html_ae67b1d98a446384fc75a1c92474e719d"><div class="ttname"><a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae67b1d98a446384fc75a1c92474e719d">cutlass::Array&lt; T, N, true &gt;::clear</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear()</div><div class="ttdoc">Efficient clear method. </div><div class="ttdef"><b>Definition:</b> array.h:354</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
