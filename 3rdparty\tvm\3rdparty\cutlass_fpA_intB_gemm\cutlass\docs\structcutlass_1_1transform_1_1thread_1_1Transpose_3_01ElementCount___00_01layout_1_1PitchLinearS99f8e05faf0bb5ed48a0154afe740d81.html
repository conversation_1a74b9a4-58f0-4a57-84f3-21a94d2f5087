<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::thread::Transpose&lt; ElementCount_, layout::PitchLinearShape&lt; 4, 4 &gt;, int8_t &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform_1_1thread.html">thread</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html">Transpose&lt; ElementCount_, layout::PitchLinearShape&lt; 4, 4 &gt;, int8_t &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS337c4bfbdb4aa0b08021c6d28539409f.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::thread::Transpose&lt; ElementCount_, layout::PitchLinearShape&lt; 4, 4 &gt;, int8_t &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Specialization for int8_t 4x4 transpose.  
</p>

<p><code>#include &lt;<a class="el" href="transpose_8h_source.html">transpose.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a438b633a80483f6dc58f6ab55976fd57"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a438b633a80483f6dc58f6ab55976fd57">TransposeShape</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 4, 4 &gt;</td></tr>
<tr class="separator:a438b633a80483f6dc58f6ab55976fd57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a732186d0cebf8c7ee2e1b0d6e7aa45dc"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a732186d0cebf8c7ee2e1b0d6e7aa45dc">Element</a> = int8_t</td></tr>
<tr class="separator:a732186d0cebf8c7ee2e1b0d6e7aa45dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab368c16371c6c8e5384fcbc9f4412ab6"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a> = cutlass::Array&lt; <a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a732186d0cebf8c7ee2e1b0d6e7aa45dc">Element</a>, <a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a5f267ce8d5c33de3d5b9eded4b4ee995">kElementCount</a> &gt;</td></tr>
<tr class="separator:ab368c16371c6c8e5384fcbc9f4412ab6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac2b77ac69bcb01ae623366cc020d6ecd"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ac2b77ac69bcb01ae623366cc020d6ecd">transform</a> (<a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a> &amp;dst, <a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a> &amp;src)</td></tr>
<tr class="separator:ac2b77ac69bcb01ae623366cc020d6ecd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a5f267ce8d5c33de3d5b9eded4b4ee995"><td class="memItemLeft" align="right" valign="top">static const int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a5f267ce8d5c33de3d5b9eded4b4ee995">kElementCount</a> = ElementCount_</td></tr>
<tr class="separator:a5f267ce8d5c33de3d5b9eded4b4ee995"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a732186d0cebf8c7ee2e1b0d6e7aa45dc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int ElementCount_&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">cutlass::transform::thread::Transpose</a>&lt; ElementCount_, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 4, 4 &gt;, int8_t &gt;::<a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a732186d0cebf8c7ee2e1b0d6e7aa45dc">Element</a> =  int8_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab368c16371c6c8e5384fcbc9f4412ab6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int ElementCount_&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">cutlass::transform::thread::Transpose</a>&lt; ElementCount_, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 4, 4 &gt;, int8_t &gt;::<a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a> =  cutlass::Array&lt;<a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a732186d0cebf8c7ee2e1b0d6e7aa45dc">Element</a>, <a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a5f267ce8d5c33de3d5b9eded4b4ee995">kElementCount</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a438b633a80483f6dc58f6ab55976fd57"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int ElementCount_&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">cutlass::transform::thread::Transpose</a>&lt; ElementCount_, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 4, 4 &gt;, int8_t &gt;::<a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a438b633a80483f6dc58f6ab55976fd57">TransposeShape</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;4,4&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ac2b77ac69bcb01ae623366cc020d6ecd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int ElementCount_&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE void <a class="el" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">cutlass::transform::thread::Transpose</a>&lt; ElementCount_, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 4, 4 &gt;, int8_t &gt;::transform </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a> &amp;&#160;</td>
          <td class="paramname"><em>dst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a> &amp;&#160;</td>
          <td class="paramname"><em>src</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a5f267ce8d5c33de3d5b9eded4b4ee995"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int ElementCount_&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const int <a class="el" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">cutlass::transform::thread::Transpose</a>&lt; ElementCount_, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 4, 4 &gt;, int8_t &gt;::kElementCount = ElementCount_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="transpose_8h_source.html">transpose.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
