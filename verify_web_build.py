#!/usr/bin/env python3
"""
Verify MLC-LLM Web build without starting server
"""

import os
from pathlib import Path

def verify_web_build():
    """Verify web build files exist and are valid"""
    
    print("🔍 Verifying MLC-LLM Web Build...")
    print("=" * 50)
    
    # Check required files
    files_to_check = [
        ("web/demo.html", "Demo HTML page"),
        ("web/dist/wasm/mlc_wasm_runtime.wasm", "MLC WASM Runtime"),
        ("3rdparty/tvm/web/dist/wasm/wasm_runtime.wasm", "TVM WASM Runtime"),
    ]
    
    all_good = True
    
    for file_path, description in files_to_check:
        path = Path(file_path)
        if path.exists():
            size = path.stat().st_size
            if size > 0:
                if size > 1024 * 1024:  # > 1MB
                    size_str = f"{size / (1024*1024):.2f} MB"
                elif size > 1024:  # > 1KB
                    size_str = f"{size / 1024:.1f} KB"
                else:
                    size_str = f"{size} bytes"
                
                print(f"✅ {description}")
                print(f"   📁 {file_path}")
                print(f"   📊 {size_str}")
                
                # Verify WASM files
                if file_path.endswith('.wasm'):
                    try:
                        with open(path, 'rb') as f:
                            magic = f.read(4)
                            if magic == b'\x00asm':
                                print(f"   🔧 Valid WebAssembly file")
                            else:
                                print(f"   ⚠️  Magic bytes: {magic} (expected: b'\\x00asm')")
                    except Exception as e:
                        print(f"   ❌ Error reading file: {e}")
                        all_good = False
            else:
                print(f"❌ {description} - File is empty")
                all_good = False
        else:
            print(f"❌ {description} - File not found: {file_path}")
            all_good = False
        print()
    
    # Check demo.html content
    demo_path = Path("web/demo.html")
    if demo_path.exists():
        try:
            content = demo_path.read_text(encoding='utf-8')
            if "MLC-LLM Web Demo" in content and "WebAssembly" in content:
                print("✅ Demo HTML contains expected content")
            else:
                print("⚠️  Demo HTML may be incomplete")
        except Exception as e:
            print(f"❌ Error reading demo.html: {e}")
            all_good = False
    
    print("=" * 50)
    
    if all_good:
        print("🎉 Web Build Verification: SUCCESS!")
        print()
        print("📋 Build Summary:")
        print("✅ All required files present and valid")
        print("✅ WebAssembly files have correct magic numbers")
        print("✅ Demo page ready for browser testing")
        print()
        print("🚀 Usage Instructions:")
        print("1. Start a web server in the 'web' directory:")
        print("   cd web && python -m http.server 8080")
        print("2. Open browser to: http://localhost:8080/demo.html")
        print("3. Test WebAssembly loading and functionality")
        print()
        print("🌐 Integration:")
        print("- Use mlc_wasm_runtime.wasm in your web applications")
        print("- Compatible with WebLLM and other ML frameworks")
        print("- Supports modern browsers with WebAssembly")
        
        return True
    else:
        print("❌ Web Build Verification: FAILED!")
        print("Some files are missing or invalid.")
        return False

if __name__ == "__main__":
    success = verify_web_build()
    exit(0 if success else 1)
