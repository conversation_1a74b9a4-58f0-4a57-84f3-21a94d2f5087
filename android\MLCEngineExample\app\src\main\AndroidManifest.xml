<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="ai.mlc.mlcengineexample">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/mlc_logo_108"
        android:label="@string/app_name"
        android:roundIcon="@drawable/mlc_logo_108"
        android:supportsRtl="true"
        android:theme="@style/Theme.MLCEngineExample"
        tools:targetApi="31">
        <uses-native-library
            android:name="libOpenCL.so"
            android:required="false"/>

        <uses-native-library
            android:name="libOpenCL-pixel.so"
            android:required="false" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Material.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>
