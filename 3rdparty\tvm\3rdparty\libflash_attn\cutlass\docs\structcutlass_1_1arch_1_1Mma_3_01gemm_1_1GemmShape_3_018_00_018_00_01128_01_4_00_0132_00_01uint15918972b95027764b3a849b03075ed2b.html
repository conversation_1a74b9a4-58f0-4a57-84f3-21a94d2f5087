<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 128 &gt;, 32, uint1b_t, layout::RowMajor, uint1b_t, layout::ColumnMajor, int, layout::RowMajor, OpXorPopc &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1arch.html">arch</a></li><li class="navelem"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html">Mma&lt; gemm::GemmShape&lt; 8, 8, 128 &gt;, 32, uint1b_t, layout::RowMajor, uint1b_t, layout::ColumnMajor, int, layout::RowMajor, OpXorPopc &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint193e4529ff6509d9dffe61a902bae1f87.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 128 &gt;, 32, uint1b_t, layout::RowMajor, uint1b_t, layout::ColumnMajor, int, layout::RowMajor, OpXorPopc &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Matrix multiply-add operation.  
</p>

<p><code>#include &lt;<a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a74666a5fe3ec0d190bde1e85b77ec5f3"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a74666a5fe3ec0d190bde1e85b77ec5f3">Shape</a> = <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;</td></tr>
<tr class="separator:a74666a5fe3ec0d190bde1e85b77ec5f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a702c4ee51ff5c234ec721ba0bc83c728"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a702c4ee51ff5c234ec721ba0bc83c728">ElementA</a> = <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a></td></tr>
<tr class="separator:a702c4ee51ff5c234ec721ba0bc83c728"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b36cb6bd0c15c32da7fe128901748f6"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a9b36cb6bd0c15c32da7fe128901748f6">LayoutA</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a9b36cb6bd0c15c32da7fe128901748f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7d755f4766981293b04cf235c55405e"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#ae7d755f4766981293b04cf235c55405e">FragmentA</a> = Array&lt; <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, 32 &gt;</td></tr>
<tr class="separator:ae7d755f4766981293b04cf235c55405e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c3be5969dd7a9d2f34a622979d1684a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a3c3be5969dd7a9d2f34a622979d1684a">ElementB</a> = <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a></td></tr>
<tr class="separator:a3c3be5969dd7a9d2f34a622979d1684a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19199abbcfff67f4e32fb9246899011e"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a19199abbcfff67f4e32fb9246899011e">LayoutB</a> = <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td></tr>
<tr class="separator:a19199abbcfff67f4e32fb9246899011e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af82ee2181ddcd4551eac17dfb758bbc7"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#af82ee2181ddcd4551eac17dfb758bbc7">FragmentB</a> = Array&lt; <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, 32 &gt;</td></tr>
<tr class="separator:af82ee2181ddcd4551eac17dfb758bbc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f0386f2a19def7dc75495bbc8b24302"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a8f0386f2a19def7dc75495bbc8b24302">ElementC</a> = int</td></tr>
<tr class="separator:a8f0386f2a19def7dc75495bbc8b24302"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a544ceebbea23bbefa1aed667f02ac2c3"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a544ceebbea23bbefa1aed667f02ac2c3">LayoutC</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a544ceebbea23bbefa1aed667f02ac2c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae7a4f4758e8d71abdf1b5f83c083cfe"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#aae7a4f4758e8d71abdf1b5f83c083cfe">FragmentC</a> = Array&lt; int, 2 &gt;</td></tr>
<tr class="separator:aae7a4f4758e8d71abdf1b5f83c083cfe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a573acef5c55968fc838a0827e51279c7"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a573acef5c55968fc838a0827e51279c7">Operator</a> = OpXorPopc</td></tr>
<tr class="separator:a573acef5c55968fc838a0827e51279c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab23a775098428cdf19a6a9a4f03e2506"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#ab23a775098428cdf19a6a9a4f03e2506">operator()</a> (<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#aae7a4f4758e8d71abdf1b5f83c083cfe">FragmentC</a> &amp;d, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#ae7d755f4766981293b04cf235c55405e">FragmentA</a> const &amp;a, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#af82ee2181ddcd4551eac17dfb758bbc7">FragmentB</a> const &amp;b, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#aae7a4f4758e8d71abdf1b5f83c083cfe">FragmentC</a> const &amp;c) const </td></tr>
<tr class="memdesc:ab23a775098428cdf19a6a9a4f03e2506"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes multiply-add.  <a href="#ab23a775098428cdf19a6a9a4f03e2506">More...</a><br /></td></tr>
<tr class="separator:ab23a775098428cdf19a6a9a4f03e2506"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a702c4ee51ff5c234ec721ba0bc83c728"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a702c4ee51ff5c234ec721ba0bc83c728">ElementA</a> =  <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3c3be5969dd7a9d2f34a622979d1684a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a3c3be5969dd7a9d2f34a622979d1684a">ElementB</a> =  <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8f0386f2a19def7dc75495bbc8b24302"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a8f0386f2a19def7dc75495bbc8b24302">ElementC</a> =  int</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae7d755f4766981293b04cf235c55405e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#ae7d755f4766981293b04cf235c55405e">FragmentA</a> =  Array&lt;<a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, 32&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af82ee2181ddcd4551eac17dfb758bbc7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#af82ee2181ddcd4551eac17dfb758bbc7">FragmentB</a> =  Array&lt;<a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, 32&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aae7a4f4758e8d71abdf1b5f83c083cfe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#aae7a4f4758e8d71abdf1b5f83c083cfe">FragmentC</a> =  Array&lt;int, 2&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9b36cb6bd0c15c32da7fe128901748f6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a9b36cb6bd0c15c32da7fe128901748f6">LayoutA</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a19199abbcfff67f4e32fb9246899011e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a19199abbcfff67f4e32fb9246899011e">LayoutB</a> =  <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a544ceebbea23bbefa1aed667f02ac2c3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a544ceebbea23bbefa1aed667f02ac2c3">LayoutC</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a573acef5c55968fc838a0827e51279c7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a573acef5c55968fc838a0827e51279c7">Operator</a> =  OpXorPopc</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a74666a5fe3ec0d190bde1e85b77ec5f3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#a74666a5fe3ec0d190bde1e85b77ec5f3">Shape</a> =  <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt;8,8,128&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ab23a775098428cdf19a6a9a4f03e2506"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 128 &gt;, 32, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpXorPopc &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#aae7a4f4758e8d71abdf1b5f83c083cfe">FragmentC</a> &amp;&#160;</td>
          <td class="paramname"><em>d</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#ae7d755f4766981293b04cf235c55405e">FragmentA</a> const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#af82ee2181ddcd4551eac17dfb758bbc7">FragmentB</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_01128_01_4_00_0132_00_01uint15918972b95027764b3a849b03075ed2b.html#aae7a4f4758e8d71abdf1b5f83c083cfe">FragmentC</a> const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
