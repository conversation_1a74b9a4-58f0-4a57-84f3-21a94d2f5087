package ai.mlc.mlcllm;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Build;
import android.util.Log;
import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * Utility functions for MLC-LLM Android
 * Provides helper methods for model management, file operations, and system info
 */
public class MLCUtils {
    private static final String TAG = "MLCUtils";
    
    /**
     * Get the recommended model path for the given model name
     */
    public static String getModelPath(Context context, String modelName) {
        return context.getFilesDir().getAbsolutePath() + "/models/" + modelName;
    }
    
    /**
     * Get the cache directory for temporary files
     */
    public static String getCachePath(Context context) {
        return context.getCacheDir().getAbsolutePath() + "/mlc_cache";
    }
    
    /**
     * Copy an asset file to internal storage
     */
    public static boolean copyAssetToFile(Context context, String assetName, String targetPath) {
        try {
            AssetManager assetManager = context.getAssets();
            InputStream in = assetManager.open(assetName);
            
            File targetFile = new File(targetPath);
            targetFile.getParentFile().mkdirs();
            
            FileOutputStream out = new FileOutputStream(targetFile);
            
            byte[] buffer = new byte[8192];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            
            in.close();
            out.close();
            
            Log.i(TAG, "Asset copied: " + assetName + " -> " + targetPath);
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Failed to copy asset: " + assetName, e);
            return false;
        }
    }
    
    /**
     * Extract a ZIP file to a directory
     */
    public static boolean extractZip(String zipPath, String extractPath) {
        try {
            File extractDir = new File(extractPath);
            extractDir.mkdirs();
            
            ZipInputStream zis = new ZipInputStream(new FileInputStream(zipPath));
            ZipEntry entry;
            
            while ((entry = zis.getNextEntry()) != null) {
                File entryFile = new File(extractDir, entry.getName());
                
                if (entry.isDirectory()) {
                    entryFile.mkdirs();
                } else {
                    entryFile.getParentFile().mkdirs();
                    
                    FileOutputStream fos = new FileOutputStream(entryFile);
                    byte[] buffer = new byte[8192];
                    int read;
                    while ((read = zis.read(buffer)) != -1) {
                        fos.write(buffer, 0, read);
                    }
                    fos.close();
                }
                zis.closeEntry();
            }
            zis.close();
            
            Log.i(TAG, "ZIP extracted: " + zipPath + " -> " + extractPath);
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Failed to extract ZIP: " + zipPath, e);
            return false;
        }
    }
    
    /**
     * Get device architecture information
     */
    public static String getDeviceArchitecture() {
        return Build.SUPPORTED_ABIS[0];
    }
    
    /**
     * Get all supported architectures
     */
    public static String[] getSupportedArchitectures() {
        return Build.SUPPORTED_ABIS;
    }
    
    /**
     * Check if the device supports the required architecture
     */
    public static boolean isArchitectureSupported(String requiredArch) {
        for (String arch : Build.SUPPORTED_ABIS) {
            if (arch.equals(requiredArch)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get device memory information
     */
    public static long getAvailableMemory(Context context) {
        android.app.ActivityManager activityManager = 
            (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        android.app.ActivityManager.MemoryInfo memoryInfo = 
            new android.app.ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);
        return memoryInfo.availMem;
    }
    
    /**
     * Check if file exists and is readable
     */
    public static boolean isFileValid(String filePath) {
        File file = new File(filePath);
        return file.exists() && file.canRead() && file.length() > 0;
    }
    
    /**
     * Get file size in bytes
     */
    public static long getFileSize(String filePath) {
        File file = new File(filePath);
        return file.exists() ? file.length() : 0;
    }
    
    /**
     * Format bytes to human readable string
     */
    public static String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
    }
    
    /**
     * Get system information for debugging
     */
    public static String getSystemInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Device: ").append(Build.MANUFACTURER).append(" ").append(Build.MODEL).append("\n");
        info.append("Android: ").append(Build.VERSION.RELEASE).append(" (API ").append(Build.VERSION.SDK_INT).append(")\n");
        info.append("Architecture: ").append(getDeviceArchitecture()).append("\n");
        info.append("Supported ABIs: ");
        for (int i = 0; i < Build.SUPPORTED_ABIS.length; i++) {
            if (i > 0) info.append(", ");
            info.append(Build.SUPPORTED_ABIS[i]);
        }
        info.append("\n");
        return info.toString();
    }
    
    /**
     * Create directory if it doesn't exist
     */
    public static boolean ensureDirectory(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            Log.d(TAG, "Directory created: " + dirPath + " -> " + created);
            return created;
        }
        return true;
    }
    
    /**
     * Delete file or directory recursively
     */
    public static boolean deleteRecursively(File file) {
        if (file.isDirectory()) {
            File[] children = file.listFiles();
            if (children != null) {
                for (File child : children) {
                    if (!deleteRecursively(child)) {
                        return false;
                    }
                }
            }
        }
        boolean deleted = file.delete();
        Log.d(TAG, "Deleted: " + file.getAbsolutePath() + " -> " + deleted);
        return deleted;
    }
}
