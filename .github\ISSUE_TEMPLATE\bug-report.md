---
name: "🐛 Bug Report"
about: Submit a bug report to help us improve MLC-LLM
title: '[Bug] '
labels: ['bug']
assignees: ''

---

## 🐛 Bug

<!-- A clear and concise description of what the bug is. -->

## To Reproduce

Steps to reproduce the behavior:

1.
1.
1.

<!-- If you have a code sample, error messages, stack traces, please provide it here as well -->

## Expected behavior

<!-- A clear and concise description of what you expected to happen. -->

## Environment

 - Platform (e.g. WebGPU/Vulkan/IOS/Android/CUDA):
 - Operating system (e.g. Ubuntu/Windows/MacOS/...):
 - Device (e.g. iPhone 12 Pro, PC+RTX 3090, ...)
 - How you installed MLC-LLM (`conda`, source):
 - How you installed TVM-Unity (`pip`, source):
 - Python version (e.g. 3.10):
 - GPU driver version (if applicable):
 - CUDA/cuDNN version (if applicable):
 - TVM Unity Hash Tag (`python -c "import tvm; print('\n'.join(f'{k}: {v}' for k, v in tvm.support.libinfo().items()))"`, applicable if you compile models):
 - Any other relevant information:

## Additional context

<!-- Add any other context about the problem here. -->
