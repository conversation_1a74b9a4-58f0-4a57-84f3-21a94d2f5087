<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: volta_tensor_op_policy.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">volta_tensor_op_policy.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="volta__tensor__op__policy_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__shape_8h.html">cutlass/matrix_shape.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  <span class="keyword">typename</span> WarpShape,             </div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keyword">typename</span> InterleavedTileShape,  </div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">typename</span> ElementC,              </div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keyword">typename</span> Layout                 </div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;&gt;</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">   52</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">VoltaTensorOpPolicy</a>; </div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keyword">typename</span> WarpShape_          </div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;&gt;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html">   60</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">VoltaTensorOpPolicy</a>&lt;WarpShape_, gemm::GemmShape&lt;32, 32, 4&gt;, <a class="code" href="structcutlass_1_1half__t.html">half_t</a>, <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>&gt; {</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a669b3b630c5166bd25d058974ba21018">   62</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a669b3b630c5166bd25d058974ba21018">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a174c54d744433e9fb55d70b9d3298a3f">   63</a></span>&#160;  <span class="keyword">using</span> InterleavedTileShape = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;32, 32, 4&gt;</a>;</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a73ad5cdfdcdfbbe891d6770769a2d7c9">   64</a></span>&#160;  <span class="keyword">using</span> ElementC = <a class="code" href="structcutlass_1_1half__t.html">half_t</a>;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a4bbecd66d0179c7c9c75addaecd17529">   65</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#aee2b5d5ef9bc8815ad484d95827452f4">   68</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">InstructionShape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;16, 16, 4&gt;</a>;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">MmaIterations</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    InterleavedTileShape::kM / InstructionShape::kM,</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;    InterleavedTileShape::kN / InstructionShape::kN</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a9c9f7ef856b9f44a823bcf054ab945c3">   74</a></span>&#160;  &gt;;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">TileIterations</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    WarpShape::kM / InterleavedTileShape::kM,</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    WarpShape::kN / InterleavedTileShape::kN</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a2b903cfb5856bdcf1613979efcc2a79e">   80</a></span>&#160;  &gt;;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a0c87fbb52f690deb4316a416660e9ca6">   83</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerMma = 8;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a1fa606c44c994a088ae186bd700de2a8">   84</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerIteration = 16;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <span class="comment">// Hard-coded constants regarding Tensor Operations</span></div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a3616da22cf9fac3bea048413778e5694">   91</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = 4;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  </div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a61170e3b111822d0f43c0b7b9d2b6d85">   94</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessesPerInterleavedTile = 4;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a5adc29eec21790473e9fa5c1129b7324">   97</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = TileIterations::kRow * 2;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <span class="comment">// Derived types</span></div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a2015fad226bfcb32ced0435861b268db">  104</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray&lt;ElementC, kElementsPerAccess&gt;</a>;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#abf178c47cbefcedf0a692ae3327ec5d2">Fragment</a> = Array&lt;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    ElementC, </div><div class="line"><a name="l00109"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#abf178c47cbefcedf0a692ae3327ec5d2">  109</a></span>&#160;    kElementsPerAccess * kAccessesPerInterleavedTile * TileIterations::kColumn&gt;;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a21f78a099c4ac53e13fb84757c88c7be">AccumulatorTile</a> = Array&lt;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    ElementC, </div><div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a21f78a099c4ac53e13fb84757c88c7be">  114</a></span>&#160;    TileIterations::kCount * MmaIterations::kCount * kElementsPerMma&gt;;</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;};</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <span class="keyword">typename</span> WarpShape_          </div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;&gt;</div><div class="line"><a name="l00123"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html">  123</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">VoltaTensorOpPolicy</a>&lt;WarpShape_, gemm::GemmShape&lt;32, 32, 4&gt;, float, <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>&gt; {</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#accce29bb04fe290815f12935a40c7080">  125</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#accce29bb04fe290815f12935a40c7080">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a94d42cddc58ca3112e572c2e7e201d0e">  126</a></span>&#160;  <span class="keyword">using</span> InterleavedTileShape = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;32, 32, 4&gt;</a>;</div><div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a8ea39786164f95cafc548651be64f481">  127</a></span>&#160;  <span class="keyword">using</span> ElementC = float;</div><div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a55da51a0f629e4770da71bd7238cd6c8">  128</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a2553198647e304e1b2e262169e89adaa">  131</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">InstructionShape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;16, 16, 4&gt;</a>;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">MmaIterations</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    InterleavedTileShape::kM / InstructionShape::kM,</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    InterleavedTileShape::kN / InstructionShape::kN</div><div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#aa1ae4efe90c09565eb128c5899b0bd74">  137</a></span>&#160;  &gt;;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">TileIterations</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    WarpShape::kM / InterleavedTileShape::kM,</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    WarpShape::kN / InterleavedTileShape::kN</div><div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a3a495900f3b74f07612f7f175403a6ca">  143</a></span>&#160;  &gt;;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a6d6baf51c49ea98de2db365806f13326">  146</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerMma = 8;</div><div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a16272abda78ae3fee0cc136b519e1f75">  147</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerIteration = 16;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  <span class="comment">// Hard-coded constants regarding Tensor Operations</span></div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#afcf8488c1b19e0cc44709fe5b8cd0407">  154</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = 2;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  </div><div class="line"><a name="l00157"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a5df324e55af9ac23dea4056f6e5b9bd5">  157</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessesPerInterleavedTile = 8;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#a58d2cf6dfbc615b0e71d9d47a2835265">  160</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerMmaTile = 2;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div><div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#aea54d5805f9b49122c1bf4147912b4ff">  163</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = TileIterations::kRow * MmaIterations::kRow;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;  <span class="comment">// Derived types</span></div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;  </div><div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#ac4b649583277d5a7d314c3cc92929d77">  170</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray&lt;ElementC, kElementsPerAccess&gt;</a>;</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#ae38ddb7de2a44dc71842e289a8416ca7">Fragment</a> = Array&lt;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    ElementC, </div><div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#ae38ddb7de2a44dc71842e289a8416ca7">  175</a></span>&#160;    kElementsPerAccess * kAccessesPerInterleavedTile * TileIterations::kColumn&gt;;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#aef2c522fb1ca3a77f3a7d00a8fd67910">AccumulatorTile</a> = Array&lt;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;    ElementC, </div><div class="line"><a name="l00180"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#aef2c522fb1ca3a77f3a7d00a8fd67910">  180</a></span>&#160;    TileIterations::kCount * MmaIterations::kCount * kElementsPerMma&gt;;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;};</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194_html_ae38ddb7de2a44dc71842e289a8416ca7"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#ae38ddb7de2a44dc71842e289a8416ca7">cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">Array&lt; ElementC, kElementsPerAccess *kAccessesPerInterleavedTile *TileIterations::kColumn &gt; Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:175</div></div>
<div class="ttc" id="classcutlass_1_1AlignedArray_html"><div class="ttname"><a href="classcutlass_1_1AlignedArray.html">cutlass::AlignedArray</a></div><div class="ttdoc">Aligned array type. </div><div class="ttdef"><b>Definition:</b> array.h:511</div></div>
<div class="ttc" id="structcutlass_1_1half__t_html"><div class="ttname"><a href="structcutlass_1_1half__t.html">cutlass::half_t</a></div><div class="ttdoc">IEEE half-precision floating-point type. </div><div class="ttdef"><b>Definition:</b> half.h:126</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="matrix__shape_8h_html"><div class="ttname"><a href="matrix__shape_8h.html">matrix_shape.h</a></div><div class="ttdoc">Defines a Shape template for matrix tiles. </div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194_html_accce29bb04fe290815f12935a40c7080"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#accce29bb04fe290815f12935a40c7080">cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:125</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f_html_abf178c47cbefcedf0a692ae3327ec5d2"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#abf178c47cbefcedf0a692ae3327ec5d2">cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">Array&lt; ElementC, kElementsPerAccess *kAccessesPerInterleavedTile *TileIterations::kColumn &gt; Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:109</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">cutlass::epilogue::warp::VoltaTensorOpPolicy</a></div><div class="ttdoc">Policy details related to the epilogue. </div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:52</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194_html_aef2c522fb1ca3a77f3a7d00a8fd67910"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_136ce744d4c1c6e8707f5a9785196194.html#aef2c522fb1ca3a77f3a7d00a8fd67910">cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">Array&lt; ElementC, TileIterations::kCount *MmaIterations::kCount *kElementsPerMma &gt; AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:180</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f_html_a21f78a099c4ac53e13fb84757c88c7be"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a21f78a099c4ac53e13fb84757c88c7be">cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">Array&lt; ElementC, TileIterations::kCount *MmaIterations::kCount *kElementsPerMma &gt; AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:114</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f_html_a669b3b630c5166bd25d058974ba21018"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_3_01WarpShape___00_01gemm_1_1GemmShape_1d48185f49e4d066f8e9327bf0856b7f.html#a669b3b630c5166bd25d058974ba21018">cutlass::epilogue::warp::VoltaTensorOpPolicy&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:62</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
