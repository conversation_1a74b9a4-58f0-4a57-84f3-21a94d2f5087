<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: mma_sm50.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">arch/mma_sm50.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="arch_2mma__sm50_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="arch_2mma_8h.html">cutlass/arch/mma.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="complex_8h.html">cutlass/complex.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">namespace </span>arch {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;&gt;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_004bb3fd76ca2af7b3210676fa9644d95b.html">   53</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;gemm::GemmShape&lt;1, 1, 1&gt;, 1, float, LayoutA, float, LayoutB, float, LayoutC, OpMultiplyAdd&gt; {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_004bb3fd76ca2af7b3210676fa9644d95b.html#a782d6a8a48b3ab0ff1eead092d348aef">   55</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_004bb3fd76ca2af7b3210676fa9644d95b.html#a47e6d9cb8a24ae8246272985741f7f0d">   58</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_004bb3fd76ca2af7b3210676fa9644d95b.html#a47e6d9cb8a24ae8246272985741f7f0d">operator()</a>(</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    Array&lt;float, 1&gt; &amp;d,</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    Array&lt;float, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    Array&lt;float, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    Array&lt;float, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  ) {</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    d[0] = a[0] * b[0] + c[0];</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  }</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;};</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;&gt;</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_0aa57e6a2e6b5da37d10688bf99419a23.html">   79</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;gemm::GemmShape&lt;1, 1, 1&gt;, 1, double, LayoutA, double, LayoutB, double, LayoutC, OpMultiplyAdd&gt; {</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_0aa57e6a2e6b5da37d10688bf99419a23.html#a25202ab89ec2def5b1bd9eec2cf6033c">   81</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_0aa57e6a2e6b5da37d10688bf99419a23.html#acc4d8ede06a490f3fbfec8e3dd75b0b1">   84</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_0aa57e6a2e6b5da37d10688bf99419a23.html#acc4d8ede06a490f3fbfec8e3dd75b0b1">operator()</a>(</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    Array&lt;double, 1&gt; &amp;d,</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    Array&lt;double, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    Array&lt;double, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    Array&lt;double, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;  ) {</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    d[0] = a[0] * b[0] + c[0];</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  }</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;};</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;&gt;</div><div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01int_00_00b2dff9ce8caad9aff5bc6a355539161.html">  106</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;gemm::GemmShape&lt;1, 1, 1&gt;, 1, int, LayoutA, int, LayoutB, int, LayoutC, OpMultiplyAdd&gt; {</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01int_00_00b2dff9ce8caad9aff5bc6a355539161.html#ae7c95bf7586a4586b9fbb10d002219e1">  108</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01int_00_00b2dff9ce8caad9aff5bc6a355539161.html#a147ecaa2af6851b26a17eb8f8d95f9d0">  111</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01int_00_00b2dff9ce8caad9aff5bc6a355539161.html#a147ecaa2af6851b26a17eb8f8d95f9d0">operator()</a>(</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;    Array&lt;int, 1&gt; &amp;d,</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    Array&lt;int, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    Array&lt;int, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;    Array&lt;int, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  ) {</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    d[0] = a[0] * b[0] + c[0];</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  }</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;};</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;&gt;</div><div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_76f9d24016e1b4167b16f4d7628c9546.html">  133</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  gemm::GemmShape&lt;1, 1, 1&gt;,</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  1,</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt;, </div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  LayoutA, </div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt;, </div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  LayoutB, </div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt;, </div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  LayoutC, </div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  OpMultiplyAdd&gt; {</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div><div class="line"><a name="l00144"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_76f9d24016e1b4167b16f4d7628c9546.html#ac9f5444de09469501776e60a42bd0c34">  144</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_76f9d24016e1b4167b16f4d7628c9546.html#ae914107ff2d7b1524cc9f8c70237a8f9">  147</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_76f9d24016e1b4167b16f4d7628c9546.html#ae914107ff2d7b1524cc9f8c70237a8f9">operator()</a>(</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; &amp;d,</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;  ) {</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    d[0].real() = a[0].real() * b[0].real() + c[0].real();</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    d[0].imag() = a[0].imag() * b[0].real() + c[0].imag();</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    d[0].real() = -a[0].imag() * b[0].imag() + d[0].real();</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    d[0].imag() = a[0].real() * b[0].imag() + d[0].imag();</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;  }</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;};</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;&gt;</div><div class="line"><a name="l00172"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_f1c9d2ee842455cd0c5b71d56108d468.html">  172</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;  gemm::GemmShape&lt;1, 1, 1&gt;,</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  1,</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt;, </div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;  LayoutA, </div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  float, </div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;  LayoutB, </div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt;, </div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;  LayoutC, </div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  OpMultiplyAdd&gt; {</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_f1c9d2ee842455cd0c5b71d56108d468.html#a2dc603e5f509e53cb54b7091c2f15c9c">  183</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_f1c9d2ee842455cd0c5b71d56108d468.html#a3b69fa99a09158d9be274651f6b74980">  186</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_f1c9d2ee842455cd0c5b71d56108d468.html#a3b69fa99a09158d9be274651f6b74980">operator()</a>(</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; &amp;d,</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    Array&lt;float, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;  ) {</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    d[0].real() = a[0].real() * b[0] + c[0].real();</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    d[0].imag() = a[0].imag() * b[0] + c[0].imag();</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;  }</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;};</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;&gt;</div><div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_00e3e12e263df6506b8cf06c3f4d478b8e.html">  209</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;  gemm::GemmShape&lt;1, 1, 1&gt;,</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  1,</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  float, </div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  LayoutA, </div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt;, </div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  LayoutB, </div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt;, </div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;  LayoutC, </div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;  OpMultiplyAdd&gt; {</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00220"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_00e3e12e263df6506b8cf06c3f4d478b8e.html#a583b65a74d484e480f400c2190486951">  220</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00223"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_00e3e12e263df6506b8cf06c3f4d478b8e.html#a175c9e89f95837838e533687f4c4078d">  223</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_00e3e12e263df6506b8cf06c3f4d478b8e.html#a175c9e89f95837838e533687f4c4078d">operator()</a>(</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; &amp;d,</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    Array&lt;float, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  ) {</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    d[0].real() = a[0] * b[0].real() + c[0].real();</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;    d[0].imag() = a[0] * b[0].imag() + d[0].imag();</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;  }</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;};</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;&gt;</div><div class="line"><a name="l00246"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_30fa42e1ad201df010637cd22fc070a1.html">  246</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;  gemm::GemmShape&lt;1, 1, 1&gt;,</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;  1,</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt;, </div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;  LayoutA, </div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt;, </div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;  LayoutB, </div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt;, </div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;  LayoutC, </div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;  OpMultiplyAdd&gt; {</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_30fa42e1ad201df010637cd22fc070a1.html#a62044fc37f00508f89509ed76b86cb5a">  257</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00260"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_30fa42e1ad201df010637cd22fc070a1.html#af37b41df0067e4c878baafc8d71574d9">  260</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_30fa42e1ad201df010637cd22fc070a1.html#af37b41df0067e4c878baafc8d71574d9">operator()</a>(</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; &amp;d,</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;  ) {</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;    d[0].real() = a[0].real() * b[0].real() + c[0].real();</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;    d[0].imag() = a[0].imag() * b[0].real() + c[0].imag();</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;    d[0].real() = -a[0].imag() * b[0].imag() + d[0].real();</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    d[0].imag() = a[0].real() * b[0].imag() + d[0].imag();</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;  }</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;};</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;&gt;</div><div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_48b3a43bc03fff93a111ac01abe7e40d.html">  283</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;  gemm::GemmShape&lt;1, 1, 1&gt;,</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;  1,</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt;, </div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;  LayoutA, </div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;  double, </div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;  LayoutB, </div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt;, </div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;  LayoutC, </div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;  OpMultiplyAdd&gt; {</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;</div><div class="line"><a name="l00294"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_48b3a43bc03fff93a111ac01abe7e40d.html#ac696059d2fc99e1840452127ec04edb9">  294</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00297"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_48b3a43bc03fff93a111ac01abe7e40d.html#a8f8180dcd03dad7b45d73f09e87c060b">  297</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_48b3a43bc03fff93a111ac01abe7e40d.html#a8f8180dcd03dad7b45d73f09e87c060b">operator()</a>(</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; &amp;d,</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;    Array&lt;double, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;  ) {</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;    d[0].real() = a[0].real() * b[0] + c[0].real();</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;    d[0].imag() = a[0].imag() * b[0] + c[0].imag();</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;  }</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;};</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;&gt;</div><div class="line"><a name="l00318"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_070b94670e040ed5855e5b42d5ca8a443.html">  318</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;  gemm::GemmShape&lt;1, 1, 1&gt;,</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;  1,</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;  double, </div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;  LayoutA, </div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt;, </div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;  LayoutB, </div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt;, </div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;  LayoutC, </div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;  OpMultiplyAdd&gt; {</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;</div><div class="line"><a name="l00329"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_070b94670e040ed5855e5b42d5ca8a443.html#aa8429d8cbbafbbf17f40cdbf040ba1c1">  329</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00332"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_070b94670e040ed5855e5b42d5ca8a443.html#a672562083112f8463a9791113482c4e9">  332</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_070b94670e040ed5855e5b42d5ca8a443.html#a672562083112f8463a9791113482c4e9">operator()</a>(</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; &amp;d,</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    Array&lt;double, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;    Array&lt;<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;  ) {</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;    d[0].real() = a[0] * b[0].real() + c[0].real();</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;    d[0].imag() = a[0] * b[0].imag() + d[0].imag();</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;  }</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;};</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;  <span class="keyword">typename</span> LayoutC</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;&gt;</div><div class="line"><a name="l00355"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01half__t_4f30ee91f7bb3844ff7579c68d078818.html">  355</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;gemm::GemmShape&lt;1, 1, 1&gt;, 1, <a class="code" href="structcutlass_1_1half__t.html">half_t</a>, LayoutA, <a class="code" href="structcutlass_1_1half__t.html">half_t</a>, LayoutB, float, LayoutC, OpMultiplyAdd&gt; {</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;</div><div class="line"><a name="l00357"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01half__t_4f30ee91f7bb3844ff7579c68d078818.html#a76e594a71cad06065389402617dd714b">  357</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00360"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01half__t_4f30ee91f7bb3844ff7579c68d078818.html#aa1e0584011f1b74dc6a541693d6a2dc2">  360</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01half__t_4f30ee91f7bb3844ff7579c68d078818.html#aa1e0584011f1b74dc6a541693d6a2dc2">operator()</a>(</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;    Array&lt;float, 1&gt; &amp;d,</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;    Array&lt;half_t, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;    Array&lt;half_t, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;    Array&lt;float, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  ) {</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;    d[0] = float(a[0]) * float(b[0]) + c[0];</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;  }</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;};</div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;}</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;}</div><div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01int_00_00b2dff9ce8caad9aff5bc6a355539161_html_a147ecaa2af6851b26a17eb8f8d95f9d0"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01int_00_00b2dff9ce8caad9aff5bc6a355539161.html#a147ecaa2af6851b26a17eb8f8d95f9d0">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, int, LayoutA, int, LayoutB, int, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; int, 1 &gt; &amp;d, Array&lt; int, 1 &gt; const &amp;a, Array&lt; int, 1 &gt; const &amp;b, Array&lt; int, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:111</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_48b3a43bc03fff93a111ac01abe7e40d_html_a8f8180dcd03dad7b45d73f09e87c060b"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_48b3a43bc03fff93a111ac01abe7e40d.html#a8f8180dcd03dad7b45d73f09e87c060b">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, complex&lt; double &gt;, LayoutA, double, LayoutB, complex&lt; double &gt;, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; complex&lt; double &gt;, 1 &gt; &amp;d, Array&lt; complex&lt; double &gt;, 1 &gt; const &amp;a, Array&lt; double, 1 &gt; const &amp;b, Array&lt; complex&lt; double &gt;, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:297</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="complex_8h_html"><div class="ttname"><a href="complex_8h.html">complex.h</a></div></div>
<div class="ttc" id="structcutlass_1_1half__t_html"><div class="ttname"><a href="structcutlass_1_1half__t.html">cutlass::half_t</a></div><div class="ttdoc">IEEE half-precision floating-point type. </div><div class="ttdef"><b>Definition:</b> half.h:126</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_76f9d24016e1b4167b16f4d7628c9546_html_ae914107ff2d7b1524cc9f8c70237a8f9"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_76f9d24016e1b4167b16f4d7628c9546.html#ae914107ff2d7b1524cc9f8c70237a8f9">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, complex&lt; float &gt;, LayoutA, complex&lt; float &gt;, LayoutB, complex&lt; float &gt;, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; complex&lt; float &gt;, 1 &gt; &amp;d, Array&lt; complex&lt; float &gt;, 1 &gt; const &amp;a, Array&lt; complex&lt; float &gt;, 1 &gt; const &amp;b, Array&lt; complex&lt; float &gt;, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:147</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_30fa42e1ad201df010637cd22fc070a1_html_af37b41df0067e4c878baafc8d71574d9"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_30fa42e1ad201df010637cd22fc070a1.html#af37b41df0067e4c878baafc8d71574d9">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, complex&lt; double &gt;, LayoutA, complex&lt; double &gt;, LayoutB, complex&lt; double &gt;, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; complex&lt; double &gt;, 1 &gt; &amp;d, Array&lt; complex&lt; double &gt;, 1 &gt; const &amp;a, Array&lt; complex&lt; double &gt;, 1 &gt; const &amp;b, Array&lt; complex&lt; double &gt;, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:260</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01half__t_4f30ee91f7bb3844ff7579c68d078818_html_aa1e0584011f1b74dc6a541693d6a2dc2"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01half__t_4f30ee91f7bb3844ff7579c68d078818.html#aa1e0584011f1b74dc6a541693d6a2dc2">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, half_t, LayoutA, half_t, LayoutB, float, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; float, 1 &gt; &amp;d, Array&lt; half_t, 1 &gt; const &amp;a, Array&lt; half_t, 1 &gt; const &amp;b, Array&lt; float, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:360</div></div>
<div class="ttc" id="arch_2mma_8h_html"><div class="ttname"><a href="arch_2mma_8h.html">mma.h</a></div><div class="ttdoc">Templates exposing architecture support for multiply-add operations. </div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_f1c9d2ee842455cd0c5b71d56108d468_html_a3b69fa99a09158d9be274651f6b74980"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01complex_f1c9d2ee842455cd0c5b71d56108d468.html#a3b69fa99a09158d9be274651f6b74980">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, complex&lt; float &gt;, LayoutA, float, LayoutB, complex&lt; float &gt;, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; complex&lt; float &gt;, 1 &gt; &amp;d, Array&lt; complex&lt; float &gt;, 1 &gt; const &amp;a, Array&lt; float, 1 &gt; const &amp;b, Array&lt; complex&lt; float &gt;, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:186</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_004bb3fd76ca2af7b3210676fa9644d95b_html_a47e6d9cb8a24ae8246272985741f7f0d"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_004bb3fd76ca2af7b3210676fa9644d95b.html#a47e6d9cb8a24ae8246272985741f7f0d">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, float, LayoutA, float, LayoutB, float, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; float, 1 &gt; &amp;d, Array&lt; float, 1 &gt; const &amp;a, Array&lt; float, 1 &gt; const &amp;b, Array&lt; float, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:58</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_00e3e12e263df6506b8cf06c3f4d478b8e_html_a175c9e89f95837838e533687f4c4078d"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01float_00e3e12e263df6506b8cf06c3f4d478b8e.html#a175c9e89f95837838e533687f4c4078d">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, float, LayoutA, complex&lt; float &gt;, LayoutB, complex&lt; float &gt;, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; complex&lt; float &gt;, 1 &gt; &amp;d, Array&lt; float, 1 &gt; const &amp;a, Array&lt; complex&lt; float &gt;, 1 &gt; const &amp;b, Array&lt; complex&lt; float &gt;, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:223</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="classcutlass_1_1complex_html"><div class="ttname"><a href="classcutlass_1_1complex.html">cutlass::complex</a></div><div class="ttdef"><b>Definition:</b> complex.h:92</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a></div><div class="ttdoc">Matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> arch/mma.h:92</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_070b94670e040ed5855e5b42d5ca8a443_html_a672562083112f8463a9791113482c4e9"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_070b94670e040ed5855e5b42d5ca8a443.html#a672562083112f8463a9791113482c4e9">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, double, LayoutA, complex&lt; double &gt;, LayoutB, complex&lt; double &gt;, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; complex&lt; double &gt;, 1 &gt; &amp;d, Array&lt; double, 1 &gt; const &amp;a, Array&lt; complex&lt; double &gt;, 1 &gt; const &amp;b, Array&lt; complex&lt; double &gt;, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:332</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_0aa57e6a2e6b5da37d10688bf99419a23_html_acc4d8ede06a490f3fbfec8e3dd75b0b1"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01double_0aa57e6a2e6b5da37d10688bf99419a23.html#acc4d8ede06a490f3fbfec8e3dd75b0b1">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, double, LayoutA, double, LayoutB, double, LayoutC, OpMultiplyAdd &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; double, 1 &gt; &amp;d, Array&lt; double, 1 &gt; const &amp;a, Array&lt; double, 1 &gt; const &amp;b, Array&lt; double, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma_sm50.h:84</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
