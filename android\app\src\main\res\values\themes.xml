<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.MLCLLMDemo" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/light_gray</item>
        <item name="android:textColorPrimary">@color/black</item>
        <item name="android:textColorSecondary">@color/dark_gray</item>
    </style>
    
    <style name="Theme.MLCLLMDemo.Splash" parent="Theme.MLCLLMDemo">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
</resources>
