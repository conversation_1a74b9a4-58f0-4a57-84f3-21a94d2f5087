<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: aligned_buffer.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">aligned_buffer.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="aligned__buffer_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="namespacecutlass.html">   35</a></span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;  <span class="keyword">typename</span> T,</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;  <span class="keywordtype">int</span> N,</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;  <span class="keywordtype">int</span> Align = 16</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;&gt;</div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html">   45</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1AlignedBuffer.html">AlignedBuffer</a> {</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  </div><div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">   48</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> = uint8_t;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba">   51</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba">kCount</a> = N;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a0bab3f7468fe898b8abddba83f0b581a">   54</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a0bab3f7468fe898b8abddba83f0b581a">kAlign</a> = Align;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#ae7742a9814b15dafe3e05f98771a32e3">   57</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#ae7742a9814b15dafe3e05f98771a32e3">kBytes</a> = </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    (<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;T&gt;::value</a> * N + 7) / 8;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">alignas</span>(Align) <a class="code" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> storage[kBytes];</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="comment">// C++ standard members</span></div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">   71</a></span>&#160;  <span class="keyword">typedef</span> T <a class="code" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">value_type</a>;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">   72</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">size_t</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">size_type</a>;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a68527eff431854311f0221aa61e1c94d">   73</a></span>&#160;  <span class="keyword">typedef</span> ptrdiff_t <a class="code" href="structcutlass_1_1AlignedBuffer.html#a68527eff431854311f0221aa61e1c94d">difference_type</a>;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a1c0b77fef16a9f3d7007817a9fc32bf1">   74</a></span>&#160;  <span class="keyword">typedef</span> value_type *<a class="code" href="structcutlass_1_1AlignedBuffer.html#a1c0b77fef16a9f3d7007817a9fc32bf1">pointer</a>;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a9ea058b3d86ad689240836e2d89686c4">   75</a></span>&#160;  <span class="keyword">typedef</span> value_type <span class="keyword">const</span> * <a class="code" href="structcutlass_1_1AlignedBuffer.html#a9ea058b3d86ad689240836e2d89686c4">const_pointer</a>;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">   77</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">Array</a> = <a class="code" href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">Array&lt;T, N&gt;</a>;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#afa029189fb46528b5eb5f50060cbf28e">   78</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#afa029189fb46528b5eb5f50060cbf28e">reference</a> = <span class="keyword">typename</span> Array::reference;</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a878e461a9368a2e9639464caf78ac718">   79</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a878e461a9368a2e9639464caf78ac718">const_reference</a> = <span class="keyword">typename</span> Array::const_reference;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">   84</a></span>&#160;  pointer <a class="code" href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">data</a>() {</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>pointer<span class="keyword">&gt;</span>(storage); </div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  }</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#acbfc684b16c9c717df5712bcb729acf3">   89</a></span>&#160;  const_pointer <a class="code" href="structcutlass_1_1AlignedBuffer.html#acbfc684b16c9c717df5712bcb729acf3">data</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>pointer<span class="keyword">&gt;</span>(storage); </div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  }</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  </div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a3a87c3b8f14893d30f374bde2b88052c">   94</a></span>&#160;  <a class="code" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> * <a class="code" href="structcutlass_1_1AlignedBuffer.html#a3a87c3b8f14893d30f374bde2b88052c">raw_data</a>() {</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    <span class="keywordflow">return</span> storage;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  }</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#ae591f458d228ec8ac08caf8846dab67d">   99</a></span>&#160;  <a class="code" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> <span class="keyword">const</span> * <a class="code" href="structcutlass_1_1AlignedBuffer.html#ae591f458d228ec8ac08caf8846dab67d">raw_data</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="keywordflow">return</span> storage;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  }</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a37921fffef065c4da23ccc328db45f14">  105</a></span>&#160;  <a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <span class="keywordtype">bool</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a37921fffef065c4da23ccc328db45f14">empty</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    <span class="keywordflow">return</span> !<a class="code" href="structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba">kCount</a>;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  }</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a2b588b6018a1f36ce68e4e0f2eac2247">  110</a></span>&#160;  <a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> size_type <a class="code" href="structcutlass_1_1AlignedBuffer.html#a2b588b6018a1f36ce68e4e0f2eac2247">size</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba">kCount</a>;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  }</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="structcutlass_1_1AlignedBuffer.html#a673d7413585d44f0c025840c9b84b6b3">  115</a></span>&#160;  <a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> size_type <a class="code" href="structcutlass_1_1AlignedBuffer.html#a673d7413585d44f0c025840c9b84b6b3">max_size</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba">kCount</a>;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  }</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;};</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="ttc" id="structcutlass_1_1AlignedBuffer_html_aa8c8238434f9029b996796dbcf175282"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">cutlass::AlignedBuffer::value_type</a></div><div class="ttdeci">T value_type</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:71</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a9ea058b3d86ad689240836e2d89686c4"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a9ea058b3d86ad689240836e2d89686c4">cutlass::AlignedBuffer::const_pointer</a></div><div class="ttdeci">value_type const * const_pointer</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:75</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="platform_8h_html_a72f0657181cca64b44eb186b707eb380"><div class="ttname"><a href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a></div><div class="ttdeci">#define constexpr</div><div class="ttdef"><b>Definition:</b> platform.h:137</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_ae591f458d228ec8ac08caf8846dab67d"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#ae591f458d228ec8ac08caf8846dab67d">cutlass::AlignedBuffer::raw_data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Storage const * raw_data() const </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:99</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a878e461a9368a2e9639464caf78ac718"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a878e461a9368a2e9639464caf78ac718">cutlass::AlignedBuffer&lt; Element, cutlass::MatrixShape::kCount &gt;::const_reference</a></div><div class="ttdeci">typename Array::const_reference const_reference</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:79</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a673d7413585d44f0c025840c9b84b6b3"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a673d7413585d44f0c025840c9b84b6b3">cutlass::AlignedBuffer::max_size</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr size_type max_size() const </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:115</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a49b7cb4bf1ff845619f927bf1d495e61"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">cutlass::AlignedBuffer&lt; Element, cutlass::MatrixShape::kCount &gt;::Storage</a></div><div class="ttdeci">uint8_t Storage</div><div class="ttdoc">Internal storage type. </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:48</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html">cutlass::sizeof_bits</a></div><div class="ttdoc">Defines the size of an element in bits. </div><div class="ttdef"><b>Definition:</b> numeric_types.h:42</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_ae7742a9814b15dafe3e05f98771a32e3"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#ae7742a9814b15dafe3e05f98771a32e3">cutlass::AlignedBuffer::kBytes</a></div><div class="ttdeci">static int const kBytes</div><div class="ttdoc">Number of storage elements. </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:57</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a></div><div class="ttdoc">Modifies semantics of cutlass::Array&lt;&gt; to provide guaranteed alignment. </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:45</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a8ed8b9d3469621fc82d0041846c59da2"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">cutlass::AlignedBuffer::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE pointer data()</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:84</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a16e2e6aa35c03e4a65b062123d9490ba"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba">cutlass::AlignedBuffer::kCount</a></div><div class="ttdeci">static int const kCount</div><div class="ttdoc">Number of logical elements held in buffer. </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:51</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a0bab3f7468fe898b8abddba83f0b581a"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a0bab3f7468fe898b8abddba83f0b581a">cutlass::AlignedBuffer::kAlign</a></div><div class="ttdeci">static int const kAlign</div><div class="ttdoc">Alignment requirement in bytes. </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:54</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a3a87c3b8f14893d30f374bde2b88052c"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a3a87c3b8f14893d30f374bde2b88052c">cutlass::AlignedBuffer::raw_data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Storage * raw_data()</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:94</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_afa029189fb46528b5eb5f50060cbf28e"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#afa029189fb46528b5eb5f50060cbf28e">cutlass::AlignedBuffer&lt; Element, cutlass::MatrixShape::kCount &gt;::reference</a></div><div class="ttdeci">typename Array::reference reference</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:78</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a6083193cadb42a445442da9be737d934"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">cutlass::AlignedBuffer&lt; Element, cutlass::MatrixShape::kCount &gt;::Array</a></div><div class="ttdeci">Array&lt; Element, N &gt; Array</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:77</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_aa1cae39c2587cffc0957ca668c95989f"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">cutlass::AlignedBuffer::size_type</a></div><div class="ttdeci">size_t size_type</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:72</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a68527eff431854311f0221aa61e1c94d"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a68527eff431854311f0221aa61e1c94d">cutlass::AlignedBuffer::difference_type</a></div><div class="ttdeci">ptrdiff_t difference_type</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:73</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a1c0b77fef16a9f3d7007817a9fc32bf1"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a1c0b77fef16a9f3d7007817a9fc32bf1">cutlass::AlignedBuffer::pointer</a></div><div class="ttdeci">value_type * pointer</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:74</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a37921fffef065c4da23ccc328db45f14"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a37921fffef065c4da23ccc328db45f14">cutlass::AlignedBuffer::empty</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr bool empty() const </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:105</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a2b588b6018a1f36ce68e4e0f2eac2247"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a2b588b6018a1f36ce68e4e0f2eac2247">cutlass::AlignedBuffer::size</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr size_type size() const </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:110</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_acbfc684b16c9c717df5712bcb729acf3"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#acbfc684b16c9c717df5712bcb729acf3">cutlass::AlignedBuffer::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE const_pointer data() const </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:89</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
