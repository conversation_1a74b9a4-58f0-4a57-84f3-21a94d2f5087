"""This file specifies how MLC's Gemma parameters are quantized using group quantization
or other formats."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import GroupQuantize, NoQuantize

from .gemma3_model import Gemma<PERSON><PERSON>onfig, Gemma3ForCausalLM


def group_quant(
    model_config: Gemma3Config,
    quantization: GroupQuantize,
) -> <PERSON><PERSON>[nn.<PERSON><PERSON><PERSON>, QuantizeMapping]:
    """Quantize a Gemma-architecture model using group quantization."""
    model: nn.Module = Gemma3ForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    quantization.tensor_parallel_shards = model_config.tensor_parallel_shards
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def no_quant(
    model_config: Gemma3Config,
    quantization: NoQuantize,
) -> <PERSON><PERSON>[nn.<PERSON><PERSON><PERSON>, QuantizeMapping]:
    """Quantize a Llama2 model without quantization."""
    model: nn.Module = Gemma3ForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
