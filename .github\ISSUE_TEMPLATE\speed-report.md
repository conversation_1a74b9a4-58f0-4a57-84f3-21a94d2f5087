---
name: " 🏎️  Speed Report"
about: Submit a speed report of an model running in MLC-LLM
title: '[Speed] '
labels: ['performance']
assignees: ''

---

# 🏎️  Speed Report

<!-- Please search if there are existing issues discuss the speed of the model you are using, if there are, we encourage you reply in the existed issue instead of creating a new one. -->

- The model code: <!-- e.g. vicuna-7b-1.1 -->


- The model configuration (e.g. quantization mode, running data type, etc.):
- Device (e.g. MacBook Pro M2, PC+RTX 3080):
- OS (if applicable):
- Encode speed (Token/s):
- Decode speed (Token/s):
- Memory usage (if applicable):

<!-- Note that the measured speed might reflect peak performance if the prompt/chat history is short. -->
