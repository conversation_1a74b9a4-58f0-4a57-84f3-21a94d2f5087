# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_LOGICAL_HPP
# define MSGPACK_PREPROCESSOR_LOGICAL_HPP
#
# include <msgpack/preprocessor/logical/and.hpp>
# include <msgpack/preprocessor/logical/bitand.hpp>
# include <msgpack/preprocessor/logical/bitnor.hpp>
# include <msgpack/preprocessor/logical/bitor.hpp>
# include <msgpack/preprocessor/logical/bitxor.hpp>
# include <msgpack/preprocessor/logical/bool.hpp>
# include <msgpack/preprocessor/logical/compl.hpp>
# include <msgpack/preprocessor/logical/nor.hpp>
# include <msgpack/preprocessor/logical/not.hpp>
# include <msgpack/preprocessor/logical/or.hpp>
# include <msgpack/preprocessor/logical/xor.hpp>
#
# endif
