//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2015-2016 MIZUKI Hirata
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef MSGPACK_ITERATOR_HPP
#define MSGPACK_ITERATOR_HPP

#include "msgpack/iterator_decl.hpp"

#include "msgpack/v1/iterator.hpp"

#endif // MSGPACK_ITERATOR_HPP
