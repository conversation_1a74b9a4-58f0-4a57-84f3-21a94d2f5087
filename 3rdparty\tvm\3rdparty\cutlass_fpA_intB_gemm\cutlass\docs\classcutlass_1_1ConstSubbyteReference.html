<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::ConstSubbyteReference&lt; Element_, Storage_ &gt; Class Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classcutlass_1_1ConstSubbyteReference-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::ConstSubbyteReference&lt; Element_, Storage_ &gt; Class Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="subbyte__reference_8h_source.html">subbyte_reference.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a3f143b914d4d7a1dbe724d64d30bf60c"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> = Element_</td></tr>
<tr class="separator:a3f143b914d4d7a1dbe724d64d30bf60c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6eaafa32796df610701bdd3c9e5aa45"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> = Storage_</td></tr>
<tr class="separator:af6eaafa32796df610701bdd3c9e5aa45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe79398d7625d244f130867a9a25dddc"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a> = <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> const *</td></tr>
<tr class="separator:afe79398d7625d244f130867a9a25dddc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aa00016fe6dafa323e9875be4287fbfe5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#aa00016fe6dafa323e9875be4287fbfe5">ConstSubbyteReference</a> ()</td></tr>
<tr class="separator:aa00016fe6dafa323e9875be4287fbfe5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a158ae5a484751f274c083807b4a37868"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a158ae5a484751f274c083807b4a37868">ConstSubbyteReference</a> (<a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> const *ptr, int64_t offset)</td></tr>
<tr class="memdesc:a158ae5a484751f274c083807b4a37868"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a158ae5a484751f274c083807b4a37868">More...</a><br /></td></tr>
<tr class="separator:a158ae5a484751f274c083807b4a37868"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfefff5e63632fcdc4f59e21dccea16d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#adfefff5e63632fcdc4f59e21dccea16d">ConstSubbyteReference</a> (<a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> *ptr=<a class="el" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>)</td></tr>
<tr class="memdesc:adfefff5e63632fcdc4f59e21dccea16d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#adfefff5e63632fcdc4f59e21dccea16d">More...</a><br /></td></tr>
<tr class="separator:adfefff5e63632fcdc4f59e21dccea16d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa76e4dd207d7405868ebba3f2e121c1e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#aa76e4dd207d7405868ebba3f2e121c1e">storage_pointer</a> () const </td></tr>
<tr class="memdesc:aa76e4dd207d7405868ebba3f2e121c1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets storage pointer.  <a href="#aa76e4dd207d7405868ebba3f2e121c1e">More...</a><br /></td></tr>
<tr class="separator:aa76e4dd207d7405868ebba3f2e121c1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ff8cfd6a308811ae197b0eb704bdc24"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a7ff8cfd6a308811ae197b0eb704bdc24">element_offset</a> () const </td></tr>
<tr class="memdesc:a7ff8cfd6a308811ae197b0eb704bdc24"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets element offset within storage vector.  <a href="#a7ff8cfd6a308811ae197b0eb704bdc24">More...</a><br /></td></tr>
<tr class="separator:a7ff8cfd6a308811ae197b0eb704bdc24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5af3bf12950795fdc96c1e65db31776"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#ae5af3bf12950795fdc96c1e65db31776">get</a> () const </td></tr>
<tr class="memdesc:ae5af3bf12950795fdc96c1e65db31776"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpacks an element from memory.  <a href="#ae5af3bf12950795fdc96c1e65db31776">More...</a><br /></td></tr>
<tr class="separator:ae5af3bf12950795fdc96c1e65db31776"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a908a341f883db06f529536fae196a7c9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a908a341f883db06f529536fae196a7c9">operator Element</a> () const </td></tr>
<tr class="memdesc:a908a341f883db06f529536fae196a7c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpacks an element from memory.  <a href="#a908a341f883db06f529536fae196a7c9">More...</a><br /></td></tr>
<tr class="separator:a908a341f883db06f529536fae196a7c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7e5cf02325b590fffa2fc5bfcb9da09"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#ad7e5cf02325b590fffa2fc5bfcb9da09">operator+=</a> (int offset)</td></tr>
<tr class="memdesc:ad7e5cf02325b590fffa2fc5bfcb9da09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds an offset in units of elements to the reference.  <a href="#ad7e5cf02325b590fffa2fc5bfcb9da09">More...</a><br /></td></tr>
<tr class="separator:ad7e5cf02325b590fffa2fc5bfcb9da09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a712b16abc1305ae5fb1c57bd25f89a6b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a712b16abc1305ae5fb1c57bd25f89a6b">operator+=</a> (long long offset)</td></tr>
<tr class="memdesc:a712b16abc1305ae5fb1c57bd25f89a6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds an offset in units of elements to the reference.  <a href="#a712b16abc1305ae5fb1c57bd25f89a6b">More...</a><br /></td></tr>
<tr class="separator:a712b16abc1305ae5fb1c57bd25f89a6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cb617780fc42c735fa0f997926936aa"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a2cb617780fc42c735fa0f997926936aa">operator-=</a> (int offset)</td></tr>
<tr class="memdesc:a2cb617780fc42c735fa0f997926936aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds an offset in units of elements to the reference.  <a href="#a2cb617780fc42c735fa0f997926936aa">More...</a><br /></td></tr>
<tr class="separator:a2cb617780fc42c735fa0f997926936aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc062e38c9eede21a770f22ac957dec0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#abc062e38c9eede21a770f22ac957dec0">operator-=</a> (long long offset)</td></tr>
<tr class="memdesc:abc062e38c9eede21a770f22ac957dec0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds an offset in units of elements to the reference.  <a href="#abc062e38c9eede21a770f22ac957dec0">More...</a><br /></td></tr>
<tr class="separator:abc062e38c9eede21a770f22ac957dec0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0afaee4126a794f9db58ed4bd079b792"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a0afaee4126a794f9db58ed4bd079b792">operator+</a> (int offset) const </td></tr>
<tr class="memdesc:a0afaee4126a794f9db58ed4bd079b792"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a reference to an element with a given offset from the current reference.  <a href="#a0afaee4126a794f9db58ed4bd079b792">More...</a><br /></td></tr>
<tr class="separator:a0afaee4126a794f9db58ed4bd079b792"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a035824f267fecb8cfc0848904cc4ab"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3a035824f267fecb8cfc0848904cc4ab">operator+</a> (long long offset) const </td></tr>
<tr class="memdesc:a3a035824f267fecb8cfc0848904cc4ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a reference to an element with a given offset from the current reference.  <a href="#a3a035824f267fecb8cfc0848904cc4ab">More...</a><br /></td></tr>
<tr class="separator:a3a035824f267fecb8cfc0848904cc4ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb37aceee94bbef99217d011b28d89f8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#aeb37aceee94bbef99217d011b28d89f8">operator-</a> (int offset) const </td></tr>
<tr class="memdesc:aeb37aceee94bbef99217d011b28d89f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a reference to an element with a given offset from the current reference.  <a href="#aeb37aceee94bbef99217d011b28d89f8">More...</a><br /></td></tr>
<tr class="separator:aeb37aceee94bbef99217d011b28d89f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a621c30a0ac6469084dc16c930e0d0213"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a621c30a0ac6469084dc16c930e0d0213">operator-=</a> (long long offset) const </td></tr>
<tr class="memdesc:a621c30a0ac6469084dc16c930e0d0213"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a reference to an element with a given offset from the current reference.  <a href="#a621c30a0ac6469084dc16c930e0d0213">More...</a><br /></td></tr>
<tr class="separator:a621c30a0ac6469084dc16c930e0d0213"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a161e7783a83a271735f753f21348314c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> ptrdiff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a161e7783a83a271735f753f21348314c">operator-</a> (<a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> ref) const </td></tr>
<tr class="memdesc:a161e7783a83a271735f753f21348314c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes the difference in elements between references.  <a href="#a161e7783a83a271735f753f21348314c">More...</a><br /></td></tr>
<tr class="separator:a161e7783a83a271735f753f21348314c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe87d46c924861d6a9e2b06d2d4d69cc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#abe87d46c924861d6a9e2b06d2d4d69cc">operator int</a> () const </td></tr>
<tr class="memdesc:abe87d46c924861d6a9e2b06d2d4d69cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Explicit cast to int.  <a href="#abe87d46c924861d6a9e2b06d2d4d69cc">More...</a><br /></td></tr>
<tr class="separator:abe87d46c924861d6a9e2b06d2d4d69cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad47d3da6dca44d5a8c821f63ca37ded7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#ad47d3da6dca44d5a8c821f63ca37ded7">operator int64_t</a> () const </td></tr>
<tr class="memdesc:ad47d3da6dca44d5a8c821f63ca37ded7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Explicit cast to signed 64-bit integer.  <a href="#ad47d3da6dca44d5a8c821f63ca37ded7">More...</a><br /></td></tr>
<tr class="separator:ad47d3da6dca44d5a8c821f63ca37ded7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd60b998269ea5f771d2f51ed736d3c1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#abd60b998269ea5f771d2f51ed736d3c1">operator uint64_t</a> () const </td></tr>
<tr class="memdesc:abd60b998269ea5f771d2f51ed736d3c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Explicit cast to unsigned 64-bit integer.  <a href="#abd60b998269ea5f771d2f51ed736d3c1">More...</a><br /></td></tr>
<tr class="separator:abd60b998269ea5f771d2f51ed736d3c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae794d96fa19581472489bece95fcf344"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#ae794d96fa19581472489bece95fcf344">operator float</a> () const </td></tr>
<tr class="memdesc:ae794d96fa19581472489bece95fcf344"><td class="mdescLeft">&#160;</td><td class="mdescRight">Explicit cast to float.  <a href="#ae794d96fa19581472489bece95fcf344">More...</a><br /></td></tr>
<tr class="separator:ae794d96fa19581472489bece95fcf344"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e735e155826e5c2a0e33b0a456cc5ab"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a5e735e155826e5c2a0e33b0a456cc5ab">operator double</a> () const </td></tr>
<tr class="memdesc:a5e735e155826e5c2a0e33b0a456cc5ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Explicit cast to double.  <a href="#a5e735e155826e5c2a0e33b0a456cc5ab">More...</a><br /></td></tr>
<tr class="separator:a5e735e155826e5c2a0e33b0a456cc5ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename Element_, typename Storage_ = uint8_t&gt;<br />
class cutlass::ConstSubbyteReference&lt; Element_, Storage_ &gt;</h3>

<p>This class provides a mechanism for packing and unpacking elements smaller than one byte. It assumes these sub-byte elements are packed in a traditional C++ numeric type.</p>
<p>The intended application is to provide a mechanism to indirectly reference elements in memory or Array&lt;&gt; objects whose addresses cannot otherwise be taken since they are smaller than one byte.</p>
<p>Supports basic pointer arithmetic:</p>
<p>Example:</p>
<p>int4b_t *ptr = ...;</p>
<p>SubbyteReference&lt;int4b_t&gt; ref = ptr; ref += 15;</p>
<p>int4b_t x = ref; // load an int4b_t ref = x + 2_s4; // perform arithmetic on int4b_t and then store </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a3f143b914d4d7a1dbe724d64d30bf60c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::<a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> =  Element_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af6eaafa32796df610701bdd3c9e5aa45"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::<a class="el" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> =  Storage_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afe79398d7625d244f130867a9a25dddc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::<a class="el" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a> =  <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#af6eaafa32796df610701bdd3c9e5aa45">Storage</a> const *</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="aa00016fe6dafa323e9875be4287fbfe5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::<a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a158ae5a484751f274c083807b4a37868"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::<a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> const *&#160;</td>
          <td class="paramname"><em>ptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int64_t&#160;</td>
          <td class="paramname"><em>offset</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">offset</td><td>pointer to memory logical offset in units of Element </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="adfefff5e63632fcdc4f59e21dccea16d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::<a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> *&#160;</td>
          <td class="paramname"><em>ptr</em> = <code><a class="el" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a></code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a7ff8cfd6a308811ae197b0eb704bdc24"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::element_offset </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae5af3bf12950795fdc96c1e65db31776"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::get </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5e735e155826e5c2a0e33b0a456cc5ab"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator double </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">explicit</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a908a341f883db06f529536fae196a7c9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#a3f143b914d4d7a1dbe724d64d30bf60c">Element</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae794d96fa19581472489bece95fcf344"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator float </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">explicit</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abe87d46c924861d6a9e2b06d2d4d69cc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator int </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">explicit</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad47d3da6dca44d5a8c821f63ca37ded7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator int64_t </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">explicit</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abd60b998269ea5f771d2f51ed736d3c1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator uint64_t </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">explicit</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0afaee4126a794f9db58ed4bd079b792"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator+ </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3a035824f267fecb8cfc0848904cc4ab"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator+ </td>
          <td>(</td>
          <td class="paramtype">long long&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad7e5cf02325b590fffa2fc5bfcb9da09"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&amp; <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator+= </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a712b16abc1305ae5fb1c57bd25f89a6b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&amp; <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator+= </td>
          <td>(</td>
          <td class="paramtype">long long&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aeb37aceee94bbef99217d011b28d89f8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator- </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a161e7783a83a271735f753f21348314c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> ptrdiff_t <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator- </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;&#160;</td>
          <td class="paramname"><em>ref</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2cb617780fc42c735fa0f997926936aa"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&amp; <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator-= </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abc062e38c9eede21a770f22ac957dec0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a>&amp; <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator-= </td>
          <td>(</td>
          <td class="paramtype">long long&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a621c30a0ac6469084dc16c930e0d0213"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">ConstSubbyteReference</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::operator-= </td>
          <td>(</td>
          <td class="paramtype">long long&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa76e4dd207d7405868ebba3f2e121c1e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_, typename Storage_ = uint8_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#afe79398d7625d244f130867a9a25dddc">StoragePointer</a> <a class="el" href="classcutlass_1_1ConstSubbyteReference.html">cutlass::ConstSubbyteReference</a>&lt; Element_, Storage_ &gt;::storage_pointer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="subbyte__reference_8h_source.html">subbyte_reference.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
