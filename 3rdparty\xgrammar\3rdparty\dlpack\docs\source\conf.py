# Configuration file for the Sphinx documentation builder.
#
# This file only contains a selection of the most common options. For a full
# list see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Path setup --------------------------------------------------------------

# If extensions (or modules to document with autodoc) are in another directory,
# add these directories to sys.path here. If the directory is relative to the
# documentation root, use os.path.abspath to make it absolute, like shown here.
#
# import os
# import sys
# sys.path.insert(0, os.path.abspath('.'))


# -- Project information -----------------------------------------------------

project = 'DLPack'
copyright = '2022, DLPack contributors'
author = 'DLPack contributors'

# The full version, including alpha/beta/rc tags
release = '0.6.0'


# -- General configuration ---------------------------------------------------

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with Sphinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = ['sphinx.ext.intersphinx', 'breathe']

# Add any paths that contain templates here, relative to this directory.
# templates_path = ['_templates']

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
# This pattern also affects html_static_path and html_extra_path.
exclude_patterns = []

intersphinx_mapping = {
    "python": ("https://docs.python.org/3/", None),
    "array_api": ("https://data-apis.org/array-api/latest", None),
}

html_use_index = True
html_domain_indices = True

html_theme_options = {
    "github_url": "https://github.com/dmlc/dlpack",
    "use_edit_page_button": True,
    "show_toc_level": 1,
}

html_context = {
    "github_user": "dmlc",
    "github_repo": "dlpack",
    "github_version": "main",
    "doc_path": "docs/source",
}


# -- Breathe configuration ---------------------------------------------------

# Tell breathe what to call the project. This is used to link XML files
# generated by Doxygen by passing a command line option:
# -Dbreathe_projects.<breathe_default_project>=</path/to/xml/index/directory>
breathe_default_project = "dlpack"

breathe_domain_by_extension = {
    "h" : "c",
}


# -- Options for HTML output -------------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
#
html_theme = 'pydata_sphinx_theme' # 'alabaster'

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".
html_static_path = ['_static']
