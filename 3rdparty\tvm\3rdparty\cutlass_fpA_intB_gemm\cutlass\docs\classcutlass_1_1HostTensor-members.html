<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="classcutlass_1_1HostTensor.html">HostTensor</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">cutlass::HostTensor&lt; Element_, Layout_ &gt; Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ab1253b57fd95f16b2dec9e36e84051d4">at</a>(TensorCoord const &amp;coord)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ae261f1b7ef78cc52dcb77f1a2fd05b69">at</a>(TensorCoord const &amp;coord) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#aa6ea111e8fcba15c07f0cf679e1eec7f">capacity</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a4c6d967596e266ceab3d36f6c8d05152">ConstReference</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ae76a55dd7f4d827f042f9c9018567271">ConstTensorRef</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ac4445444c8f092dd1be47f1530affbaf">ConstTensorView</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a5686ea068c8f3e820ccff015e95bc474">copy_in_device_to_device</a>(Element const *ptr_device, LongIndex count=-1)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a2b4858efb0356a6fc01bb9f55f0ad3b2">copy_in_device_to_host</a>(Element const *ptr_device, LongIndex count=-1)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ad385330c69ecd7bd0b6c3660815253fa">copy_in_host_to_device</a>(Element const *ptr_host, LongIndex count=-1)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ae11229ea69460ca174c5e6f9815eb97f">copy_in_host_to_host</a>(Element const *ptr_host, LongIndex count=-1)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ab7179440d39b0445113b30b7a460a1ec">copy_out_device_to_device</a>(Element *ptr_device, LongIndex count=-1) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ab3051d2842b3aa3815e2ea5f53abfc2a">copy_out_device_to_host</a>(Element *ptr_host, LongIndex count=-1) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#abeefdb8bccb2d8d751fdb22fa7e8ef0c">copy_out_host_to_device</a>(Element *ptr_device, LongIndex count=-1) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a40da221db96cfda76ba5623856c66bf1">copy_out_host_to_host</a>(Element *ptr_host, LongIndex count=-1) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a73430856f79bedb64f9cf6b2044f38e3">device_backed</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#aca2b28a16fc92d29102d00f154a1dfd1">device_data</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#abecb0dce978ea2c542d7d87a35f7997a">device_data</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a81043b0539c8d18c40957411dd149e28">device_data_ptr_offset</a>(LongIndex ptr_element_offset)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a55a73e5ff7c7404c0bdee5f2b578b876">device_ref</a>(LongIndex ptr_element_offset=0)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a4bf91f711ef17492809c09d53364cb35">device_ref</a>(LongIndex ptr_element_offset=0) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a075b666917a43c9bc168bfff6db27203">device_view</a>(LongIndex ptr_element_offset=0)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a6d1c49888cf678d3d5469eba4e911337">device_view</a>(LongIndex ptr_element_offset=0) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a8a947176a4ddab5b784c98a0f1896892">extent</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a7d34307e09d4ea09d68abbf31fe33788">host_data</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#adafe2bee53260d47bc60479c50953f57">host_data</a>(LongIndex idx)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a3dd62ca461666b3ac7e690e2befecaae">host_data</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a7741d51bf7d241d4821e52e47b704c69">host_data</a>(LongIndex idx) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a252ce9281a40863f32be25d3b40d6373">host_data_ptr_offset</a>(LongIndex ptr_element_offset)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ac921be812016052c690d2dc808d415f1">host_ref</a>(LongIndex ptr_element_offset=0)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a3c5b60678dfa105b10c87dfaab4cf395">host_ref</a>(LongIndex ptr_element_offset=0) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a75ce60d02c9d8fdcbcaceecc6b3ec7fa">host_view</a>(LongIndex ptr_element_offset=0)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a232e640ebb4b8cf21b44653d7800c5a7">host_view</a>(LongIndex ptr_element_offset=0) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#af50ce8b091c106ac88b75e15ab028868">HostTensor</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#abf381d496b50aad3df4bb72e3d891bac">HostTensor</a>(TensorCoord const &amp;extent, bool device_backed=true)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a9d71a824b0e1a4f1e8a88abecdff10b0">HostTensor</a>(TensorCoord const &amp;extent, Layout const &amp;layout, bool device_backed=true)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a33679b84f59829c70457b9ff438101bf">Index</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ad8d8df8fbb877de1d29978288405bf5f">kElementsPerStoredItem</a></td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#aa3458af41bf057e8bcfd895388a52659">kRank</a></td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a9b78ff4fb7f317809b8cb8f70c557bea">layout</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a61fe837629d7663cbb93ea8cfc177077">layout</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ac3e08629bab5304f999fb5115453a714">offset</a>(TensorCoord const &amp;coord) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39">Reference</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a6405f217fc2a52f8e23180c9f8160899">reserve</a>(size_t count, bool device_backed_=true)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a898fc427538275635a55e7633cd89140">reset</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a4f23ad5953f9cd5dff8e97d9edbc0cf0">reset</a>(TensorCoord const &amp;extent, Layout const &amp;layout, bool device_backed_=true)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ab91b456881d5f9c0f52a4bc89d448d55">reset</a>(TensorCoord const &amp;extent, bool device_backed_=true)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ac61cee5b25585439f4ccc7d7249ee853">resize</a>(TensorCoord const &amp;extent, Layout const &amp;layout, bool device_backed_=true)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a784d0b84de512c1bff131710226e2338">resize</a>(TensorCoord const &amp;extent, bool device_backed_=true)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#aacf36383bd4608f6d30c561ce4851b83">size</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a23976f8c123de032cf4a2632a894fcf2">stride</a>() const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a241cd2fe7dfe62b3e68ef75334bd2fda">stride</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a4a16e522cac85735cfcb056dc928de18">stride</a>(int dim) const </td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a439278acead2d26cb453d2949019fb68">stride</a>(int dim)</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a8775e63b9e6f320fb8611f5e8f7fc3b9">Stride</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#af339765c90429fe99b98a0da6a627421">sync_device</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#aea45851485df33ee2afb2a30bb82ebfc">sync_host</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a> typedef</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html#a068d76dabce39c48b617ee7fe8d7edb8">~HostTensor</a>()</td><td class="entry"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
