"""This file specifies how MLC's InternLM2 parameters are quantized using group quantization
or other formats."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import FTQuantize, GroupQuantize, NoQuantize

from .internlm2_model import InternLM2Config, InternLM2ForCausalLM


def group_quant(
    model_config: InternLM2Config,
    quantization: GroupQuantize,
) -> <PERSON><PERSON>[nn.Module, QuantizeMapping]:
    """Quantize a InternLM2-architecture model using group quantization."""
    model: nn.Module = InternLM2ForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    quantization.tensor_parallel_shards = model_config.tensor_parallel_shards
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def ft_quant(
    model_config: InternLM2Config,
    quantization: FTQuantize,
) -> <PERSON><PERSON>[nn.Modu<PERSON>, QuantizeMapping]:
    """Quantize a InternLM2 model using FasterTransformer quantization."""
    model: nn.Module = InternLM2ForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def no_quant(
    model_config: InternLM2Config,
    quantization: NoQuantize,
) -> Tuple[nn.Module, QuantizeMapping]:
    """Quantize a InternLM2 model without quantization."""
    model: nn.Module = InternLM2ForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
