//
// MessagePack for C++ deserializing routine
//
// Copyright (C) 2018 KOND<PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_CREATE_OBJECT_VISITOR_HPP
#define MSGPACK_CREATE_OBJECT_VISITOR_HPP

#include "msgpack/create_object_visitor_decl.hpp"

#include "msgpack/v2/create_object_visitor.hpp"

#endif // MSGPACK_CREATE_OBJECT_VISITOR_HPP
