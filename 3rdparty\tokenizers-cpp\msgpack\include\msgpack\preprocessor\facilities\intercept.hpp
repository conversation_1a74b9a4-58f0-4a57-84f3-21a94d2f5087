# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_FACILITIES_INTERCEPT_HPP
# define MSGPACK_PREPROCESSOR_FACILITIES_INTERCEPT_HPP
#
# /* MSGPACK_PP_INTERCEPT */
#
# define MSGPACK_PP_INTERCEPT MSGPACK_PP_INTERCEPT_
#
# define MSGPACK_PP_INTERCEPT_0
# define MSGPACK_PP_INTERCEPT_1
# define MSGPACK_PP_INTERCEPT_2
# define MSGPACK_PP_INTERCEPT_3
# define MSGPACK_PP_INTERCEPT_4
# define MSGPACK_PP_INTERCEPT_5
# define MSGPACK_PP_INTERCEPT_6
# define MSGPACK_PP_INTERCEPT_7
# define MSGPACK_PP_INTERCEPT_8
# define MSGPACK_PP_INTERCEPT_9
# define MSGPACK_PP_INTERCEPT_10
# define MSGPACK_PP_INTERCEPT_11
# define MSGPACK_PP_INTERCEPT_12
# define MSGPACK_PP_INTERCEPT_13
# define MSGPACK_PP_INTERCEPT_14
# define MSGPACK_PP_INTERCEPT_15
# define MSGPACK_PP_INTERCEPT_16
# define MSGPACK_PP_INTERCEPT_17
# define MSGPACK_PP_INTERCEPT_18
# define MSGPACK_PP_INTERCEPT_19
# define MSGPACK_PP_INTERCEPT_20
# define MSGPACK_PP_INTERCEPT_21
# define MSGPACK_PP_INTERCEPT_22
# define MSGPACK_PP_INTERCEPT_23
# define MSGPACK_PP_INTERCEPT_24
# define MSGPACK_PP_INTERCEPT_25
# define MSGPACK_PP_INTERCEPT_26
# define MSGPACK_PP_INTERCEPT_27
# define MSGPACK_PP_INTERCEPT_28
# define MSGPACK_PP_INTERCEPT_29
# define MSGPACK_PP_INTERCEPT_30
# define MSGPACK_PP_INTERCEPT_31
# define MSGPACK_PP_INTERCEPT_32
# define MSGPACK_PP_INTERCEPT_33
# define MSGPACK_PP_INTERCEPT_34
# define MSGPACK_PP_INTERCEPT_35
# define MSGPACK_PP_INTERCEPT_36
# define MSGPACK_PP_INTERCEPT_37
# define MSGPACK_PP_INTERCEPT_38
# define MSGPACK_PP_INTERCEPT_39
# define MSGPACK_PP_INTERCEPT_40
# define MSGPACK_PP_INTERCEPT_41
# define MSGPACK_PP_INTERCEPT_42
# define MSGPACK_PP_INTERCEPT_43
# define MSGPACK_PP_INTERCEPT_44
# define MSGPACK_PP_INTERCEPT_45
# define MSGPACK_PP_INTERCEPT_46
# define MSGPACK_PP_INTERCEPT_47
# define MSGPACK_PP_INTERCEPT_48
# define MSGPACK_PP_INTERCEPT_49
# define MSGPACK_PP_INTERCEPT_50
# define MSGPACK_PP_INTERCEPT_51
# define MSGPACK_PP_INTERCEPT_52
# define MSGPACK_PP_INTERCEPT_53
# define MSGPACK_PP_INTERCEPT_54
# define MSGPACK_PP_INTERCEPT_55
# define MSGPACK_PP_INTERCEPT_56
# define MSGPACK_PP_INTERCEPT_57
# define MSGPACK_PP_INTERCEPT_58
# define MSGPACK_PP_INTERCEPT_59
# define MSGPACK_PP_INTERCEPT_60
# define MSGPACK_PP_INTERCEPT_61
# define MSGPACK_PP_INTERCEPT_62
# define MSGPACK_PP_INTERCEPT_63
# define MSGPACK_PP_INTERCEPT_64
# define MSGPACK_PP_INTERCEPT_65
# define MSGPACK_PP_INTERCEPT_66
# define MSGPACK_PP_INTERCEPT_67
# define MSGPACK_PP_INTERCEPT_68
# define MSGPACK_PP_INTERCEPT_69
# define MSGPACK_PP_INTERCEPT_70
# define MSGPACK_PP_INTERCEPT_71
# define MSGPACK_PP_INTERCEPT_72
# define MSGPACK_PP_INTERCEPT_73
# define MSGPACK_PP_INTERCEPT_74
# define MSGPACK_PP_INTERCEPT_75
# define MSGPACK_PP_INTERCEPT_76
# define MSGPACK_PP_INTERCEPT_77
# define MSGPACK_PP_INTERCEPT_78
# define MSGPACK_PP_INTERCEPT_79
# define MSGPACK_PP_INTERCEPT_80
# define MSGPACK_PP_INTERCEPT_81
# define MSGPACK_PP_INTERCEPT_82
# define MSGPACK_PP_INTERCEPT_83
# define MSGPACK_PP_INTERCEPT_84
# define MSGPACK_PP_INTERCEPT_85
# define MSGPACK_PP_INTERCEPT_86
# define MSGPACK_PP_INTERCEPT_87
# define MSGPACK_PP_INTERCEPT_88
# define MSGPACK_PP_INTERCEPT_89
# define MSGPACK_PP_INTERCEPT_90
# define MSGPACK_PP_INTERCEPT_91
# define MSGPACK_PP_INTERCEPT_92
# define MSGPACK_PP_INTERCEPT_93
# define MSGPACK_PP_INTERCEPT_94
# define MSGPACK_PP_INTERCEPT_95
# define MSGPACK_PP_INTERCEPT_96
# define MSGPACK_PP_INTERCEPT_97
# define MSGPACK_PP_INTERCEPT_98
# define MSGPACK_PP_INTERCEPT_99
# define MSGPACK_PP_INTERCEPT_100
# define MSGPACK_PP_INTERCEPT_101
# define MSGPACK_PP_INTERCEPT_102
# define MSGPACK_PP_INTERCEPT_103
# define MSGPACK_PP_INTERCEPT_104
# define MSGPACK_PP_INTERCEPT_105
# define MSGPACK_PP_INTERCEPT_106
# define MSGPACK_PP_INTERCEPT_107
# define MSGPACK_PP_INTERCEPT_108
# define MSGPACK_PP_INTERCEPT_109
# define MSGPACK_PP_INTERCEPT_110
# define MSGPACK_PP_INTERCEPT_111
# define MSGPACK_PP_INTERCEPT_112
# define MSGPACK_PP_INTERCEPT_113
# define MSGPACK_PP_INTERCEPT_114
# define MSGPACK_PP_INTERCEPT_115
# define MSGPACK_PP_INTERCEPT_116
# define MSGPACK_PP_INTERCEPT_117
# define MSGPACK_PP_INTERCEPT_118
# define MSGPACK_PP_INTERCEPT_119
# define MSGPACK_PP_INTERCEPT_120
# define MSGPACK_PP_INTERCEPT_121
# define MSGPACK_PP_INTERCEPT_122
# define MSGPACK_PP_INTERCEPT_123
# define MSGPACK_PP_INTERCEPT_124
# define MSGPACK_PP_INTERCEPT_125
# define MSGPACK_PP_INTERCEPT_126
# define MSGPACK_PP_INTERCEPT_127
# define MSGPACK_PP_INTERCEPT_128
# define MSGPACK_PP_INTERCEPT_129
# define MSGPACK_PP_INTERCEPT_130
# define MSGPACK_PP_INTERCEPT_131
# define MSGPACK_PP_INTERCEPT_132
# define MSGPACK_PP_INTERCEPT_133
# define MSGPACK_PP_INTERCEPT_134
# define MSGPACK_PP_INTERCEPT_135
# define MSGPACK_PP_INTERCEPT_136
# define MSGPACK_PP_INTERCEPT_137
# define MSGPACK_PP_INTERCEPT_138
# define MSGPACK_PP_INTERCEPT_139
# define MSGPACK_PP_INTERCEPT_140
# define MSGPACK_PP_INTERCEPT_141
# define MSGPACK_PP_INTERCEPT_142
# define MSGPACK_PP_INTERCEPT_143
# define MSGPACK_PP_INTERCEPT_144
# define MSGPACK_PP_INTERCEPT_145
# define MSGPACK_PP_INTERCEPT_146
# define MSGPACK_PP_INTERCEPT_147
# define MSGPACK_PP_INTERCEPT_148
# define MSGPACK_PP_INTERCEPT_149
# define MSGPACK_PP_INTERCEPT_150
# define MSGPACK_PP_INTERCEPT_151
# define MSGPACK_PP_INTERCEPT_152
# define MSGPACK_PP_INTERCEPT_153
# define MSGPACK_PP_INTERCEPT_154
# define MSGPACK_PP_INTERCEPT_155
# define MSGPACK_PP_INTERCEPT_156
# define MSGPACK_PP_INTERCEPT_157
# define MSGPACK_PP_INTERCEPT_158
# define MSGPACK_PP_INTERCEPT_159
# define MSGPACK_PP_INTERCEPT_160
# define MSGPACK_PP_INTERCEPT_161
# define MSGPACK_PP_INTERCEPT_162
# define MSGPACK_PP_INTERCEPT_163
# define MSGPACK_PP_INTERCEPT_164
# define MSGPACK_PP_INTERCEPT_165
# define MSGPACK_PP_INTERCEPT_166
# define MSGPACK_PP_INTERCEPT_167
# define MSGPACK_PP_INTERCEPT_168
# define MSGPACK_PP_INTERCEPT_169
# define MSGPACK_PP_INTERCEPT_170
# define MSGPACK_PP_INTERCEPT_171
# define MSGPACK_PP_INTERCEPT_172
# define MSGPACK_PP_INTERCEPT_173
# define MSGPACK_PP_INTERCEPT_174
# define MSGPACK_PP_INTERCEPT_175
# define MSGPACK_PP_INTERCEPT_176
# define MSGPACK_PP_INTERCEPT_177
# define MSGPACK_PP_INTERCEPT_178
# define MSGPACK_PP_INTERCEPT_179
# define MSGPACK_PP_INTERCEPT_180
# define MSGPACK_PP_INTERCEPT_181
# define MSGPACK_PP_INTERCEPT_182
# define MSGPACK_PP_INTERCEPT_183
# define MSGPACK_PP_INTERCEPT_184
# define MSGPACK_PP_INTERCEPT_185
# define MSGPACK_PP_INTERCEPT_186
# define MSGPACK_PP_INTERCEPT_187
# define MSGPACK_PP_INTERCEPT_188
# define MSGPACK_PP_INTERCEPT_189
# define MSGPACK_PP_INTERCEPT_190
# define MSGPACK_PP_INTERCEPT_191
# define MSGPACK_PP_INTERCEPT_192
# define MSGPACK_PP_INTERCEPT_193
# define MSGPACK_PP_INTERCEPT_194
# define MSGPACK_PP_INTERCEPT_195
# define MSGPACK_PP_INTERCEPT_196
# define MSGPACK_PP_INTERCEPT_197
# define MSGPACK_PP_INTERCEPT_198
# define MSGPACK_PP_INTERCEPT_199
# define MSGPACK_PP_INTERCEPT_200
# define MSGPACK_PP_INTERCEPT_201
# define MSGPACK_PP_INTERCEPT_202
# define MSGPACK_PP_INTERCEPT_203
# define MSGPACK_PP_INTERCEPT_204
# define MSGPACK_PP_INTERCEPT_205
# define MSGPACK_PP_INTERCEPT_206
# define MSGPACK_PP_INTERCEPT_207
# define MSGPACK_PP_INTERCEPT_208
# define MSGPACK_PP_INTERCEPT_209
# define MSGPACK_PP_INTERCEPT_210
# define MSGPACK_PP_INTERCEPT_211
# define MSGPACK_PP_INTERCEPT_212
# define MSGPACK_PP_INTERCEPT_213
# define MSGPACK_PP_INTERCEPT_214
# define MSGPACK_PP_INTERCEPT_215
# define MSGPACK_PP_INTERCEPT_216
# define MSGPACK_PP_INTERCEPT_217
# define MSGPACK_PP_INTERCEPT_218
# define MSGPACK_PP_INTERCEPT_219
# define MSGPACK_PP_INTERCEPT_220
# define MSGPACK_PP_INTERCEPT_221
# define MSGPACK_PP_INTERCEPT_222
# define MSGPACK_PP_INTERCEPT_223
# define MSGPACK_PP_INTERCEPT_224
# define MSGPACK_PP_INTERCEPT_225
# define MSGPACK_PP_INTERCEPT_226
# define MSGPACK_PP_INTERCEPT_227
# define MSGPACK_PP_INTERCEPT_228
# define MSGPACK_PP_INTERCEPT_229
# define MSGPACK_PP_INTERCEPT_230
# define MSGPACK_PP_INTERCEPT_231
# define MSGPACK_PP_INTERCEPT_232
# define MSGPACK_PP_INTERCEPT_233
# define MSGPACK_PP_INTERCEPT_234
# define MSGPACK_PP_INTERCEPT_235
# define MSGPACK_PP_INTERCEPT_236
# define MSGPACK_PP_INTERCEPT_237
# define MSGPACK_PP_INTERCEPT_238
# define MSGPACK_PP_INTERCEPT_239
# define MSGPACK_PP_INTERCEPT_240
# define MSGPACK_PP_INTERCEPT_241
# define MSGPACK_PP_INTERCEPT_242
# define MSGPACK_PP_INTERCEPT_243
# define MSGPACK_PP_INTERCEPT_244
# define MSGPACK_PP_INTERCEPT_245
# define MSGPACK_PP_INTERCEPT_246
# define MSGPACK_PP_INTERCEPT_247
# define MSGPACK_PP_INTERCEPT_248
# define MSGPACK_PP_INTERCEPT_249
# define MSGPACK_PP_INTERCEPT_250
# define MSGPACK_PP_INTERCEPT_251
# define MSGPACK_PP_INTERCEPT_252
# define MSGPACK_PP_INTERCEPT_253
# define MSGPACK_PP_INTERCEPT_254
# define MSGPACK_PP_INTERCEPT_255
# define MSGPACK_PP_INTERCEPT_256
#
# endif
