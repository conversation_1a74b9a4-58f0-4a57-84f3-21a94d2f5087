@echo off
echo 🚀 Building MLC-LLM Android APK...
echo ================================

REM Set environment variables
set ANDROID_HOME=C:\Android\Sdk
set JAVA_HOME=C:\Program Files\Java\jdk-17

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java not found! Please install Java JDK 8+
    echo Download from: https://adoptium.net/
    pause
    exit /b 1
)

echo ✅ Java found
java -version

REM Try to build with gradlew
echo.
echo 🔨 Starting Gradle build...
echo.

REM Clean first
echo 🧹 Cleaning project...
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo ⚠️ Clean failed, continuing anyway...
)

REM Build debug APK
echo 🔨 Building debug APK...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    echo.
    echo 💡 Possible solutions:
    echo 1. Install Android Studio
    echo 2. Set ANDROID_HOME environment variable
    echo 3. Install Android SDK
    echo 4. Check internet connection for dependencies
    pause
    exit /b 1
)

REM Check if APK was created
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo.
    echo 🎉 APK built successfully!
    echo 📁 Location: app\build\outputs\apk\debug\app-debug.apk
    
    REM Get file size
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do (
        echo 📊 Size: %%~zA bytes
    )
    
    REM Copy to root directory
    copy "app\build\outputs\apk\debug\app-debug.apk" "..\mlc-llm-demo.apk"
    if %errorlevel% equ 0 (
        echo ✅ APK copied to: mlc-llm-demo.apk
    )
    
    echo.
    echo 📱 Installation Instructions:
    echo 1. Enable 'Unknown Sources' in Android Settings
    echo 2. Transfer APK to your Android device
    echo 3. Tap the APK file to install
    echo 4. Launch 'MLC-LLM Demo' app
    
) else (
    echo ❌ APK file not found!
    echo Expected: app\build\outputs\apk\debug\app-debug.apk
)

echo.
echo Build process completed.
pause
