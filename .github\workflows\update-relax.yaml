name: 'Relax Submodule Sync'

on:
  workflow_dispatch:

jobs:
  sync:
    name: 'Relax Submodule Sync'
    runs-on: ubuntu-latest

    defaults:
      run:
        shell: bash

    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: true

    - name: Git Sumbodule Update
      run: |
        git submodule update --remote 3rdparty/tvm

    - name: Commit update
      env:
        GITHUB_TOKEN: ${{ secrets.MLC_GITHUB_TOKEN }}
      run: |
        git config --global user.name 'Git bot'
        git config --global user.email '<EMAIL>'
        git remote set-url origin https://$<EMAIL>/mlc-ai/mlc-llm
        git commit -am "Auto updated submodule references" && git push || echo "No changes to commit"
