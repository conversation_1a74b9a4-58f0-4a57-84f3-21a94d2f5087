<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: simd_sm61.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">simd_sm61.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Templates exposing SIMD operators for SM60.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &quot;<a class="el" href="simd_8h_source.html">simd.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for simd_sm61.h:</div>
<div class="dyncontent">
<div class="center"><img src="simd__sm61_8h__incl.png" border="0" usemap="#simd__sm61_8h" alt=""/></div>
<map name="simd__sm61_8h" id="simd__sm61_8h">
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="simd__sm61_8h__dep__incl.png" border="0" usemap="#simd__sm61_8hdep" alt=""/></div>
<map name="simd__sm61_8hdep" id="simd__sm61_8hdep">
</map>
</div>
</div>
<p><a href="simd__sm61_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespacecutlass"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html">cutlass</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1arch"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html">cutlass::arch</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:af6adcb969a1e4acfed289a7839013695"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:af6adcb969a1e4acfed289a7839013695"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#af6adcb969a1e4acfed289a7839013695">cutlass::arch::dot</a> (Array&lt; int8_t, 4 &gt; const &amp;a, Array&lt; int8_t, 4 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:af6adcb969a1e4acfed289a7839013695"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (int8_t * int8_t) x 4 + int32_t.  <a href="namespacecutlass_1_1arch.html#af6adcb969a1e4acfed289a7839013695">More...</a><br /></td></tr>
<tr class="separator:af6adcb969a1e4acfed289a7839013695"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a027d23864f8145417feecf3f019f9ef4"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:a027d23864f8145417feecf3f019f9ef4"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a027d23864f8145417feecf3f019f9ef4">cutlass::arch::dot</a> (Array&lt; uint8_t, 4 &gt; const &amp;a, Array&lt; int8_t, 4 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:a027d23864f8145417feecf3f019f9ef4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (uint8_t * int8_t) x 4 + int32_t.  <a href="namespacecutlass_1_1arch.html#a027d23864f8145417feecf3f019f9ef4">More...</a><br /></td></tr>
<tr class="separator:a027d23864f8145417feecf3f019f9ef4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40582b9a769301d83e532fc5215a5259"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:a40582b9a769301d83e532fc5215a5259"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a40582b9a769301d83e532fc5215a5259">cutlass::arch::dot</a> (Array&lt; int8_t, 4 &gt; const &amp;a, Array&lt; uint8_t, 4 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:a40582b9a769301d83e532fc5215a5259"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (int8_t * uint8_t) x 4 + int32_t.  <a href="namespacecutlass_1_1arch.html#a40582b9a769301d83e532fc5215a5259">More...</a><br /></td></tr>
<tr class="separator:a40582b9a769301d83e532fc5215a5259"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a262f27261d801dfd9a9d1cde280321ac"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:a262f27261d801dfd9a9d1cde280321ac"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a262f27261d801dfd9a9d1cde280321ac">cutlass::arch::dot</a> (Array&lt; uint8_t, 4 &gt; const &amp;a, Array&lt; uint8_t, 4 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:a262f27261d801dfd9a9d1cde280321ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (uint8_t * uint8_t) x 4 + int32_t.  <a href="namespacecutlass_1_1arch.html#a262f27261d801dfd9a9d1cde280321ac">More...</a><br /></td></tr>
<tr class="separator:a262f27261d801dfd9a9d1cde280321ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c67269e7497315437d5ade0ab313ec8"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:a2c67269e7497315437d5ade0ab313ec8"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a2c67269e7497315437d5ade0ab313ec8">cutlass::arch::dot</a> (Array&lt; int16_t, 2 &gt; const &amp;a, Array&lt; int8_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:a2c67269e7497315437d5ade0ab313ec8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (int16_t * int8_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#a2c67269e7497315437d5ade0ab313ec8">More...</a><br /></td></tr>
<tr class="separator:a2c67269e7497315437d5ade0ab313ec8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9dc4e9c5eddc624e2aecd15ef4b55f35"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:a9dc4e9c5eddc624e2aecd15ef4b55f35"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a9dc4e9c5eddc624e2aecd15ef4b55f35">cutlass::arch::dot</a> (Array&lt; uint16_t, 2 &gt; const &amp;a, Array&lt; int8_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:a9dc4e9c5eddc624e2aecd15ef4b55f35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (uint16_t * int8_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#a9dc4e9c5eddc624e2aecd15ef4b55f35">More...</a><br /></td></tr>
<tr class="separator:a9dc4e9c5eddc624e2aecd15ef4b55f35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeedc20bfc0ea4dde354a9eee802bdea8"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:aeedc20bfc0ea4dde354a9eee802bdea8"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#aeedc20bfc0ea4dde354a9eee802bdea8">cutlass::arch::dot</a> (Array&lt; int16_t, 2 &gt; const &amp;a, Array&lt; uint8_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:aeedc20bfc0ea4dde354a9eee802bdea8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (int16_t * int8_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#aeedc20bfc0ea4dde354a9eee802bdea8">More...</a><br /></td></tr>
<tr class="separator:aeedc20bfc0ea4dde354a9eee802bdea8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4b65852d862718f9917ea2019752abb"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:ad4b65852d862718f9917ea2019752abb"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#ad4b65852d862718f9917ea2019752abb">cutlass::arch::dot</a> (Array&lt; uint16_t, 2 &gt; const &amp;a, Array&lt; uint8_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:ad4b65852d862718f9917ea2019752abb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (uint16_t * int8_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#ad4b65852d862718f9917ea2019752abb">More...</a><br /></td></tr>
<tr class="separator:ad4b65852d862718f9917ea2019752abb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3255aee11ed0bc172e248673576c37a"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:ab3255aee11ed0bc172e248673576c37a"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#ab3255aee11ed0bc172e248673576c37a">cutlass::arch::dot</a> (Array&lt; int16_t, 2 &gt; const &amp;a, Array&lt; int16_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:ab3255aee11ed0bc172e248673576c37a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (int16_t * int16_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#ab3255aee11ed0bc172e248673576c37a">More...</a><br /></td></tr>
<tr class="separator:ab3255aee11ed0bc172e248673576c37a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4af405b474cc766adcaec63d46cbbc49"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:a4af405b474cc766adcaec63d46cbbc49"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a4af405b474cc766adcaec63d46cbbc49">cutlass::arch::dot</a> (Array&lt; uint16_t, 2 &gt; const &amp;a, Array&lt; int16_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:a4af405b474cc766adcaec63d46cbbc49"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (uint16_t * int16_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#a4af405b474cc766adcaec63d46cbbc49">More...</a><br /></td></tr>
<tr class="separator:a4af405b474cc766adcaec63d46cbbc49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab42df2f28bc1b03350884df1048f060c"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:ab42df2f28bc1b03350884df1048f060c"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#ab42df2f28bc1b03350884df1048f060c">cutlass::arch::dot</a> (Array&lt; int16_t, 2 &gt; const &amp;a, Array&lt; uint16_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:ab42df2f28bc1b03350884df1048f060c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (int16_t * int16_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#ab42df2f28bc1b03350884df1048f060c">More...</a><br /></td></tr>
<tr class="separator:ab42df2f28bc1b03350884df1048f060c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae01862e2b75604eaca84e3b95bf110bf"><td class="memTemplParams" colspan="2">template&lt;&gt; </td></tr>
<tr class="memitem:ae01862e2b75604eaca84e3b95bf110bf"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int32_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#ae01862e2b75604eaca84e3b95bf110bf">cutlass::arch::dot</a> (Array&lt; uint16_t, 2 &gt; const &amp;a, Array&lt; uint16_t, 2 &gt; const &amp;b, int32_t accum)</td></tr>
<tr class="memdesc:ae01862e2b75604eaca84e3b95bf110bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot product operator - specialized for int32_t &lt;- (uint16_t * int16_t) x 2 + int32_t.  <a href="namespacecutlass_1_1arch.html#ae01862e2b75604eaca84e3b95bf110bf">More...</a><br /></td></tr>
<tr class="separator:ae01862e2b75604eaca84e3b95bf110bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
