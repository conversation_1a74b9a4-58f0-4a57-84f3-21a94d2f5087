"""This file specifies how MLC's Medusa parameters are quantized."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import NoQuantize

from .medusa_model import MedusaConfig, MedusaModel


def no_quant(
    model_config: MedusaConfig,
    quantization: NoQuantize,
) -> <PERSON>ple[nn.Module, QuantizeMapping]:
    """Quantize a Llama2 model without quantization."""
    model: nn.Module = MedusaModel(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
