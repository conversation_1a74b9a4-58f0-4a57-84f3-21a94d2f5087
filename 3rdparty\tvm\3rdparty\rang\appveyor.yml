environment:
  matrix:
    - arch: x64
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      compiler: msvc2017
    - arch: x64
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
      compiler: msvc2015

install:
  - ps:  wget  'https://github.com/ninja-build/ninja/releases/download/v1.8.2/ninja-win.zip' -OutFile ninja.zip
  - cmd: 7z x ninja.zip -oC:\ninja > nul
  - cmd: set PATH=C:\ninja;%PATH%

  - cmd: set MESON_PYTHON_PATH=C:\python35-x64
  - cmd: set PATH=%MESON_PYTHON_PATH%;%MESON_PYTHON_PATH%\Scripts;%PATH%
  - ps: $PKG_CONFIG_URL="http://ftp.gnome.org/pub/gnome/binaries/win64/dependencies/pkg-config_0.23-2_win64.zip";
  - ps: $GLIB_URL="http://ftp.gnome.org/pub/gnome/binaries/win64/glib/2.26/glib_2.26.1-1_win64.zip";
  - ps: $GETTEXT_URL="http://ftp.gnome.org/pub/gnome/binaries/win64/dependencies/gettext-runtime_0.18.1.1-2_win64.zip";
  - ps: wget $GLIB_URL -OutFile glib.zip
  - ps: wget $GETTEXT_URL -OutFile gettext.zip
  - ps: wget $PKG_CONFIG_URL -OutFile pkg_config.zip
  - ps: wget 'https://github.com/OpenCppCoverage/OpenCppCoverage/releases/download/release-*******/OpenCppCoverageSetup-x64-*******.exe' -OutFile coverage_setup.exe
  - cmd: coverage_setup.exe /VERYSILENT

  - cmd: 7z x glib.zip -oC:\glib > nul
  - cmd: 7z x gettext.zip -oC:\gettext > nul
  - cmd: 7z x pkg_config.zip -oC:\pkg_config > nul

  - cmd: set OPENCPPPATH="C:\\Program Files\\OpenCppCoverage"
  - cmd: set PATH=C:\glib\bin\;C:\gettext\bin\;C:\pkg_config\bin\;%OPENCPPPATH%;%PATH%
  - cmd: python -m pip install meson conan codecov

  - cmd: if %compiler%==msvc2015 ( call "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" %arch% )
  - cmd: if %compiler%==msvc2017 ( if %arch%==x64 ( call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars64.bat"))
  - cmd: conan remote add bincrafters https://api.bintray.com/conan/bincrafters/public-conan


build_script:
  - cmd: mkdir build && cd build
  - cmd: conan install ..
  - cmd: conan build ..
  - cmd: meson configure -Dbuildtype=debug
  - cmd: meson configure -Dwarning_level=3
  - cmd: ninja
  - cmd: cd test
  - cmd: mainTest.exe
  - cmd: colorTest.exe
  - cmd: envTermMissing.exe
  - cmd: OpenCppCoverage --sources C:\projects\rang --export_type=binary:envTermMissingReport.bin -- envTermMissing.exe
  - cmd: OpenCppCoverage --sources C:\projects\rang --export_type=binary:mainTestReport.bin -- mainTest.exe
  - cmd: OpenCppCoverage --sources C:\projects\rang --export_type=binary:colorTestReport.bin -- colorTest.exe
  - cmd: OpenCppCoverage --sources C:\projects\rang --export_type=cobertura:overallReport.xml --input_coverage=mainTestReport.bin --input_coverage=envTermMissingReport.bin --input_coverage=colorTestReport.bin
  - cmd: codecov --root ../.. --no-color --disable gcov -f overallReport.xml
