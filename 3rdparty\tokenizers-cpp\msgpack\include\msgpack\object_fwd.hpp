//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2008-2016 FURUHASH<PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef MSGPACK_OBJECT_FWD_HPP
#define MSGPACK_OBJECT_FWD_HPP

#include "msgpack/object_fwd_decl.hpp"

#include "msgpack/v1/object_fwd.hpp"
#include "msgpack/v2/object_fwd.hpp"
#include "msgpack/v3/object_fwd.hpp"

#endif // MSGPACK_OBJECT_FWD_HPP
