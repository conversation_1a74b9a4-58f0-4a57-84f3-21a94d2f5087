/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2011 <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/

#ifndef _SDL_config_h
#define _SDL_config_h

/**
 *  \file SDL_config.h.in
 *
 *  This is a set of defines to configure the SDL features
 */

/* General platform specific identifiers */
#include "SDL_platform.h"

/* Make sure that this isn't included by Visual C++ */
#ifdef _MSC_VER
#error You should run hg revert SDL_config.h 
#endif

/* C language features */
#undef const
#undef inline
#undef volatile

/* C datatypes */
#undef SIZEOF_VOIDP
#undef HAVE_GCC_ATOMICS
#undef HAVE_GCC_SYNC_LOCK_TEST_AND_SET
#undef HAVE_PTHREAD_SPINLOCK

/* Comment this if you want to build without any C library requirements */
#undef HAVE_LIBC
#if HAVE_LIBC

/* Useful headers */
#undef HAVE_ALLOCA_H
#undef HAVE_SYS_TYPES_H
#undef HAVE_STDIO_H
#undef STDC_HEADERS
#undef HAVE_STDLIB_H
#undef HAVE_STDARG_H
#undef HAVE_MALLOC_H
#undef HAVE_MEMORY_H
#undef HAVE_STRING_H
#undef HAVE_STRINGS_H
#undef HAVE_INTTYPES_H
#undef HAVE_STDINT_H
#undef HAVE_CTYPE_H
#undef HAVE_MATH_H
#undef HAVE_ICONV_H
#undef HAVE_SIGNAL_H
#undef HAVE_ALTIVEC_H

/* C library functions */
#undef HAVE_MALLOC
#undef HAVE_CALLOC
#undef HAVE_REALLOC
#undef HAVE_FREE
#undef HAVE_ALLOCA
#ifndef __WIN32__ /* Don't use C runtime versions of these on Windows */
#undef HAVE_GETENV
#undef HAVE_SETENV
#undef HAVE_PUTENV
#undef HAVE_UNSETENV
#endif
#undef HAVE_QSORT
#undef HAVE_ABS
#undef HAVE_BCOPY
#undef HAVE_MEMSET
#undef HAVE_MEMCPY
#undef HAVE_MEMMOVE
#undef HAVE_MEMCMP
#undef HAVE_STRLEN
#undef HAVE_STRLCPY
#undef HAVE_STRLCAT
#undef HAVE_STRDUP
#undef HAVE__STRREV
#undef HAVE__STRUPR
#undef HAVE__STRLWR
#undef HAVE_INDEX
#undef HAVE_RINDEX
#undef HAVE_STRCHR
#undef HAVE_STRRCHR
#undef HAVE_STRSTR
#undef HAVE_ITOA
#undef HAVE__LTOA
#undef HAVE__UITOA
#undef HAVE__ULTOA
#undef HAVE_STRTOL
#undef HAVE_STRTOUL
#undef HAVE__I64TOA
#undef HAVE__UI64TOA
#undef HAVE_STRTOLL
#undef HAVE_STRTOULL
#undef HAVE_STRTOD
#undef HAVE_ATOI
#undef HAVE_ATOF
#undef HAVE_STRCMP
#undef HAVE_STRNCMP
#undef HAVE__STRICMP
#undef HAVE_STRCASECMP
#undef HAVE__STRNICMP
#undef HAVE_STRNCASECMP
#undef HAVE_SSCANF
#undef HAVE_SNPRINTF
#undef HAVE_VSNPRINTF
#undef HAVE_M_PI
#undef HAVE_ATAN
#undef HAVE_ATAN2
#undef HAVE_CEIL
#undef HAVE_COPYSIGN
#undef HAVE_COS
#undef HAVE_COSF
#undef HAVE_FABS
#undef HAVE_FLOOR
#undef HAVE_LOG
#undef HAVE_POW
#undef HAVE_SCALBN
#undef HAVE_SIN
#undef HAVE_SINF
#undef HAVE_SQRT
#undef HAVE_SIGACTION
#undef HAVE_SA_SIGACTION
#undef HAVE_SETJMP
#undef HAVE_NANOSLEEP
#undef HAVE_SYSCONF
#undef HAVE_SYSCTLBYNAME
#undef HAVE_CLOCK_GETTIME
#undef HAVE_GETPAGESIZE
#undef HAVE_MPROTECT
#undef HAVE_ICONV

#else
/* We may need some replacement for stdarg.h here */
#include <stdarg.h>
#endif /* HAVE_LIBC */

/* SDL internal assertion support */
#undef SDL_DEFAULT_ASSERT_LEVEL

/* Allow disabling of core subsystems */
#undef SDL_ATOMIC_DISABLED
#undef SDL_AUDIO_DISABLED
#undef SDL_CPUINFO_DISABLED
#undef SDL_EVENTS_DISABLED
#undef SDL_FILE_DISABLED
#undef SDL_JOYSTICK_DISABLED
#undef SDL_HAPTIC_DISABLED
#undef SDL_LOADSO_DISABLED
#undef SDL_RENDER_DISABLED
#undef SDL_THREADS_DISABLED
#undef SDL_TIMERS_DISABLED
#undef SDL_VIDEO_DISABLED
#undef SDL_POWER_DISABLED

/* Enable various audio drivers */
#undef SDL_AUDIO_DRIVER_ALSA
#undef SDL_AUDIO_DRIVER_ALSA_DYNAMIC
#undef SDL_AUDIO_DRIVER_ARTS
#undef SDL_AUDIO_DRIVER_ARTS_DYNAMIC
#undef SDL_AUDIO_DRIVER_PULSEAUDIO
#undef SDL_AUDIO_DRIVER_PULSEAUDIO_DYNAMIC
#undef SDL_AUDIO_DRIVER_BEOSAUDIO
#undef SDL_AUDIO_DRIVER_BSD
#undef SDL_AUDIO_DRIVER_COREAUDIO
#undef SDL_AUDIO_DRIVER_DISK
#undef SDL_AUDIO_DRIVER_DUMMY
#undef SDL_AUDIO_DRIVER_XAUDIO2
#undef SDL_AUDIO_DRIVER_DSOUND
#undef SDL_AUDIO_DRIVER_ESD
#undef SDL_AUDIO_DRIVER_ESD_DYNAMIC
#undef SDL_AUDIO_DRIVER_NAS
#undef SDL_AUDIO_DRIVER_NAS_DYNAMIC
#undef SDL_AUDIO_DRIVER_NDS
#undef SDL_AUDIO_DRIVER_OSS
#undef SDL_AUDIO_DRIVER_OSS_SOUNDCARD_H
#undef SDL_AUDIO_DRIVER_PAUDIO
#undef SDL_AUDIO_DRIVER_QSA
#undef SDL_AUDIO_DRIVER_SUNAUDIO
#undef SDL_AUDIO_DRIVER_WINMM
#undef SDL_AUDIO_DRIVER_FUSIONSOUND
#undef SDL_AUDIO_DRIVER_FUSIONSOUND_DYNAMIC

/* Enable various input drivers */
#undef SDL_INPUT_LINUXEV
#undef SDL_INPUT_TSLIB
#undef SDL_JOYSTICK_BEOS
#undef SDL_JOYSTICK_DINPUT
#undef SDL_JOYSTICK_DUMMY
#undef SDL_JOYSTICK_IOKIT
#undef SDL_JOYSTICK_LINUX
#undef SDL_JOYSTICK_NDS
#undef SDL_JOYSTICK_WINMM
#undef SDL_JOYSTICK_USBHID
#undef SDL_JOYSTICK_USBHID_MACHINE_JOYSTICK_H
#undef SDL_HAPTIC_DUMMY
#undef SDL_HAPTIC_LINUX
#undef SDL_HAPTIC_IOKIT
#undef SDL_HAPTIC_DINPUT

/* Enable various shared object loading systems */
#undef SDL_LOADSO_BEOS
#undef SDL_LOADSO_DLCOMPAT
#undef SDL_LOADSO_DLOPEN
#undef SDL_LOADSO_DUMMY
#undef SDL_LOADSO_LDG
#undef SDL_LOADSO_WINDOWS

/* Enable various threading systems */
#undef SDL_THREAD_BEOS
#undef SDL_THREAD_NDS
#undef SDL_THREAD_PTHREAD
#undef SDL_THREAD_PTHREAD_RECURSIVE_MUTEX
#undef SDL_THREAD_PTHREAD_RECURSIVE_MUTEX_NP
#undef SDL_THREAD_SPROC
#undef SDL_THREAD_WINDOWS

/* Enable various timer systems */
#undef SDL_TIMER_BEOS
#undef SDL_TIMER_DUMMY
#undef SDL_TIMER_NDS
#undef SDL_TIMER_UNIX
#undef SDL_TIMER_WINDOWS
#undef SDL_TIMER_WINCE

/* Enable various video drivers */
#undef SDL_VIDEO_DRIVER_BWINDOW
#undef SDL_VIDEO_DRIVER_COCOA
#undef SDL_VIDEO_DRIVER_DIRECTFB
#undef SDL_VIDEO_DRIVER_DIRECTFB_DYNAMIC
#undef SDL_VIDEO_DRIVER_DUMMY
#undef SDL_VIDEO_DRIVER_NDS
#undef SDL_VIDEO_DRIVER_WINDOWS
#undef SDL_VIDEO_DRIVER_X11
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC_XEXT
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC_XCURSOR
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC_XINERAMA
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC_XINPUT
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC_XRANDR
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC_XSS
#undef SDL_VIDEO_DRIVER_X11_DYNAMIC_XVIDMODE
#undef SDL_VIDEO_DRIVER_X11_XCURSOR
#undef SDL_VIDEO_DRIVER_X11_XINERAMA
#undef SDL_VIDEO_DRIVER_X11_XINPUT
#undef SDL_VIDEO_DRIVER_X11_XRANDR
#undef SDL_VIDEO_DRIVER_X11_XSCRNSAVER
#undef SDL_VIDEO_DRIVER_X11_XSHAPE
#undef SDL_VIDEO_DRIVER_X11_XVIDMODE

#undef SDL_VIDEO_RENDER_D3D
#undef SDL_VIDEO_RENDER_OGL
#undef SDL_VIDEO_RENDER_OGL_ES
#undef SDL_VIDEO_RENDER_DIRECTFB

/* Enable OpenGL support */
#undef SDL_VIDEO_OPENGL
#undef SDL_VIDEO_OPENGL_ES
#undef SDL_VIDEO_OPENGL_BGL
#undef SDL_VIDEO_OPENGL_CGL
#undef SDL_VIDEO_OPENGL_GLX
#undef SDL_VIDEO_OPENGL_WGL
#undef SDL_VIDEO_OPENGL_OSMESA
#undef SDL_VIDEO_OPENGL_OSMESA_DYNAMIC

/* Enable system power support */
#undef SDL_POWER_LINUX
#undef SDL_POWER_WINDOWS
#undef SDL_POWER_MACOSX
#undef SDL_POWER_BEOS
#undef SDL_POWER_NINTENDODS
#undef SDL_POWER_HARDWIRED

/* Enable assembly routines */
#undef SDL_ASSEMBLY_ROUTINES
#undef SDL_ALTIVEC_BLITTERS

#endif /* _SDL_config_h */
