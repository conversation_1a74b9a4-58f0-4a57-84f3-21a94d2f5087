//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2016 <PERSON><PERSON><PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef MSGPACK_ITERATOR_DECL_HPP
#define MSGPACK_ITERATOR_DECL_HPP

#include "msgpack/v1/iterator_decl.hpp"
#include "msgpack/v2/iterator_decl.hpp"
#include "msgpack/v3/iterator_decl.hpp"

#endif // MSGPACK_ITERATOR_DECL_HPP
