"""This file specifies how MLC's BERT parameters are quantized using group quantization
or other formats."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import FTQuantize, GroupQuantize, NoQuantize

from .bert_model import Bert<PERSON>onfig, Bert<PERSON><PERSON><PERSON>


def group_quant(
    model_config: BertConfig,
    quantization: GroupQuantize,
) -> <PERSON>ple[nn.Module, QuantizeMapping]:
    """Quantize a BERT-architecture model using group quantization."""
    model: nn.Module = BertModel(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    quantization.tensor_parallel_shards = model_config.tensor_parallel_shards
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def ft_quant(
    model_config: BertConfig,
    quantization: FTQuantize,
) -> <PERSON><PERSON>[nn.<PERSON><PERSON><PERSON>, QuantizeMapping]:
    """Quantize a BERT-architecture model using FasterTransformer quantization."""
    model: nn.Module = BertModel(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def no_quant(
    model_config: BertConfig,
    quantization: NoQuantize,
) -> Tuple[nn.Module, QuantizeMapping]:
    """Quantize a BERT model without quantization."""
    model: nn.Module = BertModel(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
