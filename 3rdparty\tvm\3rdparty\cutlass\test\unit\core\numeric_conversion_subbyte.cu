/***************************************************************************************************
 * Copyright (c) 2017 - 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
 * SPDX-License-Identifier: BSD-3-Clause
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. Neither the name of the copyright holder nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 **************************************************************************************************/
/*! \file
    \brief Unit tests for conversion operators.
*/

#include "../common/cutlass_unit_test.h"

#include "cutlass/numeric_conversion.h"
#include "cutlass/integer_subbyte.h"

namespace test::core::host {

template <class DstValueType, class SrcValueType, int NumElements>
void run_test() {
  cutlass::Array<DstValueType, NumElements> dst;
  dst.clear();

  cutlass::Array<SrcValueType, NumElements> src;
  for (int k = 0; k < NumElements; ++k) {
    src[k] = SrcValueType(k+1);
  }

  cutlass::NumericArrayConverter<DstValueType, SrcValueType, NumElements> converter;
  dst = converter(src);

  for (int k = 0; k < NumElements; ++k) {
    EXPECT_TRUE(static_cast<int>(src[k]) == static_cast<int>(dst[k]));
  }
}

} // namespace test::core::host

TEST(NumericArrayConversion, Subbyte_int8_int8) {
  test::core::host::run_test<int8_t, int8_t, 8>();
}

TEST(NumericArrayConversion, Subbyte_int8_int4) {
  test::core::host::run_test<int8_t, cutlass::int4b_t, 8>();
}

