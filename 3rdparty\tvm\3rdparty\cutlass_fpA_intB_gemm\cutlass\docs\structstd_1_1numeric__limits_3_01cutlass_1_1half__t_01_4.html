<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: std::numeric_limits&lt; cutlass::half_t &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>std</b></li><li class="navelem"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">numeric_limits&lt; cutlass::half_t &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">std::numeric_limits&lt; cutlass::half_t &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Numeric limits.  
</p>

<p><code>#include &lt;<a class="el" href="half_8h_source.html">half.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:ad9175b4d7b32fe18cf9c07e4f559b32c"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ad9175b4d7b32fe18cf9c07e4f559b32c">min</a> ()</td></tr>
<tr class="memdesc:ad9175b4d7b32fe18cf9c07e4f559b32c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Least positive value.  <a href="#ad9175b4d7b32fe18cf9c07e4f559b32c">More...</a><br /></td></tr>
<tr class="separator:ad9175b4d7b32fe18cf9c07e4f559b32c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2ae43139037875e38056a675ae1f6c4"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ac2ae43139037875e38056a675ae1f6c4">lowest</a> ()</td></tr>
<tr class="memdesc:ac2ae43139037875e38056a675ae1f6c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum finite value.  <a href="#ac2ae43139037875e38056a675ae1f6c4">More...</a><br /></td></tr>
<tr class="separator:ac2ae43139037875e38056a675ae1f6c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39a5774583daedbb5ac4aaaaa8034883"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a39a5774583daedbb5ac4aaaaa8034883">max</a> ()</td></tr>
<tr class="memdesc:a39a5774583daedbb5ac4aaaaa8034883"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum finite value.  <a href="#a39a5774583daedbb5ac4aaaaa8034883">More...</a><br /></td></tr>
<tr class="separator:a39a5774583daedbb5ac4aaaaa8034883"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9fc3a009eaff0c922307f2780ee3fc0"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab9fc3a009eaff0c922307f2780ee3fc0">epsilon</a> ()</td></tr>
<tr class="memdesc:ab9fc3a009eaff0c922307f2780ee3fc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns smallest finite value.  <a href="#ab9fc3a009eaff0c922307f2780ee3fc0">More...</a><br /></td></tr>
<tr class="separator:ab9fc3a009eaff0c922307f2780ee3fc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0a036db7a1ad11c65e876020c78b1a5"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab0a036db7a1ad11c65e876020c78b1a5">round_error</a> ()</td></tr>
<tr class="memdesc:ab0a036db7a1ad11c65e876020c78b1a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns smallest finite value.  <a href="#ab0a036db7a1ad11c65e876020c78b1a5">More...</a><br /></td></tr>
<tr class="separator:ab0a036db7a1ad11c65e876020c78b1a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7a40820e64282376a050095d5004b74"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab7a40820e64282376a050095d5004b74">infinity</a> ()</td></tr>
<tr class="memdesc:ab7a40820e64282376a050095d5004b74"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns smallest finite value.  <a href="#ab7a40820e64282376a050095d5004b74">More...</a><br /></td></tr>
<tr class="separator:ab7a40820e64282376a050095d5004b74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c7eafdd3b121353c0914dc6e1c0d108"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a8c7eafdd3b121353c0914dc6e1c0d108">quiet_NaN</a> ()</td></tr>
<tr class="memdesc:a8c7eafdd3b121353c0914dc6e1c0d108"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns smallest finite value.  <a href="#a8c7eafdd3b121353c0914dc6e1c0d108">More...</a><br /></td></tr>
<tr class="separator:a8c7eafdd3b121353c0914dc6e1c0d108"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a423fb5b95e6071e832d40918e597f63f"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a423fb5b95e6071e832d40918e597f63f">signaling_NaN</a> ()</td></tr>
<tr class="memdesc:a423fb5b95e6071e832d40918e597f63f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns smallest finite value.  <a href="#a423fb5b95e6071e832d40918e597f63f">More...</a><br /></td></tr>
<tr class="separator:a423fb5b95e6071e832d40918e597f63f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c05c19022c183e8734ada65c8970af5"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a2c05c19022c183e8734ada65c8970af5">denorm_min</a> ()</td></tr>
<tr class="memdesc:a2c05c19022c183e8734ada65c8970af5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns smallest finite value.  <a href="#a2c05c19022c183e8734ada65c8970af5">More...</a><br /></td></tr>
<tr class="separator:a2c05c19022c183e8734ada65c8970af5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:ade4affb586360c5356a7939c1b343a40"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ade4affb586360c5356a7939c1b343a40">is_specialized</a> = true</td></tr>
<tr class="separator:ade4affb586360c5356a7939c1b343a40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3a169117baca2e7fae33846caa5dbfd"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab3a169117baca2e7fae33846caa5dbfd">is_signed</a> = true</td></tr>
<tr class="separator:ab3a169117baca2e7fae33846caa5dbfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a2ec168a6f0e9f55dc42d3b3e5fff25"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a2a2ec168a6f0e9f55dc42d3b3e5fff25">is_integer</a> = false</td></tr>
<tr class="separator:a2a2ec168a6f0e9f55dc42d3b3e5fff25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7413e9cd24eb03a86cc5f2d47c49db3e"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a7413e9cd24eb03a86cc5f2d47c49db3e">is_exact</a> = false</td></tr>
<tr class="separator:a7413e9cd24eb03a86cc5f2d47c49db3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f7f2fbe6cd7a04803b90b8fa9172098"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a6f7f2fbe6cd7a04803b90b8fa9172098">has_infinity</a> = true</td></tr>
<tr class="separator:a6f7f2fbe6cd7a04803b90b8fa9172098"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d75832e46bc154758e35a03a624ccf8"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a3d75832e46bc154758e35a03a624ccf8">has_quiet_NaN</a> = true</td></tr>
<tr class="separator:a3d75832e46bc154758e35a03a624ccf8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68a0d0f6ecc2f3b84f2e71475b2c48bd"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a68a0d0f6ecc2f3b84f2e71475b2c48bd">has_signaling_NaN</a> = false</td></tr>
<tr class="separator:a68a0d0f6ecc2f3b84f2e71475b2c48bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf46b5d03403828c1e6633fb714ffd84"><td class="memItemLeft" align="right" valign="top">static std::float_denorm_style const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#aaf46b5d03403828c1e6633fb714ffd84">has_denorm</a> = std::denorm_present</td></tr>
<tr class="separator:aaf46b5d03403828c1e6633fb714ffd84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b371f82151fd0238b7da083fa2b87a9"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a8b371f82151fd0238b7da083fa2b87a9">has_denorm_loss</a> = true</td></tr>
<tr class="separator:a8b371f82151fd0238b7da083fa2b87a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0af85c1d7c83ca03ed0c083fe22262f"><td class="memItemLeft" align="right" valign="top">static std::float_round_style const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab0af85c1d7c83ca03ed0c083fe22262f">round_style</a> = std::round_to_nearest</td></tr>
<tr class="separator:ab0af85c1d7c83ca03ed0c083fe22262f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc86b85a1fe209658a50d8c06e54cb77"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#afc86b85a1fe209658a50d8c06e54cb77">is_iec559</a> = true</td></tr>
<tr class="separator:afc86b85a1fe209658a50d8c06e54cb77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59737f49161b87c259683c26737f42c2"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a59737f49161b87c259683c26737f42c2">is_bounded</a> = true</td></tr>
<tr class="separator:a59737f49161b87c259683c26737f42c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a640a034527a3577039053113bc1c5e46"><td class="memItemLeft" align="right" valign="top">static bool const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a640a034527a3577039053113bc1c5e46">is_modulo</a> = false</td></tr>
<tr class="separator:a640a034527a3577039053113bc1c5e46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92152311525685a53c6a0db4cb74f193"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a92152311525685a53c6a0db4cb74f193">digits</a> = 10</td></tr>
<tr class="separator:a92152311525685a53c6a0db4cb74f193"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a2c05c19022c183e8734ada65c8970af5"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::denorm_min </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab9fc3a009eaff0c922307f2780ee3fc0"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::epsilon </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab7a40820e64282376a050095d5004b74"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::infinity </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac2ae43139037875e38056a675ae1f6c4"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::lowest </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a39a5774583daedbb5ac4aaaaa8034883"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::max </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad9175b4d7b32fe18cf9c07e4f559b32c"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::min </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8c7eafdd3b121353c0914dc6e1c0d108"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::quiet_NaN </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab0a036db7a1ad11c65e876020c78b1a5"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::round_error </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a423fb5b95e6071e832d40918e597f63f"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::signaling_NaN </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a92152311525685a53c6a0db4cb74f193"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::digits = 10</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aaf46b5d03403828c1e6633fb714ffd84"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::float_denorm_style const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::has_denorm = std::denorm_present</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8b371f82151fd0238b7da083fa2b87a9"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::has_denorm_loss = true</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6f7f2fbe6cd7a04803b90b8fa9172098"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::has_infinity = true</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3d75832e46bc154758e35a03a624ccf8"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::has_quiet_NaN = true</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a68a0d0f6ecc2f3b84f2e71475b2c48bd"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::has_signaling_NaN = false</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a59737f49161b87c259683c26737f42c2"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::is_bounded = true</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7413e9cd24eb03a86cc5f2d47c49db3e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::is_exact = false</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afc86b85a1fe209658a50d8c06e54cb77"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::is_iec559 = true</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2a2ec168a6f0e9f55dc42d3b3e5fff25"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::is_integer = false</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a640a034527a3577039053113bc1c5e46"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::is_modulo = false</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab3a169117baca2e7fae33846caa5dbfd"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::is_signed = true</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ade4affb586360c5356a7939c1b343a40"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::is_specialized = true</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab0af85c1d7c83ca03ed0c083fe22262f"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::float_round_style const std::numeric_limits&lt; <a class="el" href="structcutlass_1_1half__t.html">cutlass::half_t</a> &gt;::round_style = std::round_to_nearest</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="half_8h_source.html">half.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
