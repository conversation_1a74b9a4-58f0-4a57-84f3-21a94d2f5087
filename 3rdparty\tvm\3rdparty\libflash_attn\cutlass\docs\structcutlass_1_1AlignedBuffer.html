<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::AlignedBuffer&lt; T, N, Align &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1AlignedBuffer.html">AlignedBuffer</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1AlignedBuffer-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::AlignedBuffer&lt; T, N, Align &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Modifies semantics of cutlass::Array&lt;&gt; to provide guaranteed alignment.  
</p>

<p><code>#include &lt;<a class="el" href="aligned__buffer_8h_source.html">aligned_buffer.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a49b7cb4bf1ff845619f927bf1d495e61"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> = uint8_t</td></tr>
<tr class="memdesc:a49b7cb4bf1ff845619f927bf1d495e61"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal storage type.  <a href="#a49b7cb4bf1ff845619f927bf1d495e61">More...</a><br /></td></tr>
<tr class="separator:a49b7cb4bf1ff845619f927bf1d495e61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8c8238434f9029b996796dbcf175282"><td class="memItemLeft" align="right" valign="top">typedef T&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">value_type</a></td></tr>
<tr class="separator:aa8c8238434f9029b996796dbcf175282"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1cae39c2587cffc0957ca668c95989f"><td class="memItemLeft" align="right" valign="top">typedef size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">size_type</a></td></tr>
<tr class="separator:aa1cae39c2587cffc0957ca668c95989f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68527eff431854311f0221aa61e1c94d"><td class="memItemLeft" align="right" valign="top">typedef ptrdiff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a68527eff431854311f0221aa61e1c94d">difference_type</a></td></tr>
<tr class="separator:a68527eff431854311f0221aa61e1c94d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c0b77fef16a9f3d7007817a9fc32bf1"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">value_type</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a1c0b77fef16a9f3d7007817a9fc32bf1">pointer</a></td></tr>
<tr class="separator:a1c0b77fef16a9f3d7007817a9fc32bf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ea058b3d86ad689240836e2d89686c4"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">value_type</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a9ea058b3d86ad689240836e2d89686c4">const_pointer</a></td></tr>
<tr class="separator:a9ea058b3d86ad689240836e2d89686c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6083193cadb42a445442da9be737d934"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">Array</a> = <a class="el" href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">Array</a>&lt; T, N &gt;</td></tr>
<tr class="separator:a6083193cadb42a445442da9be737d934"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa029189fb46528b5eb5f50060cbf28e"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#afa029189fb46528b5eb5f50060cbf28e">reference</a> = typename Array::reference</td></tr>
<tr class="separator:afa029189fb46528b5eb5f50060cbf28e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a878e461a9368a2e9639464caf78ac718"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a878e461a9368a2e9639464caf78ac718">const_reference</a> = typename Array::const_reference</td></tr>
<tr class="separator:a878e461a9368a2e9639464caf78ac718"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8ed8b9d3469621fc82d0041846c59da2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a1c0b77fef16a9f3d7007817a9fc32bf1">pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">data</a> ()</td></tr>
<tr class="separator:a8ed8b9d3469621fc82d0041846c59da2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acbfc684b16c9c717df5712bcb729acf3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a9ea058b3d86ad689240836e2d89686c4">const_pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#acbfc684b16c9c717df5712bcb729acf3">data</a> () const </td></tr>
<tr class="separator:acbfc684b16c9c717df5712bcb729acf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a87c3b8f14893d30f374bde2b88052c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a3a87c3b8f14893d30f374bde2b88052c">raw_data</a> ()</td></tr>
<tr class="separator:a3a87c3b8f14893d30f374bde2b88052c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae591f458d228ec8ac08caf8846dab67d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#ae591f458d228ec8ac08caf8846dab67d">raw_data</a> () const </td></tr>
<tr class="separator:ae591f458d228ec8ac08caf8846dab67d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37921fffef065c4da23ccc328db45f14"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a37921fffef065c4da23ccc328db45f14">empty</a> () const </td></tr>
<tr class="separator:a37921fffef065c4da23ccc328db45f14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b588b6018a1f36ce68e4e0f2eac2247"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">size_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a2b588b6018a1f36ce68e4e0f2eac2247">size</a> () const </td></tr>
<tr class="separator:a2b588b6018a1f36ce68e4e0f2eac2247"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a673d7413585d44f0c025840c9b84b6b3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">size_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a673d7413585d44f0c025840c9b84b6b3">max_size</a> () const </td></tr>
<tr class="separator:a673d7413585d44f0c025840c9b84b6b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a16e2e6aa35c03e4a65b062123d9490ba"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a16e2e6aa35c03e4a65b062123d9490ba">kCount</a> = N</td></tr>
<tr class="memdesc:a16e2e6aa35c03e4a65b062123d9490ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of logical elements held in buffer.  <a href="#a16e2e6aa35c03e4a65b062123d9490ba">More...</a><br /></td></tr>
<tr class="separator:a16e2e6aa35c03e4a65b062123d9490ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bab3f7468fe898b8abddba83f0b581a"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#a0bab3f7468fe898b8abddba83f0b581a">kAlign</a> = Align</td></tr>
<tr class="memdesc:a0bab3f7468fe898b8abddba83f0b581a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Alignment requirement in bytes.  <a href="#a0bab3f7468fe898b8abddba83f0b581a">More...</a><br /></td></tr>
<tr class="separator:a0bab3f7468fe898b8abddba83f0b581a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7742a9814b15dafe3e05f98771a32e3"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1AlignedBuffer.html#ae7742a9814b15dafe3e05f98771a32e3">kBytes</a></td></tr>
<tr class="memdesc:ae7742a9814b15dafe3e05f98771a32e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of storage elements.  <a href="#ae7742a9814b15dafe3e05f98771a32e3">More...</a><br /></td></tr>
<tr class="separator:ae7742a9814b15dafe3e05f98771a32e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a6083193cadb42a445442da9be737d934"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">Array</a> =  <a class="el" href="structcutlass_1_1AlignedBuffer.html#a6083193cadb42a445442da9be737d934">Array</a>&lt;T, N&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9ea058b3d86ad689240836e2d89686c4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">value_type</a> const* <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#a9ea058b3d86ad689240836e2d89686c4">const_pointer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a878e461a9368a2e9639464caf78ac718"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#a878e461a9368a2e9639464caf78ac718">const_reference</a> =  typename Array::const_reference</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a68527eff431854311f0221aa61e1c94d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ptrdiff_t <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#a68527eff431854311f0221aa61e1c94d">difference_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1c0b77fef16a9f3d7007817a9fc32bf1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">value_type</a>* <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#a1c0b77fef16a9f3d7007817a9fc32bf1">pointer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afa029189fb46528b5eb5f50060cbf28e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#afa029189fb46528b5eb5f50060cbf28e">reference</a> =  typename Array::reference</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa1cae39c2587cffc0957ca668c95989f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef size_t <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">size_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a49b7cb4bf1ff845619f927bf1d495e61"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> =  uint8_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa8c8238434f9029b996796dbcf175282"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef T <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::<a class="el" href="structcutlass_1_1AlignedBuffer.html#aa8c8238434f9029b996796dbcf175282">value_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a8ed8b9d3469621fc82d0041846c59da2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a1c0b77fef16a9f3d7007817a9fc32bf1">pointer</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acbfc684b16c9c717df5712bcb729acf3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a9ea058b3d86ad689240836e2d89686c4">const_pointer</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a37921fffef065c4da23ccc328db45f14"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> bool <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::empty </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a673d7413585d44f0c025840c9b84b6b3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">size_type</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::max_size </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3a87c3b8f14893d30f374bde2b88052c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a>* <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::raw_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae591f458d228ec8ac08caf8846dab67d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#a49b7cb4bf1ff845619f927bf1d495e61">Storage</a> const* <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::raw_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2b588b6018a1f36ce68e4e0f2eac2247"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html#aa1cae39c2587cffc0957ca668c95989f">size_type</a> <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::size </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a0bab3f7468fe898b8abddba83f0b581a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::kAlign = Align</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae7742a9814b15dafe3e05f98771a32e3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::kBytes</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= </div><div class="line">    (<a class="code" href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">sizeof_bits&lt;T&gt;::value</a> * N + 7) / 8</div></div><!-- fragment -->
</div>
</div>
<a class="anchor" id="a16e2e6aa35c03e4a65b062123d9490ba"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, int N, int Align = 16&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a>&lt; T, N, Align &gt;::kCount = N</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="aligned__buffer_8h_source.html">aligned_buffer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
