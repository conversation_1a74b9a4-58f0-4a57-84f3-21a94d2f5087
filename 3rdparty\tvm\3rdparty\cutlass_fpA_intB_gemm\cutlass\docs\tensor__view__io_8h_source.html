<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_view_io.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_view_io.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__view__io_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">* Copyright (c) 2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">* Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">* provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">*     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">*       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">*     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">*       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">*       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">*     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">*       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">*       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">* FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">* FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment">* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment">* STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">* OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment">**************************************************************************************************/</span></div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="core__io_8h.html">cutlass/core_io.h</a>&quot;</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__view_8h.html">cutlass/tensor_view.h</a>&quot;</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">namespace </span>detail {</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;  <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;  <span class="keyword">typename</span> Layout</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;&gt;</div><div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1detail.html#a5b1d94c43ee400779843fb8c297257e3">   41</a></span>&#160;<span class="keyword">inline</span> std::ostream &amp; <a class="code" href="namespacecutlass_1_1detail.html#a5b1d94c43ee400779843fb8c297257e3">TensorView_WriteLeastSignificantRank</a>(</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;  std::ostream&amp; out, </div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a> <span class="keyword">const</span>&amp; view,</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;Layout::kRank&gt;</a> <span class="keyword">const</span> &amp;start_coord,</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keywordtype">int</span> rank,</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  std::streamsize width) {</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> idx = 0; idx &lt; view.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>(rank); ++idx) {</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;Layout::kRank&gt;</a> coord(start_coord);</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    coord[rank] = idx;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    <span class="keywordflow">if</span> (idx) {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;      out.width(0);</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;      out &lt;&lt; <span class="stringliteral">&quot;, &quot;</span>;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    }</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    <span class="keywordflow">if</span> (idx || coord) {</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;      out.width(width);</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    }</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    out &lt;&lt; ScalarIO&lt;Element&gt;(view.<a class="code" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">at</a>(coord));</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  }</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keywordflow">return</span> out;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;}</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="keyword">typename</span> Layout</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;&gt;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1detail.html#aa643d9608b71a6eedb7dc48d838c9b52">   71</a></span>&#160;<span class="keyword">inline</span> std::ostream &amp; <a class="code" href="namespacecutlass_1_1detail.html#aa643d9608b71a6eedb7dc48d838c9b52">TensorView_WriteRank</a>(</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  std::ostream&amp; out, </div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a> <span class="keyword">const</span>&amp; view,</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;Layout::kRank&gt;</a> <span class="keyword">const</span> &amp;start_coord,</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keywordtype">int</span> rank,</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  std::streamsize width) {</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="comment">// If called on the least significant rank, write the result as a row</span></div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  <span class="keywordflow">if</span> (rank + 1 == Layout::kRank) {</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="namespacecutlass_1_1detail.html#a5b1d94c43ee400779843fb8c297257e3">TensorView_WriteLeastSignificantRank</a>(out, view, start_coord, rank, width);</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  }</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <span class="comment">// Otherwise, write a sequence of rows and newlines</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> idx = 0; idx &lt; view.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>(rank); ++idx) {</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;Layout::kRank&gt;</a> coord(start_coord);</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    coord[rank] = idx;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    <span class="keywordflow">if</span> (rank + 2 == Layout::kRank) {</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      <span class="comment">// Write least significant ranks asa matrix with rows delimited by &quot;;\n&quot;</span></div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;      out &lt;&lt; (idx ? <span class="stringliteral">&quot;;\n&quot;</span> : <span class="stringliteral">&quot;&quot;</span>);</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;      <a class="code" href="namespacecutlass_1_1detail.html#a5b1d94c43ee400779843fb8c297257e3">TensorView_WriteLeastSignificantRank</a>(out, view, coord, rank + 1, width);</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    }</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <span class="keywordflow">else</span> {</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;      <span class="comment">// Higher ranks are separated by newlines</span></div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;      out &lt;&lt; (idx ? <span class="stringliteral">&quot;\n&quot;</span> : <span class="stringliteral">&quot;&quot;</span>);</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;      <a class="code" href="namespacecutlass_1_1detail.html#aa643d9608b71a6eedb7dc48d838c9b52">TensorView_WriteRank</a>(out, view, coord, rank + 1, width);</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    }</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  }</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="keywordflow">return</span> out;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;}</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;} <span class="comment">// namespace detail</span></div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  <span class="keyword">typename</span> Layout</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;&gt;</div><div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="namespacecutlass.html#ab6898de9565b78dc1c446901b899208b">  113</a></span>&#160;<span class="keyword">inline</span> std::ostream&amp; <a class="code" href="namespacecutlass.html#ab6898de9565b78dc1c446901b899208b">TensorViewWrite</a>(</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  std::ostream&amp; out, </div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a> <span class="keyword">const</span>&amp; view) {</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <span class="comment">// Prints a TensorView according to the following conventions:</span></div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  <span class="comment">//   - least significant rank is printed as rows separated by &quot;;\n&quot;</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <span class="comment">//   - all greater ranks are delimited with newlines</span></div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <span class="comment">// The result is effectively a whitespace-delimited series of 2D matrices.</span></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="namespacecutlass_1_1detail.html#aa643d9608b71a6eedb7dc48d838c9b52">detail::TensorView_WriteRank</a>(out, view, <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;Layout::kRank&gt;</a>(), 0, out.width());</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;}</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <span class="keyword">typename</span> Layout</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;&gt;</div><div class="line"><a name="l00131"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a806ce4c816ee2c20ed853715f74c092c">  131</a></span>&#160;<span class="keyword">inline</span> std::ostream&amp; <a class="code" href="namespacecutlass.html#addd443fc82f2acf867a61acff6c1cd29">operator&lt;&lt;</a>(</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  std::ostream&amp; out, </div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a> <span class="keyword">const</span>&amp; view) {</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <span class="comment">// Prints a TensorView according to the following conventions:</span></div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <span class="comment">//   - least significant rank is printed as rows separated by &quot;;\n&quot;</span></div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <span class="comment">//   - all greater ranks are delimited with newlines</span></div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  <span class="comment">// The result is effectively a whitespace-delimited series of 2D matrices.</span></div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="namespacecutlass.html#ab6898de9565b78dc1c446901b899208b">TensorViewWrite</a>(out, view);</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;}</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="core__io_8h_html"><div class="ttname"><a href="core__io_8h.html">core_io.h</a></div><div class="ttdoc">Helpers for printing cutlass/core objects. </div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a7d3914dd5042c9c40be9e21a7b4e9ece"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">cutlass::TensorView::extent</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord const &amp; extent() const </div><div class="ttdoc">Returns the extent of the view (the size along each logical dimension). </div><div class="ttdef"><b>Definition:</b> tensor_view.h:167</div></div>
<div class="ttc" id="tensor__view_8h_html"><div class="ttname"><a href="tensor__view_8h.html">tensor_view.h</a></div><div class="ttdoc">Defines a structure containing strides and a pointer to tensor data. </div></div>
<div class="ttc" id="namespacecutlass_html_ab6898de9565b78dc1c446901b899208b"><div class="ttname"><a href="namespacecutlass.html#ab6898de9565b78dc1c446901b899208b">cutlass::TensorViewWrite</a></div><div class="ttdeci">std::ostream &amp; TensorViewWrite(std::ostream &amp;out, TensorView&lt; Element, Layout &gt; const &amp;view)</div><div class="ttdoc">Prints human-readable representation of a TensorView to an ostream. </div><div class="ttdef"><b>Definition:</b> tensor_view_io.h:113</div></div>
<div class="ttc" id="namespacecutlass_1_1detail_html_a5b1d94c43ee400779843fb8c297257e3"><div class="ttname"><a href="namespacecutlass_1_1detail.html#a5b1d94c43ee400779843fb8c297257e3">cutlass::detail::TensorView_WriteLeastSignificantRank</a></div><div class="ttdeci">std::ostream &amp; TensorView_WriteLeastSignificantRank(std::ostream &amp;out, TensorView&lt; Element, Layout &gt; const &amp;view, Coord&lt; Layout::kRank &gt; const &amp;start_coord, int rank, std::streamsize width)</div><div class="ttdoc">Helper to write the least significant rank of a TensorView. </div><div class="ttdef"><b>Definition:</b> tensor_view_io.h:41</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html"><div class="ttname"><a href="classcutlass_1_1TensorView.html">cutlass::TensorView&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord&lt; Layout::kRank &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a8758907a1c9b1fcd00e7ece626d03b76"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">cutlass::TensorRef::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference at(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns a reference to the element at a given Coord. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:307</div></div>
<div class="ttc" id="namespacecutlass_html_addd443fc82f2acf867a61acff6c1cd29"><div class="ttname"><a href="namespacecutlass.html#addd443fc82f2acf867a61acff6c1cd29">cutlass::operator&lt;&lt;</a></div><div class="ttdeci">std::ostream &amp; operator&lt;&lt;(std::ostream &amp;out, complex&lt; T &gt; const &amp;z)</div><div class="ttdef"><b>Definition:</b> complex.h:291</div></div>
<div class="ttc" id="namespacecutlass_1_1detail_html_aa643d9608b71a6eedb7dc48d838c9b52"><div class="ttname"><a href="namespacecutlass_1_1detail.html#aa643d9608b71a6eedb7dc48d838c9b52">cutlass::detail::TensorView_WriteRank</a></div><div class="ttdeci">std::ostream &amp; TensorView_WriteRank(std::ostream &amp;out, TensorView&lt; Element, Layout &gt; const &amp;view, Coord&lt; Layout::kRank &gt; const &amp;start_coord, int rank, std::streamsize width)</div><div class="ttdoc">Helper to write a rank of a TensorView. </div><div class="ttdef"><b>Definition:</b> tensor_view_io.h:71</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
