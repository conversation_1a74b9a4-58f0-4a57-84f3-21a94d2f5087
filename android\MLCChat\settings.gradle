pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}
rootProject.name = "MLCChat"
include ':app'
include ':mlc4j'
project(':mlc4j').projectDir = file('dist/lib/mlc4j')
include ':mlcengineexample'
