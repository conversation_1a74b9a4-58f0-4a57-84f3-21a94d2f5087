//
// MessagePack for C++ simple buffer implementation
//
// Copyright (C) 2008-2016 FURUHASH<PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_SBUFFER_HPP
#define MSGPACK_SBUFFER_HPP

#include "msgpack/sbuffer_decl.hpp"

#include "msgpack/v1/sbuffer.hpp"

#endif // MSGPACK_SBUFFER_HPP
