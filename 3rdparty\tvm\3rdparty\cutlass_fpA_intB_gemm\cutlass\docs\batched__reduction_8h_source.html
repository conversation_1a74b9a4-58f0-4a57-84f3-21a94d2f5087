<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: batched_reduction.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ac488927e63b76ba9cb3ad9c317bbde9.html">reduction</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">batched_reduction.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="batched__reduction_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">* Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">* Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">* provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">*     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">*       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">*     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">*       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">*       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">*     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">*       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">*       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">* FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">* FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment">* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment">* STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">* OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment">**************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#if !defined(__CUDACC_RTC__)</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &lt;cuda.h&gt;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;cutlass/util/platform.h&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;cutlass/fragment.h&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reduction.html">   40</a></span>&#160;<span class="keyword">namespace </span>reduction {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> batched_reduction_&gt;</div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reduction.html#a9665e8f438a7b290d6e2eb640d93045f">   45</a></span>&#160;__global__ <a class="code" href="namespacecutlass_1_1reduction.html#a9665e8f438a7b290d6e2eb640d93045f">__launch_bounds__</a>(batched_reduction_::Traits::kThreads, 1) void batched_reduction_kernel(typename batched_reduction_::Params params) {</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  <span class="comment">// Construct the batched_reduction object</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  batched_reduction_ batched_reduction(params);</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  batched_reduction.run();</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;}</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> BatchedReductionTraits_&gt;</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html">   52</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html">BatchedReduction</a> {</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#ae0c48344c7457f17429e6ae7a76dba37">   54</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html">BatchedReduction&lt;BatchedReductionTraits_&gt;</a> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#ae0c48344c7457f17429e6ae7a76dba37">This_</a>;</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#aa50195fcf53f6d69ccfa37f44a524a99">   56</a></span>&#160;  <span class="keyword">typedef</span> BatchedReductionTraits_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#aa50195fcf53f6d69ccfa37f44a524a99">Traits</a>;</div><div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a213c6812458c008435ed3ea710fe2454">   58</a></span>&#160;  <span class="keyword">typedef</span> <span class="keyword">typename</span> Traits::Params <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a213c6812458c008435ed3ea710fe2454">Params</a>;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a30605f35d51e4364fe80edb80eac5e80">   60</a></span>&#160;  <span class="keyword">typedef</span> <span class="keyword">typename</span> Traits::Functor <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a30605f35d51e4364fe80edb80eac5e80">Functor</a>;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9d76da3dcf4d8ec0cfeb2134f73ea22b">   63</a></span>&#160;  CUTLASS_DEVICE <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9d76da3dcf4d8ec0cfeb2134f73ea22b">BatchedReduction</a>(Params <span class="keyword">const</span> &amp;params_)</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      : <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>(params_), <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a0f6b38c1b3a5800e6f29d9a2c6c1928d">functor</a>(params_.functorParams) {}</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  </div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a4bfcdfd8f2edeb4fc7081443584d599b">   68</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a4bfcdfd8f2edeb4fc7081443584d599b">run</a>() {</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="preprocessor">#if (__CUDA_ARCH__ &gt;= 600)</span></div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    <span class="comment">// Swizzle the IDs of the block </span></div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <span class="keyword">typename</span> Traits::BlockSwizzle block_swizzle;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;3&gt;</a> threadblock_offset =</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;      block_swizzle.get_threadblock_offset(make_Coord_from_shape&lt;Traits::SubTile&gt;());</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    <span class="keywordtype">int</span> subTileSize = gridDim.x * Traits::SubTile::kW;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    <span class="keywordtype">int</span> tileSize = <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>.problem_size[1] * <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>.problem_size[2];</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    <span class="keywordtype">int</span> subTileOffset = threadblock_offset[2] + threadIdx.x * Traits::ThreadShape::kW;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    <span class="keywordtype">int</span> subTileBase = 0;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <span class="keyword">typename</span> Traits::ScalarA inRegs[Traits::maxInReg];</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    <span class="keyword">typename</span> Traits::ScalarAccum AccumRegs[Traits::maxOutReg];</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> subTile = 0; subTile &lt; tileSize; subTile += subTileSize) {</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;      <span class="keywordtype">int</span> tileOffset = subTileBase + subTileOffset;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;      <span class="comment">// Init AccumRegs</span></div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; Traits::ThreadShape::kW; i++)</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        AccumRegs[i] = static_cast&lt;typename Traits::ScalarAccum&gt;(0.0f);</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      <span class="comment">// Fetch c0</span></div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;      <span class="keyword">typename</span> Traits::ScalarAccum c0[Traits::ThreadShape::kW];</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i&lt; Traits::ThreadShape::kW; i++)</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        c0[i] = static_cast&lt;typename Traits::ScalarAccum&gt;(<a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>.d_c[tileOffset + i]);</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;      <span class="comment">// Fetch partial sums from A</span></div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> s = 0; s &lt; Traits::ReductionSize; s++) {</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        <span class="keywordtype">int</span> inRegOffset = s * Traits::ThreadShape::kW;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        <span class="keywordtype">int</span> dOffset = (s * tileSize) + tileOffset;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i&lt; Traits::ThreadShape::kW; i++) {</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;          inRegs[inRegOffset + i] = <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>.d_a[dOffset + i];</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        }</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;      }</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;      <span class="comment">// Accumulate</span></div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> s = 0; s &lt; Traits::ReductionSize; s++) {</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        <span class="keywordtype">int</span> inRegOffset = s * Traits::ThreadShape::kW;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; Traits::ThreadShape::kW; i++) {</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;          <span class="comment">//AccumRegs[i] = cuFma(params.alpha, inRegs[inRegOffset + i], AccumRegs[i]);</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;          <span class="comment">//AccumRegs[i] = params.alpha * inRegs[inRegOffset + i] + AccumRegs[i];</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;          AccumRegs[i] = <span class="keyword">static_cast&lt;</span>typename Traits::ScalarAccum<span class="keyword">&gt;</span>(inRegs[inRegOffset + i]) + AccumRegs[i];</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        }</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;      }</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;      <span class="comment">// calling functor</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;      functor_caller&lt;Traits::ThreadShapeMultiple2&gt;(AccumRegs, c0, AccumRegs);</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;      <span class="comment">// Store AccumRegs to D</span></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; Traits::ThreadShape::kW; i++) {</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>.d_d[tileOffset + i] = <span class="keyword">static_cast&lt;</span>typename Traits::ScalarD<span class="keyword">&gt;</span>(AccumRegs[i]);</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;      }</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      <span class="comment">// Advance sub-tile pointer</span></div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;      subTileBase += subTileSize;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    } <span class="comment">// end for loop</span></div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="preprocessor">#endif //#if (__CUDA_ARCH__ &gt;= 600)</span></div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  }</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  <span class="keyword">template</span>&lt;<span class="keywordtype">bool</span> ThreadShapeMultiple2&gt;</div><div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a7c1d173cbe3abd93bd7bd4c4bf0e0d26">  134</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a7c1d173cbe3abd93bd7bd4c4bf0e0d26">functor_caller</a>(<span class="keyword">typename</span> Traits::ScalarAccum <span class="keyword">const</span> *accum, <span class="keyword">typename</span> Traits::ScalarAccum <span class="keyword">const</span> *old, <span class="keyword">typename</span> Traits::ScalarAccum *output) {</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    <span class="keywordflow">if</span> (ThreadShapeMultiple2 == <span class="keyword">true</span>) {</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; Traits::ThreadShape::kW / 2; i++) {</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a0f6b38c1b3a5800e6f29d9a2c6c1928d">functor</a>.template evaluate&lt;typename Traits::ScalarAccum, typename Traits::ScalarAccum, 2&gt;(&amp;accum[2 * i], &amp;old[2 * i], &amp;output[2 * i]);</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;      }</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    }</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="keywordflow">else</span> {</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;<span class="preprocessor">#pragma unroll</span></div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; Traits::ThreadShape::kW; i++) {</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a0f6b38c1b3a5800e6f29d9a2c6c1928d">functor</a>.template evaluate&lt;typename Traits::ScalarAccum, typename Traits::ScalarAccum, 1&gt;(&amp;accum[i], &amp;old[i], &amp;output[i]);</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;      }</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    }</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  }</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  <span class="comment">// Static function members</span></div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;<span class="preprocessor">#if !defined(__CUDACC_RTC__)</span></div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <span class="keyword">static</span> __host__ cudaError_t <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#ab059393ac467ac365bd0b45c200befdf">launch</a>(Params <span class="keyword">const</span>&amp; <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>,</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    cudaStream_t stream = cudaStreamDefault) {</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <span class="comment">// Setup the grid. </span></div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <span class="keyword">typename</span> Traits::BlockSwizzle block_swizzle;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    dim3 grid = block_swizzle.get_grid_layout(params.problem_size,</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;                                              make_Coord_from_shape&lt;typename Traits::OutputTile&gt;());</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    </div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    dim3 block;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    block.x = Traits::kThreads;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    batched_reduction_kernel&lt;This_&gt;&lt;&lt;&lt;grid, block, 0, stream&gt;&gt;&gt;(<a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>);</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keywordflow">return</span> cudaGetLastError();</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  }</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">  173</a></span>&#160;  Params <span class="keyword">const</span>&amp; <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">params</a>;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  <span class="comment">// The functor.</span></div><div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a0f6b38c1b3a5800e6f29d9a2c6c1928d">  175</a></span>&#160;  Functor <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html#a0f6b38c1b3a5800e6f29d9a2c6c1928d">functor</a>;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;};</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;} <span class="comment">// namespace reduction</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_a9c9d8378c735597f39927c6ab32519ef"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9c9d8378c735597f39927c6ab32519ef">cutlass::reduction::BatchedReduction::params</a></div><div class="ttdeci">Params const &amp; params</div><div class="ttdoc">The params. </div><div class="ttdef"><b>Definition:</b> batched_reduction.h:173</div></div>
<div class="ttc" id="namespacecutlass_1_1reduction_html_a9665e8f438a7b290d6e2eb640d93045f"><div class="ttname"><a href="namespacecutlass_1_1reduction.html#a9665e8f438a7b290d6e2eb640d93045f">cutlass::reduction::__launch_bounds__</a></div><div class="ttdeci">__global__ __launch_bounds__(batched_reduction_::Traits::kThreads, 1) void batched_reduction_kernel(typename batched_reduction_</div><div class="ttdef"><b>Definition:</b> batched_reduction.h:45</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_a4bfcdfd8f2edeb4fc7081443584d599b"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#a4bfcdfd8f2edeb4fc7081443584d599b">cutlass::reduction::BatchedReduction::run</a></div><div class="ttdeci">CUTLASS_DEVICE void run()</div><div class="ttdef"><b>Definition:</b> batched_reduction.h:68</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_ae0c48344c7457f17429e6ae7a76dba37"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#ae0c48344c7457f17429e6ae7a76dba37">cutlass::reduction::BatchedReduction::This_</a></div><div class="ttdeci">BatchedReduction&lt; BatchedReductionTraits_ &gt; This_</div><div class="ttdoc">This class. </div><div class="ttdef"><b>Definition:</b> batched_reduction.h:54</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_a0f6b38c1b3a5800e6f29d9a2c6c1928d"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#a0f6b38c1b3a5800e6f29d9a2c6c1928d">cutlass::reduction::BatchedReduction::functor</a></div><div class="ttdeci">Functor functor</div><div class="ttdef"><b>Definition:</b> batched_reduction.h:175</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html">cutlass::reduction::BatchedReduction</a></div><div class="ttdef"><b>Definition:</b> batched_reduction.h:52</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 3 &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_a9d76da3dcf4d8ec0cfeb2134f73ea22b"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#a9d76da3dcf4d8ec0cfeb2134f73ea22b">cutlass::reduction::BatchedReduction::BatchedReduction</a></div><div class="ttdeci">CUTLASS_DEVICE BatchedReduction(Params const &amp;params_)</div><div class="ttdoc">ctor </div><div class="ttdef"><b>Definition:</b> batched_reduction.h:63</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_a213c6812458c008435ed3ea710fe2454"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#a213c6812458c008435ed3ea710fe2454">cutlass::reduction::BatchedReduction::Params</a></div><div class="ttdeci">Traits::Params Params</div><div class="ttdoc">Params. </div><div class="ttdef"><b>Definition:</b> batched_reduction.h:58</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_a7c1d173cbe3abd93bd7bd4c4bf0e0d26"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#a7c1d173cbe3abd93bd7bd4c4bf0e0d26">cutlass::reduction::BatchedReduction::functor_caller</a></div><div class="ttdeci">CUTLASS_DEVICE void functor_caller(typename Traits::ScalarAccum const *accum, typename Traits::ScalarAccum const *old, typename Traits::ScalarAccum *output)</div><div class="ttdef"><b>Definition:</b> batched_reduction.h:134</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_a30605f35d51e4364fe80edb80eac5e80"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#a30605f35d51e4364fe80edb80eac5e80">cutlass::reduction::BatchedReduction::Functor</a></div><div class="ttdeci">Traits::Functor Functor</div><div class="ttdoc">functor </div><div class="ttdef"><b>Definition:</b> batched_reduction.h:60</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_aa50195fcf53f6d69ccfa37f44a524a99"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#aa50195fcf53f6d69ccfa37f44a524a99">cutlass::reduction::BatchedReduction::Traits</a></div><div class="ttdeci">BatchedReductionTraits_ Traits</div><div class="ttdoc">The traits. </div><div class="ttdef"><b>Definition:</b> batched_reduction.h:56</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html_ab059393ac467ac365bd0b45c200befdf"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html#ab059393ac467ac365bd0b45c200befdf">cutlass::reduction::BatchedReduction::launch</a></div><div class="ttdeci">static __host__ cudaError_t launch(Params const &amp;params, cudaStream_t stream=cudaStreamDefault)</div><div class="ttdoc">Launch the kernel. </div><div class="ttdef"><b>Definition:</b> batched_reduction.h:154</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
