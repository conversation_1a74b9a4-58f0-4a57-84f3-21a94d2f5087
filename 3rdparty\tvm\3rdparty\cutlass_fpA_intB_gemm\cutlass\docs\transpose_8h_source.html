<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: transpose.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_568e97a0eb81cc0d3daf98cef30c9135.html">transform</a></li><li class="navelem"><a class="el" href="dir_f9f54b1d82c28725d6670ba47204b309.html">thread</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">transpose.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="transpose_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">namespace </span>transform {</div><div class="line"><a name="l00032"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1transform_1_1thread.html">   32</a></span>&#160;<span class="keyword">namespace </span>thread {</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;  <span class="keywordtype">int</span> ElementCount, </div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;  <span class="keyword">typename</span> TransposeShape, </div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;  <span class="keyword">typename</span> Element</div><div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">   39</a></span>&#160;&gt; <span class="keyword">class </span><a class="code" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">Transpose</a>;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> ElementCount_&gt;</div><div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html">   43</a></span>&#160;<span class="keyword">struct </span><a class="code" href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">Transpose</a>&lt;ElementCount_, layout::PitchLinearShape&lt;4,4&gt; , int8_t&gt; {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a5f267ce8d5c33de3d5b9eded4b4ee995">   45</a></span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">int</span> kElementCount = ElementCount_;</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a438b633a80483f6dc58f6ab55976fd57">   46</a></span>&#160;    <span class="keyword">using</span> TransposeShape = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape&lt;4,4&gt;</a>;</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a732186d0cebf8c7ee2e1b0d6e7aa45dc">   47</a></span>&#160;    <span class="keyword">using</span> <a class="code" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a732186d0cebf8c7ee2e1b0d6e7aa45dc">Element</a> = int8_t;</div><div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">   48</a></span>&#160;    <span class="keyword">using</span> <a class="code" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a> = cutlass::Array&lt;Element, kElementCount&gt;;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(!(kElementCount % TransposeShape::kCount), <span class="stringliteral">&quot;Shape needs to be multiple of 16 elements to do a 4x4 transpose&quot;</span>);</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    CUTLASS_DEVICE </div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ac2b77ac69bcb01ae623366cc020d6ecd">   53</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ac2b77ac69bcb01ae623366cc020d6ecd">transform</a>(<a class="code" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a>&amp; dst, <a class="code" href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">Fragment</a>&amp; src) {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    <span class="comment">// Expose src/dst as int arrays.</span></div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    <span class="keywordtype">int</span>* src_int = <span class="keyword">reinterpret_cast&lt;</span><span class="keywordtype">int</span>*<span class="keyword">&gt;</span>(&amp;src);</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    <span class="keywordtype">int</span>* dst_int = <span class="keyword">reinterpret_cast&lt;</span><span class="keywordtype">int</span>*<span class="keyword">&gt;</span>(&amp;dst);</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kElementCount / TransposeShape::kCount; i++){</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  </div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;      <span class="keywordtype">int</span> <span class="keyword">const</span> i0 = 4 * i + 0;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;      <span class="keywordtype">int</span> <span class="keyword">const</span> i1 = 4 * i + 1;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      <span class="keywordtype">int</span> <span class="keyword">const</span> i2 = 4 * i + 2;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      <span class="keywordtype">int</span> <span class="keyword">const</span> i3 = 4 * i + 3;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;      <span class="keywordtype">int</span> a0 = src_int[i0];</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;      <span class="keywordtype">int</span> a1 = src_int[i1];</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;      <span class="keywordtype">int</span> a2 = src_int[i2];</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;      <span class="keywordtype">int</span> a3 = src_int[i3];</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;      <span class="keywordtype">int</span> b0, b1, b2, b3, c0;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0040;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b0) : <span class="stringliteral">&quot;r&quot;</span>(a0), <span class="stringliteral">&quot;r&quot;</span>(a1));</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0040;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(c0) : <span class="stringliteral">&quot;r&quot;</span>(a2), <span class="stringliteral">&quot;r&quot;</span>(a3));</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x5410;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b0) : <span class="stringliteral">&quot;r&quot;</span>(b0), <span class="stringliteral">&quot;r&quot;</span>(c0));</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0051;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b1) : <span class="stringliteral">&quot;r&quot;</span>(a0), <span class="stringliteral">&quot;r&quot;</span>(a1));</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0051;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(c0) : <span class="stringliteral">&quot;r&quot;</span>(a2), <span class="stringliteral">&quot;r&quot;</span>(a3));</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x5410;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b1) : <span class="stringliteral">&quot;r&quot;</span>(b1), <span class="stringliteral">&quot;r&quot;</span>(c0));</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0062;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b2) : <span class="stringliteral">&quot;r&quot;</span>(a0), <span class="stringliteral">&quot;r&quot;</span>(a1));</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0062;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(c0) : <span class="stringliteral">&quot;r&quot;</span>(a2), <span class="stringliteral">&quot;r&quot;</span>(a3));</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x5410;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b2) : <span class="stringliteral">&quot;r&quot;</span>(b2), <span class="stringliteral">&quot;r&quot;</span>(c0));</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0073;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b3) : <span class="stringliteral">&quot;r&quot;</span>(a0), <span class="stringliteral">&quot;r&quot;</span>(a1));</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x0073;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(c0) : <span class="stringliteral">&quot;r&quot;</span>(a2), <span class="stringliteral">&quot;r&quot;</span>(a3));</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span>(<span class="stringliteral">&quot;prmt.b32 %0, %1, %2, 0x5410;&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(b3) : <span class="stringliteral">&quot;r&quot;</span>(b3), <span class="stringliteral">&quot;r&quot;</span>(c0));</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;      dst_int[i0] = b0;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      dst_int[i1] = b1;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;      dst_int[i2] = b2;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;      dst_int[i3] = b3;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    }</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  }</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;};</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;}  <span class="comment">// namespace thread</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;}  <span class="comment">// namespace layout</span></div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;}  <span class="comment">// namespace cutlass</span></div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81_html_ab368c16371c6c8e5384fcbc9f4412ab6"><div class="ttname"><a href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ab368c16371c6c8e5384fcbc9f4412ab6">cutlass::transform::thread::Transpose&lt; ElementCount_, layout::PitchLinearShape&lt; 4, 4 &gt;, int8_t &gt;::Fragment</a></div><div class="ttdeci">cutlass::Array&lt; Element, kElementCount &gt; Fragment</div><div class="ttdef"><b>Definition:</b> transpose.h:48</div></div>
<div class="ttc" id="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81_html_ac2b77ac69bcb01ae623366cc020d6ecd"><div class="ttname"><a href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#ac2b77ac69bcb01ae623366cc020d6ecd">cutlass::transform::thread::Transpose&lt; ElementCount_, layout::PitchLinearShape&lt; 4, 4 &gt;, int8_t &gt;::transform</a></div><div class="ttdeci">CUTLASS_DEVICE void transform(Fragment &amp;dst, Fragment &amp;src)</div><div class="ttdef"><b>Definition:</b> transpose.h:53</div></div>
<div class="ttc" id="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81_html_a732186d0cebf8c7ee2e1b0d6e7aa45dc"><div class="ttname"><a href="structcutlass_1_1transform_1_1thread_1_1Transpose_3_01ElementCount___00_01layout_1_1PitchLinearS99f8e05faf0bb5ed48a0154afe740d81.html#a732186d0cebf8c7ee2e1b0d6e7aa45dc">cutlass::transform::thread::Transpose&lt; ElementCount_, layout::PitchLinearShape&lt; 4, 4 &gt;, int8_t &gt;::Element</a></div><div class="ttdeci">int8_t Element</div><div class="ttdef"><b>Definition:</b> transpose.h:47</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a></div><div class="ttdoc">Template defining a shape used by pitch-linear operators. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:43</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1thread_1_1Transpose_html"><div class="ttname"><a href="classcutlass_1_1transform_1_1thread_1_1Transpose.html">cutlass::transform::thread::Transpose</a></div><div class="ttdoc">Transforms a fragment by doing a transpose. </div><div class="ttdef"><b>Definition:</b> transpose.h:39</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
