<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: predicated_tile_iterator.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_568e97a0eb81cc0d3daf98cef30c9135.html">transform</a></li><li class="navelem"><a class="el" href="dir_5a68e39c181f2defa4dd959f7500739b.html">threadblock</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">transform/threadblock/predicated_tile_iterator.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="transform_2threadblock_2predicated__tile__iterator_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="memory_8h.html">cutlass/arch/memory.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="predicated__tile__access__iterator_8h.html">cutlass/transform/threadblock/predicated_tile_access_iterator.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">namespace </span>transform {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">namespace </span>threadblock {</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="comment">// template &lt;typename Iterator&gt;</span></div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="comment">// __global__ void kernel(</span></div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="comment">//   typename Iterator::Params params, </span></div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="comment">//   typename Iterator::Element *ptr,</span></div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="comment">//   TensorCoord extent) {</span></div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;<span class="comment">//   typename Iterator::Fragment fragment;</span></div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="comment">//   TensorCoord threadblock_offset(0, 0);</span></div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="comment">//   Iterator iter(params, ptr, extent, threadIdx.x, threadblock_offsets);</span></div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="comment">//   fragment = *iter;        // load &quot;residue&quot; tile first</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="comment">//   ++iter;                  // advance to first &quot;steady state&quot; tile and update internal masks</span></div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;<span class="comment">//   #pragma unroll</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="comment">//   for (int i = Remaining - 1; i &gt;= 0; --i) {</span></div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="comment">//     f(fragment);</span></div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="comment">//     if (!i) {</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="comment">//       iter.clear_mask();   // light-weight operation to clear masks - subsequent loads become NO-OPs.</span></div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="comment">//     }</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="comment">//  </span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="comment">//     fragment = *iter;      // load tile during &quot;steady state&quot; phase</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="comment">//     ++iter;                // advance to next tile - lightweight due to steady-state masks</span></div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="comment">//   }</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="comment">// }</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="comment">// void host(TensorView&lt;Element, 2, layout::PitchLinear&gt; view) {</span></div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="comment">//   using Iterator = transform::threadblock::PredicatedTileIterator;</span></div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="comment">//   typename Iterator::Params params(view.layout());</span></div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="comment">//   kernel&lt;Iterator&gt;(params, view.data());</span></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="comment">// }</span></div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="comment"></span><span class="keyword">template</span> &lt;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <span class="keyword">typename</span> Shape,</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  <span class="keyword">typename</span> Layout,</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <span class="keywordtype">int</span> AdvanceRank,</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  <span class="keyword">typename</span> ThreadMap,</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="keywordtype">int</span> AccessSize = ThreadMap::kElementsPerAccess</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;&gt;</div><div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">  133</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Shape_, <span class="keyword">typename</span> Element_, <span class="keywordtype">int</span> AdvanceRank,</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;          <span class="keyword">typename</span> ThreadMap_, <span class="keywordtype">int</span> AccessSize&gt;</div><div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html">  146</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;Shape_, Element_, layout::PitchLinear, AdvanceRank,</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;                             ThreadMap_, AccessSize&gt; {</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;      AdvanceRank == 0 || AdvanceRank == 1,</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;      <span class="stringliteral">&quot;Specialization for pitch-linear iterator may along advance along the &quot;</span></div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;      <span class="stringliteral">&quot;contiguous(rank=0) or strided(rank=1) dimension.&quot;</span>);</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a1faa65b2559fa3f7ca89cd171d656135">  154</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a1faa65b2559fa3f7ca89cd171d656135">Shape</a> = Shape_;</div><div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6bb4a258adfe8ddc8c2029fe2c483068">  155</a></span>&#160;  <span class="keyword">using</span> Element = Element_;</div><div class="line"><a name="l00156"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a274c38e6b7a428643379438969243d2b">  156</a></span>&#160;  <span class="keyword">using</span> Layout = <a class="code" href="classcutlass_1_1layout_1_1PitchLinear.html">layout::PitchLinear</a>;</div><div class="line"><a name="l00157"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a5d4c4f757ba6db23fbe7227d1c41869a">  157</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAdvanceRank = AdvanceRank;</div><div class="line"><a name="l00158"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a588133c47d960763cf9136431900c37f">  158</a></span>&#160;  <span class="keyword">using</span> ThreadMap = ThreadMap_;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ac68f091bc3ef944e3476c838f2225157">  160</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ac68f091bc3ef944e3476c838f2225157">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1PitchLinear.html#a9d1dfd7b6d3b2b651009dcba8f5fd5cd">Layout::Index</a>;</div><div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6994e38394aac156a8bcf006fe0a86dc">  161</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6994e38394aac156a8bcf006fe0a86dc">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1PitchLinear.html#a1a4b31740e77b3c03925f507650978ea">Layout::LongIndex</a>;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div><div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aaafb0bee5f9890a5b54ec9f226b00492">  163</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00164"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a2ceec9d4f759c02dbfe8e97f976edf0b">  164</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00165"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#acd68921055f7b559580922bdaa86232e">  165</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#acd68921055f7b559580922bdaa86232e">TensorCoord</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">Layout::TensorCoord</a>;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#af79b4a0ff3191a3dbdd01afd164fbc68">  167</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#af79b4a0ff3191a3dbdd01afd164fbc68">Pointer</a> = Element *;</div><div class="line"><a name="l00168"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aec0b17e3c03da63a798a83ea086b380c">  168</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aec0b17e3c03da63a798a83ea086b380c">NonConstPointer</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">platform::remove_const&lt;Element&gt;::type</a> *;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div><div class="line"><a name="l00171"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aa2a62bf046f53db07bc2c2d6bf35ab64">  171</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray&lt;Element, AccessSize, (AccessSize * sizeof_bits&lt;Element&gt;::value</a> / 8)&gt;;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator.html">TileAccessIterator</a> =</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator.html">PredicatedTileAccessIterator</a>&lt;<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a1faa65b2559fa3f7ca89cd171d656135">Shape</a>, Element, Layout, kAdvanceRank,</div><div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a277ef74b3ca9602f1f33a381fe342555">  176</a></span>&#160;                                   ThreadMap, <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a>&gt;;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#af99ae79be70bd5bf0c814759ca40c95d">  178</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessesPerVector = TileAccessIterator::kAccessesPerVector;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a8e3ff6897ff18b510d459052e6aa5648">Fragment</a> = cutlass::Array&lt;Element, ThreadMap::Iterations::kCount *</div><div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a8e3ff6897ff18b510d459052e6aa5648">  182</a></span>&#160;                                               ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div><div class="line"><a name="l00185"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6b9ef24ac3350b33f76a2c580e31a579">  185</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6b9ef24ac3350b33f76a2c580e31a579">Mask</a> = <span class="keyword">typename</span> TileAccessIterator::Mask;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html">  188</a></span>&#160;  <span class="keyword">class </span>Params {</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;   <span class="keyword">public</span>:</div><div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#ae0f61aca69c0d05ec21684cd08d040fc">  190</a></span>&#160;    <span class="keyword">friend</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#ae0f61aca69c0d05ec21684cd08d040fc">PredicatedTileIterator</a>;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;   <span class="keyword">private</span>:</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    <span class="keyword">typename</span> TileAccessIterator::Params params_;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;   <span class="keyword">public</span>:</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#a45e7ac8084883fdf4d84d71afddf45d6">  199</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#a45e7ac8084883fdf4d84d71afddf45d6">Params</a>(Layout <span class="keyword">const</span> &amp;layout) : params_(layout) { }</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;    </div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00202"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#a3f17d7ab9151f7204f34da4642b7b790">  202</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#a3f17d7ab9151f7204f34da4642b7b790">Params</a>() { }</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;  };</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;  <span class="keyword">using</span> BytePointer = <span class="keywordtype">char</span> *;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator.html">TileAccessIterator</a> address_iterator_;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00221"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a562259e56e4981326c16c96b00b67987">  221</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a562259e56e4981326c16c96b00b67987">PredicatedTileIterator</a>(</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;      Params <span class="keyword">const</span> &amp;params,</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#af79b4a0ff3191a3dbdd01afd164fbc68">Pointer</a> pointer,</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#acd68921055f7b559580922bdaa86232e">TensorCoord</a> extent,</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;      <span class="keywordtype">int</span> thread_id,</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#acd68921055f7b559580922bdaa86232e">TensorCoord</a> <span class="keyword">const</span> &amp;threadblock_offset)</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;      : address_iterator_(params.params_, pointer, extent, thread_id,</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;                          threadblock_offset) {}</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00237"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a36c915019a92458f22a40fbd0bad8363">  237</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a36c915019a92458f22a40fbd0bad8363">PredicatedTileIterator</a>(</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;      Params <span class="keyword">const</span> &amp;params,  </div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#af79b4a0ff3191a3dbdd01afd164fbc68">Pointer</a> pointer,       </div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#acd68921055f7b559580922bdaa86232e">TensorCoord</a> extent,    </div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;      <span class="keywordtype">int</span> thread_id          </div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;      )</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;      : <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>(params, pointer, extent, thread_id,</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;                               <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(0, 0)) {}</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00248"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a9cc44093405805682cb4e26a4cd00271">  248</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a9cc44093405805682cb4e26a4cd00271">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6994e38394aac156a8bcf006fe0a86dc">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;    address_iterator_.add_pointer_offset(pointer_offset);</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;  }</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00259"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a139515c9d839b495b661d1f7a5a58b48">  259</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> &amp;<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a139515c9d839b495b661d1f7a5a58b48">operator++</a>() {</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    <span class="keywordflow">if</span> (kAdvanceRank)</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;      address_iterator_.add_tile_offset({0, 1});</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <span class="keywordflow">else</span></div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;      address_iterator_.add_tile_offset({1, 0});</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  }</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00275"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aa0670788c8e0ec05f173f6fd5b3e6cc2">  275</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aa0670788c8e0ec05f173f6fd5b3e6cc2">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <span class="keyword">self</span>(*this);</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;    <a class="code" href="namespacecutlass.html#a1c7a9e66ca7b5dc7413ea3b8f349530a">operator++</a>();</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">self</span>;</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  }</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#afd3e189a510aef0ddceb6f0ceb519d88">  283</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#afd3e189a510aef0ddceb6f0ceb519d88">clear_mask</a>() { address_iterator_.clear_mask(); }</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00287"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a9b108452c6733a2de76de611aab8caf6">  287</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a9b108452c6733a2de76de611aab8caf6">enable_mask</a>() { address_iterator_.enable_mask(); }</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00291"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ada91bd8779c8636da59b7328de635c15">  291</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ada91bd8779c8636da59b7328de635c15">set_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6b9ef24ac3350b33f76a2c580e31a579">Mask</a> <span class="keyword">const</span> &amp;mask) { address_iterator_.set_mask(mask); }</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00295"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a7ba9103031ca5d3e09867d057417594d">  295</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a7ba9103031ca5d3e09867d057417594d">get_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6b9ef24ac3350b33f76a2c580e31a579">Mask</a> &amp;mask) { address_iterator_.get_mask(mask); }</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00298"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a78b52c1335ed6e5d355b012da983c05a">  298</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a78b52c1335ed6e5d355b012da983c05a">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a8e3ff6897ff18b510d459052e6aa5648">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ac68f091bc3ef944e3476c838f2225157">Index</a> pointer_offset) {</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;    </div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;    AccessType *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span>AccessType *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> s = 0; s &lt; ThreadMap::Iterations::kStrided; ++s) {</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> c = 0; c &lt; ThreadMap::Iterations::kContiguous; ++c) {</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> v = 0; v &lt; kAccessesPerVector; ++v) {</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;          <span class="keywordtype">int</span> idx = v + kAccessesPerVector * (c + s * ThreadMap::Iterations::kContiguous);</div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;          </div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;          address_iterator_.set_iteration_index(idx);</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;          <span class="keyword">auto</span> ptr = (address_iterator_.get() + pointer_offset);</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;          <span class="keywordflow">if</span> (address_iterator_.valid()) {</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;              frag_ptr[idx] = *ptr;</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;          }</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;          ++address_iterator_;</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;        }</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;      }</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;    }</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;  }</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00326"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#abe79a4ca57e8ad8adbd88c703c75da80">  326</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#abe79a4ca57e8ad8adbd88c703c75da80">load</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a8e3ff6897ff18b510d459052e6aa5648">Fragment</a> &amp;frag) { load_with_pointer_offset(frag, 0); }</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00330"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ad7cac32cc4512e994d4da4e3b8930a1a">  330</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ad7cac32cc4512e994d4da4e3b8930a1a">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a8e3ff6897ff18b510d459052e6aa5648">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ac68f091bc3ef944e3476c838f2225157">Index</a> pointer_offset) {</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;    address_iterator_.set_iteration_index(0);</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;    AccessType <span class="keyword">const</span> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span>AccessType <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> s = 0; s &lt; ThreadMap::Iterations::kStrided; ++s) {</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> c = 0; c &lt; ThreadMap::Iterations::kContiguous; ++c) {</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> v = 0; v &lt; kAccessesPerVector; ++v) {</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;          <span class="keywordtype">int</span> idx = v + kAccessesPerVector * (c + s * ThreadMap::Iterations::kContiguous);</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;          <span class="keywordflow">if</span> (address_iterator_.valid()) {</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;            *(address_iterator_.get() + pointer_offset) = frag_ptr[idx];</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;          }</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;          ++address_iterator_;</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;        }</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;      }</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;    }</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;  }</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00354"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a652b98a226203cf3835fdbb3b5df2e36">  354</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a652b98a226203cf3835fdbb3b5df2e36">store</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a8e3ff6897ff18b510d459052e6aa5648">Fragment</a> <span class="keyword">const</span> &amp;frag) { store_with_pointer_offset(frag, 0); }</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;};</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;  <span class="keyword">typename</span> Shape_,</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  <span class="keyword">typename</span> Element_,</div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;  <span class="keywordtype">int</span> AdvanceRank,</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;  <span class="keyword">typename</span> ThreadMap_,</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;  <span class="keywordtype">int</span> AccessSize</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;&gt;</div><div class="line"><a name="l00373"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html">  373</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize&gt; {</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(AdvanceRank == 0 || AdvanceRank == 1, </div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;    <span class="stringliteral">&quot;Specialization for pitch-linear iterator may along advance along the &quot;</span></div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;    <span class="stringliteral">&quot;contiguous(rank=0) or strided(rank=1) dimension.&quot;</span>);</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;</div><div class="line"><a name="l00380"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a6700785ba130f52003af6ec2dbd0b92d">  380</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a6700785ba130f52003af6ec2dbd0b92d">Shape</a> = Shape_;</div><div class="line"><a name="l00381"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa476dc5039d98ecaad147dff946fba5b">  381</a></span>&#160;  <span class="keyword">using</span> Element = Element_;</div><div class="line"><a name="l00382"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#af156b766930c841133c2edaee29bab16">  382</a></span>&#160;  <span class="keyword">using</span> Layout = <a class="code" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>;</div><div class="line"><a name="l00383"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a77b2969e0af0ec77437681a1cd1bfc82">  383</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAdvanceRank = AdvanceRank;</div><div class="line"><a name="l00384"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa98fe4d2af87321acfcb336b71e1d24a">  384</a></span>&#160;  <span class="keyword">using</span> ThreadMap = ThreadMap_;</div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;</div><div class="line"><a name="l00386"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#acfd766202c25c0323160a91db74ad82b">  386</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#acfd766202c25c0323160a91db74ad82b">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1ColumnMajor.html#a6409273d6b40acb27aff0564eb038336">Layout::Index</a>;</div><div class="line"><a name="l00387"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#abd7913223c57a050ba28cb3683094384">  387</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#abd7913223c57a050ba28cb3683094384">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1ColumnMajor.html#a4cc90aa67c4692f0a2cd9f59b8a07997">Layout::LongIndex</a>;</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;</div><div class="line"><a name="l00389"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a4490084190f3fdd4b0fd846866adf457">  389</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00390"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ace68dde3ca4dfcea62c4000969d032e4">  390</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00391"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9c35288da31e9d3503aac4afdd17b620">  391</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9c35288da31e9d3503aac4afdd17b620">TensorCoord</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">Layout::TensorCoord</a>;</div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;</div><div class="line"><a name="l00393"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a77ec03286075f26cb5b3a020e3156745">  393</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a77ec03286075f26cb5b3a020e3156745">Pointer</a> = Element *;</div><div class="line"><a name="l00394"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a8a9532decd6ea4543b064de8f34cdb67">  394</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a8a9532decd6ea4543b064de8f34cdb67">NonConstPointer</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">platform::remove_const&lt;Element&gt;::type</a> *;</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> = <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape&lt;Shape::kRow, Shape::kColumn&gt;</a>,</div><div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;    Element,</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;    <a class="code" href="classcutlass_1_1layout_1_1PitchLinear.html">layout::PitchLinear</a>,</div><div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;    (kAdvanceRank == 0 ? 0 : 1),</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;    ThreadMap,</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;    AccessSize</div><div class="line"><a name="l00403"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a31062f8f847a6e962e157f29a2ee6221">  403</a></span>&#160;  &gt;;</div><div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;</div><div class="line"><a name="l00405"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a5284ce5a768c6e35e67580ffaa67e86b">  405</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a5284ce5a768c6e35e67580ffaa67e86b">AccessType</a> = <span class="keyword">typename</span> UnderlyingIterator::AccessType;</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;</div><div class="line"><a name="l00408"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aefea1fc075a0f02ef2d6069b379a48ba">  408</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aefea1fc075a0f02ef2d6069b379a48ba">Fragment</a> = cutlass::Array&lt;Element, ThreadMap::Iterations::kCount * ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;</div><div class="line"><a name="l00411"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9e22fa23c9927277741d6bea462f7589">  411</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9e22fa23c9927277741d6bea462f7589">Mask</a> = <span class="keyword">typename</span> UnderlyingIterator::Mask;</div><div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;</div><div class="line"><a name="l00414"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380.html">  414</a></span>&#160;  <span class="keyword">class </span>Params {</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;  <span class="keyword">private</span>:</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;</div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;    <span class="keyword">friend</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>;</div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;</div><div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;    <span class="keyword">typename</span> UnderlyingIterator::Params params_;</div><div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;</div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;    </div><div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00425"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380.html#a234151421c93148ed80209c99415bf7f">  425</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380.html#a234151421c93148ed80209c99415bf7f">Params</a>() { }</div><div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;</div><div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00429"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380.html#a14187b46ff5e13fec953402e0ce1ebac">  429</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380.html#a14187b46ff5e13fec953402e0ce1ebac">Params</a>(Layout <span class="keyword">const</span> &amp;layout): params_(layout::PitchLinear(layout.stride(0))) {</div><div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;</div><div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;    }</div><div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;  };</div><div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;</div><div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;</div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;</div><div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> iterator_;</div><div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;</div><div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00448"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a80cdbe77271741f7f60da8af629149f2">  448</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a80cdbe77271741f7f60da8af629149f2">PredicatedTileIterator</a>(</div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;    Params <span class="keyword">const</span> &amp;params,                         </div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a77ec03286075f26cb5b3a020e3156745">Pointer</a> pointer,                              </div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9c35288da31e9d3503aac4afdd17b620">TensorCoord</a> extent,                           </div><div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;    <span class="keywordtype">int</span> thread_id,                                </div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9c35288da31e9d3503aac4afdd17b620">TensorCoord</a> <span class="keyword">const</span> &amp;threadblock_offset         </div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;  ):</div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;    iterator_(</div><div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;      params.params_,</div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;      pointer,</div><div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;      layout::PitchLinearCoord(extent.row(), extent.column()),</div><div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;      thread_id,</div><div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;      layout::PitchLinearCoord(threadblock_offset.row(), threadblock_offset.column())</div><div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;    ) { }</div><div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;</div><div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00465"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a79a6680479608730781b18d8f9ef86fb">  465</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a79a6680479608730781b18d8f9ef86fb">PredicatedTileIterator</a>(</div><div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;    Params <span class="keyword">const</span> &amp;params,                         </div><div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a77ec03286075f26cb5b3a020e3156745">Pointer</a> pointer,                              </div><div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9c35288da31e9d3503aac4afdd17b620">TensorCoord</a> extent,                           </div><div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;    <span class="keywordtype">int</span> thread_id                                 </div><div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;  ): <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>(params, pointer, extent, thread_id, <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(0, 0)) { }</div><div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;</div><div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00474"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ae61cb62d51d3201d350b4f67556f2748">  474</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ae61cb62d51d3201d350b4f67556f2748">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#abd7913223c57a050ba28cb3683094384">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;    iterator_.add_pointer_offset(pointer_offset);</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;  }</div><div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;</div><div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00484"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac34969c60025d6206b848433319da4fa">  484</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> &amp;<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac34969c60025d6206b848433319da4fa">operator++</a>() {</div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;    ++iterator_;</div><div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;  }</div><div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;</div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00495"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a1461c6e7787331b888dca7122ac22927">  495</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a1461c6e7787331b888dca7122ac22927">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <span class="keyword">self</span>(*this);</div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;    <a class="code" href="namespacecutlass.html#a1c7a9e66ca7b5dc7413ea3b8f349530a">operator++</a>();</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">self</span>;</div><div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;  }</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00503"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac31418e71cb8ed71b7ebf51ab7028713">  503</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac31418e71cb8ed71b7ebf51ab7028713">clear_mask</a>() {</div><div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;    iterator_.clear_mask();</div><div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;  }</div><div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;</div><div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00509"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aeaaaa921a3fa27f9a181296c71c671e9">  509</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aeaaaa921a3fa27f9a181296c71c671e9">enable_mask</a>() {</div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;    iterator_.enable_mask();</div><div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;  }</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;</div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00515"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a26d7f61b89502cff1406f1268ecd1292">  515</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a26d7f61b89502cff1406f1268ecd1292">set_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9e22fa23c9927277741d6bea462f7589">Mask</a> <span class="keyword">const</span> &amp;mask) {</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;    iterator_.set_mask(mask);</div><div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;  }</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00521"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a8a2ddd611de1585295a6f698a8a60022">  521</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a8a2ddd611de1585295a6f698a8a60022">get_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9e22fa23c9927277741d6bea462f7589">Mask</a> &amp;mask) {</div><div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;    iterator_.get_mask(mask);</div><div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;  }</div><div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;</div><div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00527"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa8647ab882cf2af303ee89beb641e909">  527</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa8647ab882cf2af303ee89beb641e909">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aefea1fc075a0f02ef2d6069b379a48ba">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#acfd766202c25c0323160a91db74ad82b">Index</a> pointer_offset) {</div><div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;    iterator_.load_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;  }</div><div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;</div><div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00533"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa313dc3b60cef1df563ba4d78649f936">  533</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa313dc3b60cef1df563ba4d78649f936">load</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aefea1fc075a0f02ef2d6069b379a48ba">Fragment</a> &amp;frag) {</div><div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;    load_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;  }</div><div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;</div><div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00539"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac80e1650d1dca537fb577b5330710057">  539</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac80e1650d1dca537fb577b5330710057">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aefea1fc075a0f02ef2d6069b379a48ba">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#acfd766202c25c0323160a91db74ad82b">Index</a> pointer_offset) {</div><div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;    iterator_.store_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;  }</div><div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;</div><div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00545"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a3bf5b6d908482f6d98a483b4a7cafdcb">  545</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a3bf5b6d908482f6d98a483b4a7cafdcb">store</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aefea1fc075a0f02ef2d6069b379a48ba">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;    store_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;  }</div><div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;};</div><div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;</div><div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;</div><div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;  <span class="keyword">typename</span> Shape_,</div><div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;  <span class="keyword">typename</span> Element_,</div><div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;  <span class="keywordtype">int</span> AdvanceRank,</div><div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;  <span class="keyword">typename</span> ThreadMap_,</div><div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;  <span class="keywordtype">int</span> AccessSize</div><div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;&gt;</div><div class="line"><a name="l00566"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html">  566</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize&gt; {</div><div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;</div><div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(AdvanceRank == 0 || AdvanceRank == 1, </div><div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;    <span class="stringliteral">&quot;Specialization for pitch-linear iterator may along advance along the &quot;</span></div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;    <span class="stringliteral">&quot;contiguous(rank=0) or strided(rank=1) dimension.&quot;</span>);</div><div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;</div><div class="line"><a name="l00573"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aa7481efb4a8103be501fd5cb8e0e526e">  573</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aa7481efb4a8103be501fd5cb8e0e526e">Shape</a> = Shape_;</div><div class="line"><a name="l00574"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a260ae0db8d571d7a47a0852eb429990a">  574</a></span>&#160;  <span class="keyword">using</span> Element = Element_;</div><div class="line"><a name="l00575"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aa2f5d30f2316367c6deef3d6e1811132">  575</a></span>&#160;  <span class="keyword">using</span> Layout = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00576"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ab0c8347bf8f121315d30f4429f3855b9">  576</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAdvanceRank = AdvanceRank;</div><div class="line"><a name="l00577"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a793ea29df805abccf7e388ad33f192a8">  577</a></span>&#160;  <span class="keyword">using</span> ThreadMap = ThreadMap_;</div><div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;</div><div class="line"><a name="l00579"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aee0b606c8a3d36df29d4e7a537d3755a">  579</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aee0b606c8a3d36df29d4e7a537d3755a">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#aa49e242b14b4f482bc6bdd082acfb576">Layout::Index</a>;</div><div class="line"><a name="l00580"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a42dd3be2ba430549488aa4aff56f5296">  580</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a42dd3be2ba430549488aa4aff56f5296">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">Layout::LongIndex</a>;</div><div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;</div><div class="line"><a name="l00582"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a9c6eb32c222a698eef50770407fdc86d">  582</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00583"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a541c99bcc66affca62f429e6e7698b17">  583</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00584"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a530090ed803464ad2819c6b8b036b573">  584</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a530090ed803464ad2819c6b8b036b573">TensorCoord</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">Layout::TensorCoord</a>;</div><div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;</div><div class="line"><a name="l00586"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0f1b733f3d1da13bd967b6eb76f04ae6">  586</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0f1b733f3d1da13bd967b6eb76f04ae6">Pointer</a> = Element *;</div><div class="line"><a name="l00587"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a54dc1021b1620142b91ed7ace0282b77">  587</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a54dc1021b1620142b91ed7ace0282b77">NonConstPointer</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">platform::remove_const&lt;Element&gt;::type</a> *;</div><div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;</div><div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> = <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;</div><div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape&lt;Shape::kColumn, Shape::kRow&gt;</a>,</div><div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;    Element,</div><div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;    <a class="code" href="classcutlass_1_1layout_1_1PitchLinear.html">layout::PitchLinear</a>,</div><div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;    (kAdvanceRank == 0 ? 1 : 0),</div><div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;    ThreadMap,</div><div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;    AccessSize</div><div class="line"><a name="l00596"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#abf177445aee9593c35a4343dd082b07d">  596</a></span>&#160;  &gt;;</div><div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;</div><div class="line"><a name="l00598"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a90f8faf720c886784e1383ab1fa6295b">  598</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a90f8faf720c886784e1383ab1fa6295b">AccessType</a> = <span class="keyword">typename</span> UnderlyingIterator::AccessType;</div><div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;</div><div class="line"><a name="l00601"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ad1ade679ed38f572252668ecbc3b6df4">  601</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ad1ade679ed38f572252668ecbc3b6df4">Fragment</a> = cutlass::Array&lt;Element, ThreadMap::Iterations::kCount * ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;</div><div class="line"><a name="l00604"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a278a0d69e5ffe2602faac7a29f5f9e74">  604</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a278a0d69e5ffe2602faac7a29f5f9e74">Mask</a> = <span class="keyword">typename</span> UnderlyingIterator::Mask;</div><div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;</div><div class="line"><a name="l00607"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd.html">  607</a></span>&#160;  <span class="keyword">class </span>Params {</div><div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;  <span class="keyword">private</span>:</div><div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;</div><div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;    <span class="keyword">friend</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>;</div><div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;</div><div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;    <span class="keyword">typename</span> UnderlyingIterator::Params params_;</div><div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;</div><div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;    </div><div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00618"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd.html#a40aa9cc4c64cc49e926ce791326f70bb">  618</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd.html#a40aa9cc4c64cc49e926ce791326f70bb">Params</a>() { } </div><div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;</div><div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00622"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd.html#ac553ce446cfdc6e4a1ccfe443812564e">  622</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd.html#ac553ce446cfdc6e4a1ccfe443812564e">Params</a>(Layout <span class="keyword">const</span> &amp;layout): params_(layout::PitchLinear(layout.stride(0))) {</div><div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;</div><div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;    };</div><div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;  };</div><div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;</div><div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;</div><div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;</div><div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;</div><div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> iterator_;</div><div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;</div><div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;</div><div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00641"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aa496890ca772ec1ee9ed7afe0651ba07">  641</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aa496890ca772ec1ee9ed7afe0651ba07">PredicatedTileIterator</a>(</div><div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;    Params <span class="keyword">const</span> &amp;params,                         </div><div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0f1b733f3d1da13bd967b6eb76f04ae6">Pointer</a> pointer,                              </div><div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a530090ed803464ad2819c6b8b036b573">TensorCoord</a> extent,                           </div><div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;    <span class="keywordtype">int</span> thread_id,                                </div><div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a530090ed803464ad2819c6b8b036b573">TensorCoord</a> <span class="keyword">const</span> &amp;threadblock_offset         </div><div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;  ):</div><div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;    iterator_(</div><div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;      params.params_,</div><div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;      pointer,</div><div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;      layout::PitchLinearCoord(extent.column(), extent.row()),</div><div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;      thread_id,</div><div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;      layout::PitchLinearCoord(threadblock_offset.column(), threadblock_offset.row())</div><div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;    ) { }</div><div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;</div><div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00658"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ab77e9c9d9d7337dea6c27ee24f09ea86">  658</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ab77e9c9d9d7337dea6c27ee24f09ea86">PredicatedTileIterator</a>(</div><div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;    Params <span class="keyword">const</span> &amp;params,                         </div><div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0f1b733f3d1da13bd967b6eb76f04ae6">Pointer</a> pointer,                              </div><div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a530090ed803464ad2819c6b8b036b573">TensorCoord</a> extent,                           </div><div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;    <span class="keywordtype">int</span> thread_id                                 </div><div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;  ): <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>(params, pointer, extent, thread_id, <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(0, 0)) { }</div><div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;</div><div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00667"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a36163de0c19d00d8f00ea5ef76a7068d">  667</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a36163de0c19d00d8f00ea5ef76a7068d">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a42dd3be2ba430549488aa4aff56f5296">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;    iterator_.add_pointer_offset(pointer_offset);</div><div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;  }</div><div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160;</div><div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00677"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a03832ac3ba57eb81b9cbd3eb63f4af70">  677</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> &amp;<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a03832ac3ba57eb81b9cbd3eb63f4af70">operator++</a>() {</div><div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;    ++iterator_;</div><div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;  }</div><div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;</div><div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00688"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a052bca4037b27b51e285019ac3b08f2e">  688</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a052bca4037b27b51e285019ac3b08f2e">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <span class="keyword">self</span>(*this);</div><div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;    <a class="code" href="namespacecutlass.html#a1c7a9e66ca7b5dc7413ea3b8f349530a">operator++</a>();</div><div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">self</span>;</div><div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;  }</div><div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;</div><div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00696"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a38b1509afcb20c5474ea5998f85c6507">  696</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a38b1509afcb20c5474ea5998f85c6507">clear_mask</a>() {</div><div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;    iterator_.clear_mask();</div><div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;  }</div><div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;</div><div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00702"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#afed2a0c5cfb8f89aaf66a7acaa4e6568">  702</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#afed2a0c5cfb8f89aaf66a7acaa4e6568">enable_mask</a>() {</div><div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;    iterator_.enable_mask();</div><div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;  }</div><div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;</div><div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00708"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0a5671614bcf97fda9d6f042a79fe4c2">  708</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0a5671614bcf97fda9d6f042a79fe4c2">set_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a278a0d69e5ffe2602faac7a29f5f9e74">Mask</a> <span class="keyword">const</span> &amp;mask) {</div><div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;    iterator_.set_mask(mask);</div><div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;  }</div><div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;</div><div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00714"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a958f75bd15e03403c1517c767bc06b9e">  714</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a958f75bd15e03403c1517c767bc06b9e">get_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a278a0d69e5ffe2602faac7a29f5f9e74">Mask</a> &amp;mask) {</div><div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;    iterator_.get_mask(mask);</div><div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;  }</div><div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;</div><div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00720"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a113aa25ba6653b2590f3995cccc3b8f1">  720</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a113aa25ba6653b2590f3995cccc3b8f1">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ad1ade679ed38f572252668ecbc3b6df4">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aee0b606c8a3d36df29d4e7a537d3755a">Index</a> pointer_offset) {</div><div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;    iterator_.load_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;  }</div><div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;</div><div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00726"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a608521f07cd7929c312891b6765d4a0e">  726</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a608521f07cd7929c312891b6765d4a0e">load</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ad1ade679ed38f572252668ecbc3b6df4">Fragment</a> &amp;frag) {</div><div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;    load_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;  }</div><div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;</div><div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00732"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a3c3f0b4acc1f837edd2352659c594552">  732</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a3c3f0b4acc1f837edd2352659c594552">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ad1ade679ed38f572252668ecbc3b6df4">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aee0b606c8a3d36df29d4e7a537d3755a">Index</a> pointer_offset) {</div><div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;    iterator_.store_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;  }</div><div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160;</div><div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00738"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a612698abfee9f8c3bc71d25646b3987e">  738</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a612698abfee9f8c3bc71d25646b3987e">store</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ad1ade679ed38f572252668ecbc3b6df4">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;    store_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;  }</div><div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;};</div><div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;</div><div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;</div><div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;</div><div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Shape_, <span class="keyword">typename</span> Element_, <span class="keywordtype">int</span> AdvanceRank,</div><div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;          <span class="keyword">typename</span> ThreadMap_, <span class="keywordtype">int</span> AccessSize, <span class="keywordtype">int</span> InterleavedK&gt;</div><div class="line"><a name="l00756"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html">  756</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;Shape_, Element_,</div><div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;                             layout::ColumnMajorInterleaved&lt;InterleavedK&gt;,</div><div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;                             AdvanceRank, ThreadMap_, AccessSize&gt; {</div><div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;      AdvanceRank == 0 || AdvanceRank == 1,</div><div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;      <span class="stringliteral">&quot;Specialization for pitch-linear iterator may along advance along the &quot;</span></div><div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;      <span class="stringliteral">&quot;contiguous(rank=0) or strided(rank=1) dimension.&quot;</span>);</div><div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;</div><div class="line"><a name="l00765"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a81b11f9aa20f81003ddae3a482004ec1">  765</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a81b11f9aa20f81003ddae3a482004ec1">Shape</a> = Shape_;</div><div class="line"><a name="l00766"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#abe10b707c6f431eea0ccc08adfd1c1e9">  766</a></span>&#160;  <span class="keyword">using</span> Element = Element_;</div><div class="line"><a name="l00767"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ab71694768c19d697bf73481ca0d7a3d5">  767</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kInterleavedK = InterleavedK;</div><div class="line"><a name="l00768"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a53196148230c2f8933616f736b1ba704">  768</a></span>&#160;  <span class="keyword">using</span> Layout = <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">layout::ColumnMajorInterleaved&lt;kInterleavedK&gt;</a>;</div><div class="line"><a name="l00769"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a3dce16276f08ab3143ded18faeaed489">  769</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAdvanceRank = AdvanceRank;</div><div class="line"><a name="l00770"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#af68826051ca8dcde2df672a78cec83f5">  770</a></span>&#160;  <span class="keyword">using</span> ThreadMap = ThreadMap_;</div><div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;</div><div class="line"><a name="l00772"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a910b8ba4e409fb7d26739ee3f0c46a36">  772</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a910b8ba4e409fb7d26739ee3f0c46a36">Index</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#aa3e25100889b6dfb1a8c1a256e9185ea">Layout::Index</a>;</div><div class="line"><a name="l00773"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#af950c284f063cced0e0946d824aff078">  773</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#af950c284f063cced0e0946d824aff078">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#a85c8727c1bf645ac264dfed8186a1240">Layout::LongIndex</a>;</div><div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;</div><div class="line"><a name="l00775"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a359d84e3697cdc7dc6344aa9d748b221">  775</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00776"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7149dbf1d9b02348de5c71af7b24764a">  776</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00777"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a414f47df2907cd8e2336b8cb75b58308">  777</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a414f47df2907cd8e2336b8cb75b58308">TensorCoord</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">Layout::TensorCoord</a>;</div><div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;</div><div class="line"><a name="l00779"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a5761c878c096fe769e76b5dbb951d1f5">  779</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a5761c878c096fe769e76b5dbb951d1f5">Pointer</a> = Element *;</div><div class="line"><a name="l00780"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a0ac0d4198232bc67245b8f7f3536968e">  780</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a0ac0d4198232bc67245b8f7f3536968e">NonConstPointer</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">platform::remove_const&lt;Element&gt;::type</a> *;</div><div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;</div><div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> = <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;</div><div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;      <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;Shape::kRow * kInterleavedK,</div><div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;                               Shape::kColumn / kInterleavedK&gt;,</div><div class="line"><a name="l00785"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a637707ceb0eaf9c93cb56b6df5942df0">  785</a></span>&#160;      Element, <a class="code" href="classcutlass_1_1layout_1_1PitchLinear.html">layout::PitchLinear</a>, (kAdvanceRank == 0 ? 0 : 1), ThreadMap, AccessSize&gt;;</div><div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;</div><div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160;</div><div class="line"><a name="l00788"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a9421518aa9c9510dcbe41bda4e90295b">  788</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a9421518aa9c9510dcbe41bda4e90295b">AccessType</a> = <span class="keyword">typename</span> UnderlyingIterator::AccessType;</div><div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;</div><div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7712f023dcf819a124a9cb569eda0483">Fragment</a> = cutlass::Array&lt;Element, ThreadMap::Iterations::kCount *</div><div class="line"><a name="l00792"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7712f023dcf819a124a9cb569eda0483">  792</a></span>&#160;                                               ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;</div><div class="line"><a name="l00795"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac9583e3c6f7046733a0fa5671d8f32be">  795</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac9583e3c6f7046733a0fa5671d8f32be">Mask</a> = <span class="keyword">typename</span> UnderlyingIterator::Mask;</div><div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;</div><div class="line"><a name="l00798"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8.html">  798</a></span>&#160;  <span class="keyword">class </span>Params {</div><div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;   <span class="keyword">private</span>:</div><div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;    <span class="keyword">friend</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>;</div><div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;</div><div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;    <span class="keyword">typename</span> UnderlyingIterator::Params params_;</div><div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;</div><div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;   <span class="keyword">public</span>:</div><div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00807"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8.html#ae36baf85b0cc499418c5c0c6622e2b8a">  807</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8.html#ae36baf85b0cc499418c5c0c6622e2b8a">Params</a>() {}</div><div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;</div><div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00811"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8.html#a4787959c38114875acb60e554141860b">  811</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8.html#a4787959c38114875acb60e554141860b">Params</a>(Layout <span class="keyword">const</span> &amp;layout)</div><div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;        : params_(layout::PitchLinear(layout.stride(0))) {}</div><div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;  };</div><div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;</div><div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;</div><div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> iterator_;</div><div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;</div><div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00827"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a9bf65422579611ecaf4275516b6d9e1f">  827</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a9bf65422579611ecaf4275516b6d9e1f">PredicatedTileIterator</a>(</div><div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;      Params <span class="keyword">const</span> &amp;params,</div><div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a5761c878c096fe769e76b5dbb951d1f5">Pointer</a> pointer,</div><div class="line"><a name="l00833"></a><span class="lineno">  833</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a414f47df2907cd8e2336b8cb75b58308">TensorCoord</a> extent,</div><div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;      <span class="keywordtype">int</span> thread_id,</div><div class="line"><a name="l00837"></a><span class="lineno">  837</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a414f47df2907cd8e2336b8cb75b58308">TensorCoord</a> <span class="keyword">const</span> &amp;threadblock_offset)</div><div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;      : iterator_(params.params_, pointer,</div><div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;                  layout::PitchLinearCoord(extent.row() * kInterleavedK,</div><div class="line"><a name="l00840"></a><span class="lineno">  840</span>&#160;                                           extent.column() / kInterleavedK),</div><div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;                  thread_id,</div><div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;                  layout::PitchLinearCoord(</div><div class="line"><a name="l00843"></a><span class="lineno">  843</span>&#160;                      threadblock_offset.row() * kInterleavedK,</div><div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;                      threadblock_offset.column() / kInterleavedK)) {}</div><div class="line"><a name="l00845"></a><span class="lineno">  845</span>&#160;</div><div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00848"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a52c7377c5a2457b3b652e90f2f3a1a53">  848</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a52c7377c5a2457b3b652e90f2f3a1a53">PredicatedTileIterator</a>(</div><div class="line"><a name="l00849"></a><span class="lineno">  849</span>&#160;      Params <span class="keyword">const</span> &amp;params,  </div><div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a5761c878c096fe769e76b5dbb951d1f5">Pointer</a> pointer,       </div><div class="line"><a name="l00851"></a><span class="lineno">  851</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a414f47df2907cd8e2336b8cb75b58308">TensorCoord</a> extent,    </div><div class="line"><a name="l00852"></a><span class="lineno">  852</span>&#160;      <span class="keywordtype">int</span> thread_id          </div><div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;      )</div><div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;      : <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>(params, pointer, extent, thread_id,</div><div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;                               <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(0, 0)) {}</div><div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;</div><div class="line"><a name="l00858"></a><span class="lineno">  858</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00859"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a15baac20f1c70f0adc6f3f882ba2ef16">  859</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a15baac20f1c70f0adc6f3f882ba2ef16">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#af950c284f063cced0e0946d824aff078">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160;    iterator_.add_pointer_offset(pointer_offset);</div><div class="line"><a name="l00861"></a><span class="lineno">  861</span>&#160;  }</div><div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;</div><div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00870"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac87a36086f64417f60da797760236aaa">  870</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> &amp;<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac87a36086f64417f60da797760236aaa">operator++</a>() {</div><div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;    ++iterator_;</div><div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;  }</div><div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160;</div><div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00882"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a914aca3f9a44a0bd2098d2fa13d59c62">  882</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a914aca3f9a44a0bd2098d2fa13d59c62">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <span class="keyword">self</span>(*this);</div><div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;    <a class="code" href="namespacecutlass.html#a1c7a9e66ca7b5dc7413ea3b8f349530a">operator++</a>();</div><div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">self</span>;</div><div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160;  }</div><div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;</div><div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00890"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a73c78020399019ab244400b48f43d7cc">  890</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a73c78020399019ab244400b48f43d7cc">clear_mask</a>() { iterator_.clear_mask(); }</div><div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;</div><div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00894"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ae26a5a59ceb0def13f9717340b36a147">  894</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ae26a5a59ceb0def13f9717340b36a147">enable_mask</a>() { iterator_.enable_mask(); }</div><div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;</div><div class="line"><a name="l00897"></a><span class="lineno">  897</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00898"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ab9d91c62b03d9ac31141967f590efdef">  898</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ab9d91c62b03d9ac31141967f590efdef">set_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac9583e3c6f7046733a0fa5671d8f32be">Mask</a> <span class="keyword">const</span> &amp;mask) { iterator_.set_mask(mask); }</div><div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;</div><div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00902"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a02237c18b5f4662756ef7f1898a75a2d">  902</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a02237c18b5f4662756ef7f1898a75a2d">get_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac9583e3c6f7046733a0fa5671d8f32be">Mask</a> &amp;mask) { iterator_.get_mask(mask); }</div><div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;</div><div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00906"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a4fc50f92e4b27f31123244caddee61cd">  906</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a4fc50f92e4b27f31123244caddee61cd">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7712f023dcf819a124a9cb569eda0483">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a910b8ba4e409fb7d26739ee3f0c46a36">Index</a> pointer_offset) {</div><div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;    iterator_.load_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;  }</div><div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;</div><div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00912"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a09f33899cad1084651ac9a558a701a4e">  912</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a09f33899cad1084651ac9a558a701a4e">load</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7712f023dcf819a124a9cb569eda0483">Fragment</a> &amp;frag) { load_with_pointer_offset(frag, 0); }</div><div class="line"><a name="l00913"></a><span class="lineno">  913</span>&#160;</div><div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00916"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a8b56ab072b393e313624c5761676e097">  916</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a8b56ab072b393e313624c5761676e097">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7712f023dcf819a124a9cb569eda0483">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a910b8ba4e409fb7d26739ee3f0c46a36">Index</a> pointer_offset) {</div><div class="line"><a name="l00917"></a><span class="lineno">  917</span>&#160;    iterator_.store_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;  }</div><div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;</div><div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00922"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a657aa5f22a2bc0cb7558256a7a8bf36e">  922</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a657aa5f22a2bc0cb7558256a7a8bf36e">store</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7712f023dcf819a124a9cb569eda0483">Fragment</a> <span class="keyword">const</span> &amp;frag) { store_with_pointer_offset(frag, 0); }</div><div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;};</div><div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;</div><div class="line"><a name="l00926"></a><span class="lineno">  926</span>&#160;</div><div class="line"><a name="l00935"></a><span class="lineno">  935</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Shape_, <span class="keyword">typename</span> Element_, <span class="keywordtype">int</span> AdvanceRank,</div><div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;          <span class="keyword">typename</span> ThreadMap_, <span class="keywordtype">int</span> AccessSize, <span class="keywordtype">int</span> InterleavedK&gt;</div><div class="line"><a name="l00937"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html">  937</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;Shape_, Element_,</div><div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;                             layout::RowMajorInterleaved&lt;InterleavedK&gt;,</div><div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;                             AdvanceRank, ThreadMap_, AccessSize&gt; {</div><div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00941"></a><span class="lineno">  941</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;      AdvanceRank == 0 || AdvanceRank == 1,</div><div class="line"><a name="l00943"></a><span class="lineno">  943</span>&#160;      <span class="stringliteral">&quot;Specialization for pitch-linear iterator may along advance along the &quot;</span></div><div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;      <span class="stringliteral">&quot;contiguous(rank=0) or strided(rank=1) dimension.&quot;</span>);</div><div class="line"><a name="l00945"></a><span class="lineno">  945</span>&#160;</div><div class="line"><a name="l00946"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a77d8f033b1266f6cd90688b363ec71e4">  946</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a77d8f033b1266f6cd90688b363ec71e4">Shape</a> = Shape_;</div><div class="line"><a name="l00947"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a2a986ab82d4695a640bc71ce6bcf88c8">  947</a></span>&#160;  <span class="keyword">using</span> Element = Element_;</div><div class="line"><a name="l00948"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a001d75d0f08f26b38ceefa6d63426f09">  948</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kInterleavedK = InterleavedK;</div><div class="line"><a name="l00949"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a08dc4dc5f67cf7255ad3b4757646f6d8">  949</a></span>&#160;  <span class="keyword">using</span> Layout = <a class="code" href="structcutlass_1_1layout_1_1RowMajorInterleaved.html">layout::RowMajorInterleaved&lt;kInterleavedK&gt;</a>;</div><div class="line"><a name="l00950"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#afbc38be9ceacc01a877073ec35219542">  950</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAdvanceRank = AdvanceRank;</div><div class="line"><a name="l00951"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a7a7f85659348e65d6722ca0cf9980fa5">  951</a></span>&#160;  <span class="keyword">using</span> ThreadMap = ThreadMap_;</div><div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;</div><div class="line"><a name="l00953"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#afa189422b204c68d154c816ae84fa13c">  953</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#afa189422b204c68d154c816ae84fa13c">Index</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorInterleaved.html#ac827eba11d5fb935273a295d8eb1b972">Layout::Index</a>;</div><div class="line"><a name="l00954"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ad4b81297032276616f712800457bded4">  954</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ad4b81297032276616f712800457bded4">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1layout_1_1RowMajorInterleaved.html#a4b39c8accf5d11d6cc8d33aeb2b2e5fe">Layout::LongIndex</a>;</div><div class="line"><a name="l00955"></a><span class="lineno">  955</span>&#160;</div><div class="line"><a name="l00956"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a8549cc94fc141761af6932a4afca3cb3">  956</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00957"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a41cdc3060ac6453a0208e0385be3fdc4">  957</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00958"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aedf4f1a74adbb6948585059359918300">  958</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aedf4f1a74adbb6948585059359918300">TensorCoord</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">Layout::TensorCoord</a>;</div><div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;</div><div class="line"><a name="l00960"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a838b764b815761c22bb879e4ceebccf9">  960</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a838b764b815761c22bb879e4ceebccf9">Pointer</a> = Element *;</div><div class="line"><a name="l00961"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa0273c9e8e274e488164729ca5e83e71">  961</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa0273c9e8e274e488164729ca5e83e71">NonConstPointer</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">platform::remove_const&lt;Element&gt;::type</a> *;</div><div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;</div><div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> = <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>&lt;</div><div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;      <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;Shape::kColumn * kInterleavedK,</div><div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;                               Shape::kRow / kInterleavedK&gt;,</div><div class="line"><a name="l00966"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ae529ae9942e1454e524ba767f76c12f0">  966</a></span>&#160;      Element, <a class="code" href="classcutlass_1_1layout_1_1PitchLinear.html">layout::PitchLinear</a>, (kAdvanceRank == 0 ? 1 : 0), ThreadMap, AccessSize&gt;;</div><div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;</div><div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;</div><div class="line"><a name="l00969"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a22505310ada0406477bafd4edfbc1ad3">  969</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a22505310ada0406477bafd4edfbc1ad3">AccessType</a> = <span class="keyword">typename</span> UnderlyingIterator::AccessType;</div><div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;  </div><div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a799b0d6d28334da4d5f0c16e95813338">Fragment</a> = cutlass::Array&lt;Element, ThreadMap::Iterations::kCount *</div><div class="line"><a name="l00973"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a799b0d6d28334da4d5f0c16e95813338">  973</a></span>&#160;                                               ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;</div><div class="line"><a name="l00976"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa047aa70ae6f0eabebbafa2620692833">  976</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa047aa70ae6f0eabebbafa2620692833">Mask</a> = <span class="keyword">typename</span> UnderlyingIterator::Mask;</div><div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;</div><div class="line"><a name="l00979"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f.html">  979</a></span>&#160;  <span class="keyword">class </span>Params {</div><div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;   <span class="keyword">private</span>:</div><div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;    <span class="keyword">friend</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>;</div><div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;</div><div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;    <span class="keyword">typename</span> UnderlyingIterator::Params params_;</div><div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;</div><div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;   <span class="keyword">public</span>:</div><div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00988"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f.html#a565c30dda08950f30fa66b6ca1d0bae1">  988</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f.html#a565c30dda08950f30fa66b6ca1d0bae1">Params</a>() {}</div><div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;</div><div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00992"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f.html#a7e2f044d616b27b0fad5c4fa060acef9">  992</a></span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f.html#a7e2f044d616b27b0fad5c4fa060acef9">Params</a>(Layout <span class="keyword">const</span> &amp;layout)</div><div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;        : params_(layout::PitchLinear(layout.stride(0))) {}</div><div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;  };</div><div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;</div><div class="line"><a name="l00996"></a><span class="lineno">  996</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00997"></a><span class="lineno">  997</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l01000"></a><span class="lineno"> 1000</span>&#160;</div><div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">UnderlyingIterator</a> iterator_;</div><div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160;</div><div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01008"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a699675b64057208e8ae82f700c7adb6b"> 1008</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a699675b64057208e8ae82f700c7adb6b">PredicatedTileIterator</a>(</div><div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;      Params <span class="keyword">const</span> &amp;params,</div><div class="line"><a name="l01012"></a><span class="lineno"> 1012</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a838b764b815761c22bb879e4ceebccf9">Pointer</a> pointer,</div><div class="line"><a name="l01014"></a><span class="lineno"> 1014</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aedf4f1a74adbb6948585059359918300">TensorCoord</a> extent,</div><div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;      <span class="keywordtype">int</span> thread_id,</div><div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aedf4f1a74adbb6948585059359918300">TensorCoord</a> <span class="keyword">const</span> &amp;threadblock_offset)</div><div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160;      : iterator_(params.params_, pointer,</div><div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;                  layout::PitchLinearCoord(extent.column() * kInterleavedK,</div><div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160;                                           extent.row() / kInterleavedK),</div><div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;                  thread_id,</div><div class="line"><a name="l01023"></a><span class="lineno"> 1023</span>&#160;                  layout::PitchLinearCoord(</div><div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160;                      threadblock_offset.column() * kInterleavedK,</div><div class="line"><a name="l01025"></a><span class="lineno"> 1025</span>&#160;                      threadblock_offset.row() / kInterleavedK)) {}</div><div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;</div><div class="line"><a name="l01028"></a><span class="lineno"> 1028</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01029"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#adc98a00c097c5b93755b182ea493d2c6"> 1029</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#adc98a00c097c5b93755b182ea493d2c6">PredicatedTileIterator</a>(</div><div class="line"><a name="l01030"></a><span class="lineno"> 1030</span>&#160;      Params <span class="keyword">const</span> &amp;params,  </div><div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a838b764b815761c22bb879e4ceebccf9">Pointer</a> pointer,       </div><div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;      <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aedf4f1a74adbb6948585059359918300">TensorCoord</a> extent,    </div><div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;      <span class="keywordtype">int</span> thread_id          </div><div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;      )</div><div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;      : <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a>(params, pointer, extent, thread_id,</div><div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;                               <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(0, 0)) {}</div><div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;</div><div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01040"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#af7dcf21ebed32a5a1b98180413ff8d8c"> 1040</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#af7dcf21ebed32a5a1b98180413ff8d8c">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ad4b81297032276616f712800457bded4">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;    iterator_.add_pointer_offset(pointer_offset);</div><div class="line"><a name="l01042"></a><span class="lineno"> 1042</span>&#160;  }</div><div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;</div><div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01051"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a958a3a54e4af3bbe7e22c8205e2c335f"> 1051</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> &amp;<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a958a3a54e4af3bbe7e22c8205e2c335f">operator++</a>() {</div><div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160;    ++iterator_;</div><div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l01054"></a><span class="lineno"> 1054</span>&#160;  }</div><div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;</div><div class="line"><a name="l01062"></a><span class="lineno"> 1062</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01063"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a2465bdfb439468c269dd32c441b54a86"> 1063</a></span>&#160;  <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a2465bdfb439468c269dd32c441b54a86">operator++</a>(<span class="keywordtype">int</span>) {</div><div class="line"><a name="l01064"></a><span class="lineno"> 1064</span>&#160;    <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> <span class="keyword">self</span>(*this);</div><div class="line"><a name="l01065"></a><span class="lineno"> 1065</span>&#160;    <a class="code" href="namespacecutlass.html#a1c7a9e66ca7b5dc7413ea3b8f349530a">operator++</a>();</div><div class="line"><a name="l01066"></a><span class="lineno"> 1066</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">self</span>;</div><div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;  }</div><div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;</div><div class="line"><a name="l01070"></a><span class="lineno"> 1070</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01071"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a40cdf0a9b56d2571c87f50d1abcfae73"> 1071</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a40cdf0a9b56d2571c87f50d1abcfae73">clear_mask</a>() { iterator_.clear_mask(); }</div><div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;</div><div class="line"><a name="l01074"></a><span class="lineno"> 1074</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01075"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ace4da5baffa2dac199449b90dc486414"> 1075</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ace4da5baffa2dac199449b90dc486414">enable_mask</a>() { iterator_.enable_mask(); }</div><div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;</div><div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01079"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a6f363efdc9a85011e1d4ed76a83f5ce9"> 1079</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a6f363efdc9a85011e1d4ed76a83f5ce9">set_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa047aa70ae6f0eabebbafa2620692833">Mask</a> <span class="keyword">const</span> &amp;mask) { iterator_.set_mask(mask); }</div><div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;</div><div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01083"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a44b1d56e527e466f8406b19cd51f34a5"> 1083</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a44b1d56e527e466f8406b19cd51f34a5">get_mask</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa047aa70ae6f0eabebbafa2620692833">Mask</a> &amp;mask) { iterator_.get_mask(mask); }</div><div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;</div><div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l01087"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a5107ffd9ef9d9016b1f810a0d5ecac07"> 1087</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a5107ffd9ef9d9016b1f810a0d5ecac07">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a799b0d6d28334da4d5f0c16e95813338">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#afa189422b204c68d154c816ae84fa13c">Index</a> pointer_offset) {</div><div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;    iterator_.load_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160;  }</div><div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;</div><div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l01093"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a9f98ff9fa96941ccd8ce149fbaa620f9"> 1093</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a9f98ff9fa96941ccd8ce149fbaa620f9">load</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a799b0d6d28334da4d5f0c16e95813338">Fragment</a> &amp;frag) { load_with_pointer_offset(frag, 0); }</div><div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;</div><div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l01097"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a7399a18e6cd90e857f9f6dc3e93ab1e6"> 1097</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a7399a18e6cd90e857f9f6dc3e93ab1e6">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a799b0d6d28334da4d5f0c16e95813338">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#afa189422b204c68d154c816ae84fa13c">Index</a> pointer_offset) {</div><div class="line"><a name="l01098"></a><span class="lineno"> 1098</span>&#160;    iterator_.store_with_pointer_offset(frag, pointer_offset);</div><div class="line"><a name="l01099"></a><span class="lineno"> 1099</span>&#160;  }</div><div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;</div><div class="line"><a name="l01102"></a><span class="lineno"> 1102</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l01103"></a><span class="lineno"><a class="line" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a28785f98e2f9204d29cd430b87c33bdf"> 1103</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a28785f98e2f9204d29cd430b87c33bdf">store</a>(<a class="code" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a799b0d6d28334da4d5f0c16e95813338">Fragment</a> <span class="keyword">const</span> &amp;frag) { store_with_pointer_offset(frag, 0); }</div><div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;};</div><div class="line"><a name="l01105"></a><span class="lineno"> 1105</span>&#160;</div><div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;</div><div class="line"><a name="l01108"></a><span class="lineno"> 1108</span>&#160;} <span class="comment">// namespace threadblock</span></div><div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;} <span class="comment">// namespace transform</span></div><div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l01111"></a><span class="lineno"> 1111</span>&#160;</div><div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a5284ce5a768c6e35e67580ffaa67e86b"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a5284ce5a768c6e35e67580ffaa67e86b">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::AccessType</a></div><div class="ttdeci">typename UnderlyingIterator::AccessType AccessType</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:405</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a6700785ba130f52003af6ec2dbd0b92d"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a6700785ba130f52003af6ec2dbd0b92d">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:380</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_ac31418e71cb8ed71b7ebf51ab7028713"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac31418e71cb8ed71b7ebf51ab7028713">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::clear_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:503</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_ad4b81297032276616f712800457bded4"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ad4b81297032276616f712800457bded4">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:954</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_aa313dc3b60cef1df563ba4d78649f936"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa313dc3b60cef1df563ba4d78649f936">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:533</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_a4062a36ab044fdea058504ed52ee60b8"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">cutlass::layout::RowMajor::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:62</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorInterleaved_html_a85c8727c1bf645ac264dfed8186a1240"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#a85c8727c1bf645ac264dfed8186a1240">cutlass::layout::ColumnMajorInterleaved::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:355</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a652b98a226203cf3835fdbb3b5df2e36"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a652b98a226203cf3835fdbb3b5df2e36">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::store</a></div><div class="ttdeci">CUTLASS_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:354</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a0f1b733f3d1da13bd967b6eb76f04ae6"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0f1b733f3d1da13bd967b6eb76f04ae6">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Pointer</a></div><div class="ttdeci">Element * Pointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:586</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_af7dcf21ebed32a5a1b98180413ff8d8c"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#af7dcf21ebed32a5a1b98180413ff8d8c">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1040</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a1faa65b2559fa3f7ca89cd171d656135"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a1faa65b2559fa3f7ca89cd171d656135">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:154</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a6b9ef24ac3350b33f76a2c580e31a579"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6b9ef24ac3350b33f76a2c580e31a579">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Mask</a></div><div class="ttdeci">typename TileAccessIterator::Mask Mask</div><div class="ttdoc">Predicate vector stores mask to guard accesses. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:185</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_ac87a36086f64417f60da797760236aaa"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac87a36086f64417f60da797760236aaa">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:870</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html">cutlass::layout::PitchLinearCoord</a></div><div class="ttdoc">Coordinate in pitch-linear space. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:52</div></div>
<div class="ttc" id="memory_8h_html"><div class="ttname"><a href="memory_8h.html">memory.h</a></div><div class="ttdoc">Architecture-specific operators on memory. </div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a1461c6e7787331b888dca7122ac22927"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a1461c6e7787331b888dca7122ac22927">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator operator++(int)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:495</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_afa189422b204c68d154c816ae84fa13c"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#afa189422b204c68d154c816ae84fa13c">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:953</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a26d7f61b89502cff1406f1268ecd1292"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a26d7f61b89502cff1406f1268ecd1292">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::set_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void set_mask(Mask const &amp;mask)</div><div class="ttdoc">Sets the predicate mask, overriding value stored in predicate iterator. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:515</div></div>
<div class="ttc" id="structcutlass_1_1platform_1_1remove__const_html_ac3662947fa50251daf58240a9c798085"><div class="ttname"><a href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">cutlass::platform::remove_const::type</a></div><div class="ttdeci">T type</div><div class="ttdef"><b>Definition:</b> platform.h:351</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a79a6680479608730781b18d8f9ef86fb"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a79a6680479608730781b18d8f9ef86fb">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id)</div><div class="ttdoc">Construct a PredicatedTileIterator with zero threadblock offset. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:465</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorInterleaved_html_a4b39c8accf5d11d6cc8d33aeb2b2e5fe"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorInterleaved.html#a4b39c8accf5d11d6cc8d33aeb2b2e5fe">cutlass::layout::RowMajorInterleaved::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:249</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PitchLinear_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1PitchLinear.html">cutlass::layout::PitchLinear</a></div><div class="ttdoc">Mapping function for pitch-linear memory. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:163</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorInterleaved_html_aa3e25100889b6dfb1a8c1a256e9185ea"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#aa3e25100889b6dfb1a8c1a256e9185ea">cutlass::layout::ColumnMajorInterleaved::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:352</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_aedf4f1a74adbb6948585059359918300"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aedf4f1a74adbb6948585059359918300">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:958</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a77ec03286075f26cb5b3a020e3156745"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a77ec03286075f26cb5b3a020e3156745">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Pointer</a></div><div class="ttdeci">Element * Pointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:393</div></div>
<div class="ttc" id="namespacecutlass_html_a7419519fa453a121dfa5f26bf87318d9"><div class="ttname"><a href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">cutlass::make_Coord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord&lt; 1 &gt; make_Coord(int _0)</div><div class="ttdoc">Helper to make a 2-element coordinate. </div><div class="ttdef"><b>Definition:</b> coord.h:387</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1ColumnMajor_html_a4cc90aa67c4692f0a2cd9f59b8a07997"><div class="ttname"><a href="classcutlass_1_1layout_1_1ColumnMajor.html#a4cc90aa67c4692f0a2cd9f59b8a07997">cutlass::layout::ColumnMajor::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:154</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a38b1509afcb20c5474ea5998f85c6507"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a38b1509afcb20c5474ea5998f85c6507">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::clear_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:696</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_aee0b606c8a3d36df29d4e7a537d3755a"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aee0b606c8a3d36df29d4e7a537d3755a">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:579</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a530090ed803464ad2819c6b8b036b573"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a530090ed803464ad2819c6b8b036b573">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:584</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a42dd3be2ba430549488aa4aff56f5296"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a42dd3be2ba430549488aa4aff56f5296">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:580</div></div>
<div class="ttc" id="classcutlass_1_1AlignedArray_html"><div class="ttname"><a href="classcutlass_1_1AlignedArray.html">cutlass::AlignedArray</a></div><div class="ttdoc">Aligned array type. </div><div class="ttdef"><b>Definition:</b> array.h:511</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a8b56ab072b393e313624c5761676e097"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a8b56ab072b393e313624c5761676e097">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:916</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a81b11f9aa20f81003ddae3a482004ec1"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a81b11f9aa20f81003ddae3a482004ec1">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:765</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a8a9532decd6ea4543b064de8f34cdb67"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a8a9532decd6ea4543b064de8f34cdb67">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::NonConstPointer</a></div><div class="ttdeci">typename platform::remove_const&lt; Element &gt;::type * NonConstPointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:394</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_adc98a00c097c5b93755b182ea493d2c6"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#adc98a00c097c5b93755b182ea493d2c6">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id)</div><div class="ttdoc">Construct a PredicatedTileIterator with zero threadblock offset. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1029</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a3c3f0b4acc1f837edd2352659c594552"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a3c3f0b4acc1f837edd2352659c594552">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:732</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorInterleaved_html_ac827eba11d5fb935273a295d8eb1b972"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorInterleaved.html#ac827eba11d5fb935273a295d8eb1b972">cutlass::layout::RowMajorInterleaved::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:246</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_aa496890ca772ec1ee9ed7afe0651ba07"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aa496890ca772ec1ee9ed7afe0651ba07">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id, TensorCoord const &amp;threadblock_offset)</div><div class="ttdoc">Constructs a TileIterator from its precomputed state, threadblock offset, and thread ID...</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:641</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a15baac20f1c70f0adc6f3f882ba2ef16"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a15baac20f1c70f0adc6f3f882ba2ef16">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:859</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a78b52c1335ed6e5d355b012da983c05a"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a78b52c1335ed6e5d355b012da983c05a">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:298</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a699675b64057208e8ae82f700c7adb6b"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a699675b64057208e8ae82f700c7adb6b">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id, TensorCoord const &amp;threadblock_offset)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1008</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a09f33899cad1084651ac9a558a701a4e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a09f33899cad1084651ac9a558a701a4e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:912</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_af950c284f063cced0e0946d824aff078"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#af950c284f063cced0e0946d824aff078">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:773</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a40cdf0a9b56d2571c87f50d1abcfae73"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a40cdf0a9b56d2571c87f50d1abcfae73">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::clear_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1071</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_ac34969c60025d6206b848433319da4fa"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac34969c60025d6206b848433319da4fa">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:484</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a52c7377c5a2457b3b652e90f2f3a1a53"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a52c7377c5a2457b3b652e90f2f3a1a53">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id)</div><div class="ttdoc">Construct a PredicatedTileIterator with zero threadblock offset. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:848</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a7399a18e6cd90e857f9f6dc3e93ab1e6"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a7399a18e6cd90e857f9f6dc3e93ab1e6">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1097</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a562259e56e4981326c16c96b00b67987"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a562259e56e4981326c16c96b00b67987">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id, TensorCoord const &amp;threadblock_offset)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:221</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a6f363efdc9a85011e1d4ed76a83f5ce9"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a6f363efdc9a85011e1d4ed76a83f5ce9">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::set_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void set_mask(Mask const &amp;mask)</div><div class="ttdoc">Sets the predicate mask, overriding value stored in predicate iterator. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1079</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_aefea1fc075a0f02ef2d6069b379a48ba"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aefea1fc075a0f02ef2d6069b379a48ba">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Fragment</a></div><div class="ttdeci">cutlass::Array&lt; Element, ThreadMap::Iterations::kCount *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object to be loaded or stored. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:408</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8_html_a4787959c38114875acb60e554141860b"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8.html#a4787959c38114875acb60e554141860b">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(Layout const &amp;layout)</div><div class="ttdoc">Construct the Params object given a pitch-linear tensor&amp;#39;s layout. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:811</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a958f75bd15e03403c1517c767bc06b9e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a958f75bd15e03403c1517c767bc06b9e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::get_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void get_mask(Mask &amp;mask)</div><div class="ttdoc">Gets the mask. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:714</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a7712f023dcf819a124a9cb569eda0483"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a7712f023dcf819a124a9cb569eda0483">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Fragment</a></div><div class="ttdeci">cutlass::Array&lt; Element, ThreadMap::Iterations::kCount *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object to be loaded or stored. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:792</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a9cc44093405805682cb4e26a4cd00271"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a9cc44093405805682cb4e26a4cd00271">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:248</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a9e22fa23c9927277741d6bea462f7589"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9e22fa23c9927277741d6bea462f7589">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Mask</a></div><div class="ttdeci">typename UnderlyingIterator::Mask Mask</div><div class="ttdoc">Predicate vector stores mask to guard accesses. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:411</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a9bf65422579611ecaf4275516b6d9e1f"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a9bf65422579611ecaf4275516b6d9e1f">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id, TensorCoord const &amp;threadblock_offset)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:827</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a8a2ddd611de1585295a6f698a8a60022"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a8a2ddd611de1585295a6f698a8a60022">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::get_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void get_mask(Mask &amp;mask)</div><div class="ttdoc">Gets the mask. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:521</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1ColumnMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1ColumnMajor.html">cutlass::layout::ColumnMajor</a></div><div class="ttdoc">Mapping function for column-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:142</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a9f98ff9fa96941ccd8ce149fbaa620f9"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a9f98ff9fa96941ccd8ce149fbaa620f9">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1093</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380_html_a14187b46ff5e13fec953402e0ce1ebac"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380.html#a14187b46ff5e13fec953402e0ce1ebac">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(Layout const &amp;layout)</div><div class="ttdoc">Construct the Params object given a pitch-linear tensor&amp;#39;s layout. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:429</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd_html_a40aa9cc4c64cc49e926ce791326f70bb"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd.html#a40aa9cc4c64cc49e926ce791326f70bb">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:618</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a7ba9103031ca5d3e09867d057417594d"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a7ba9103031ca5d3e09867d057417594d">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::get_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void get_mask(Mask &amp;mask)</div><div class="ttdoc">Gets the mask. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:295</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearShape_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a></div><div class="ttdoc">Template defining a shape used by pitch-linear operators. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:43</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a278a0d69e5ffe2602faac7a29f5f9e74"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a278a0d69e5ffe2602faac7a29f5f9e74">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Mask</a></div><div class="ttdeci">typename UnderlyingIterator::Mask Mask</div><div class="ttdoc">Predicate vector stores mask to guard accesses. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:604</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a052bca4037b27b51e285019ac3b08f2e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a052bca4037b27b51e285019ac3b08f2e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator operator++(int)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:688</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_aa49e242b14b4f482bc6bdd082acfb576"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#aa49e242b14b4f482bc6bdd082acfb576">cutlass::layout::RowMajor::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:59</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af_html_a45e7ac8084883fdf4d84d71afddf45d6"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#a45e7ac8084883fdf4d84d71afddf45d6">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(Layout const &amp;layout)</div><div class="ttdoc">Construct the Params object given a pitch-linear tensor&amp;#39;s layout. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:199</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8_html_ae36baf85b0cc499418c5c0c6622e2b8a"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00ebd1a63351e1085d0b718582ec7b06c8.html#ae36baf85b0cc499418c5c0c6622e2b8a">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:807</div></div>
<div class="ttc" id="namespacecutlass_html_a1c7a9e66ca7b5dc7413ea3b8f349530a"><div class="ttname"><a href="namespacecutlass.html#a1c7a9e66ca7b5dc7413ea3b8f349530a">cutlass::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE half_t &amp; operator++(half_t &amp;lhs)</div><div class="ttdef"><b>Definition:</b> half.h:694</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PitchLinear_html_a1a4b31740e77b3c03925f507650978ea"><div class="ttname"><a href="classcutlass_1_1layout_1_1PitchLinear.html#a1a4b31740e77b3c03925f507650978ea">cutlass::layout::PitchLinear::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:175</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a0a5671614bcf97fda9d6f042a79fe4c2"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a0a5671614bcf97fda9d6f042a79fe4c2">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::set_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void set_mask(Mask const &amp;mask)</div><div class="ttdoc">Sets the predicate mask, overriding value stored in predicate iterator. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:708</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a44b1d56e527e466f8406b19cd51f34a5"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a44b1d56e527e466f8406b19cd51f34a5">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::get_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void get_mask(Mask &amp;mask)</div><div class="ttdoc">Gets the mask. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1083</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a80cdbe77271741f7f60da8af629149f2"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a80cdbe77271741f7f60da8af629149f2">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id, TensorCoord const &amp;threadblock_offset)</div><div class="ttdoc">Constructs a TileIterator from its precomputed state, threadblock offset, and thread ID...</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:448</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html"><div class="ttname"><a href="classcutlass_1_1TensorView.html">cutlass::TensorView&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a9421518aa9c9510dcbe41bda4e90295b"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a9421518aa9c9510dcbe41bda4e90295b">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::AccessType</a></div><div class="ttdeci">typename UnderlyingIterator::AccessType AccessType</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:788</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a8e3ff6897ff18b510d459052e6aa5648"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a8e3ff6897ff18b510d459052e6aa5648">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Fragment</a></div><div class="ttdeci">cutlass::Array&lt; Element, ThreadMap::Iterations::kCount *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object to be loaded or stored. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:182</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a90f8faf720c886784e1383ab1fa6295b"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a90f8faf720c886784e1383ab1fa6295b">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::AccessType</a></div><div class="ttdeci">typename UnderlyingIterator::AccessType AccessType</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:598</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a36163de0c19d00d8f00ea5ef76a7068d"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a36163de0c19d00d8f00ea5ef76a7068d">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:667</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f_html_a565c30dda08950f30fa66b6ca1d0bae1"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f.html#a565c30dda08950f30fa66b6ca1d0bae1">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:988</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a914aca3f9a44a0bd2098d2fa13d59c62"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a914aca3f9a44a0bd2098d2fa13d59c62">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator operator++(int)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:882</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a03832ac3ba57eb81b9cbd3eb63f4af70"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a03832ac3ba57eb81b9cbd3eb63f4af70">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:677</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_aeaaaa921a3fa27f9a181296c71c671e9"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aeaaaa921a3fa27f9a181296c71c671e9">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::enable_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void enable_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:509</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_ab77e9c9d9d7337dea6c27ee24f09ea86"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ab77e9c9d9d7337dea6c27ee24f09ea86">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id)</div><div class="ttdoc">Construct a PredicatedTileIterator with zero threadblock offset. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:658</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_abe79a4ca57e8ad8adbd88c703c75da80"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#abe79a4ca57e8ad8adbd88c703c75da80">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:326</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a612698abfee9f8c3bc71d25646b3987e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a612698abfee9f8c3bc71d25646b3987e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::store</a></div><div class="ttdeci">CUTLASS_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:738</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a6994e38394aac156a8bcf006fe0a86dc"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a6994e38394aac156a8bcf006fe0a86dc">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:161</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a9c35288da31e9d3503aac4afdd17b620"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a9c35288da31e9d3503aac4afdd17b620">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:391</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a799b0d6d28334da4d5f0c16e95813338"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a799b0d6d28334da4d5f0c16e95813338">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Fragment</a></div><div class="ttdeci">cutlass::Array&lt; Element, ThreadMap::Iterations::kCount *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object to be loaded or stored. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:973</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a958a3a54e4af3bbe7e22c8205e2c335f"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a958a3a54e4af3bbe7e22c8205e2c335f">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1051</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_ace4da5baffa2dac199449b90dc486414"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ace4da5baffa2dac199449b90dc486414">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::enable_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void enable_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1075</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a838b764b815761c22bb879e4ceebccf9"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a838b764b815761c22bb879e4ceebccf9">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Pointer</a></div><div class="ttdeci">Element * Pointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:960</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a910b8ba4e409fb7d26739ee3f0c46a36"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a910b8ba4e409fb7d26739ee3f0c46a36">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:772</div></div>
<div class="ttc" id="predicated__tile__access__iterator_8h_html"><div class="ttname"><a href="predicated__tile__access__iterator_8h.html">predicated_tile_access_iterator.h</a></div><div class="ttdoc">Templates calculating the address and predicates to the load of tiles from pitch-linear rank=2 tensor...</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a139515c9d839b495b661d1f7a5a58b48"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a139515c9d839b495b661d1f7a5a58b48">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator &amp; operator++()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:259</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_aa7481efb4a8103be501fd5cb8e0e526e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#aa7481efb4a8103be501fd5cb8e0e526e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:573</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380_html_a234151421c93148ed80209c99415bf7f"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00a6b756b1bcfbb35fe4a3e68ff074e380.html#a234151421c93148ed80209c99415bf7f">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:425</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_afd3e189a510aef0ddceb6f0ceb519d88"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#afd3e189a510aef0ddceb6f0ceb519d88">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::clear_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:283</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_afed2a0c5cfb8f89aaf66a7acaa4e6568"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#afed2a0c5cfb8f89aaf66a7acaa4e6568">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::enable_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void enable_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:702</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_af79b4a0ff3191a3dbdd01afd164fbc68"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#af79b4a0ff3191a3dbdd01afd164fbc68">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Pointer</a></div><div class="ttdeci">Element * Pointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:167</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1PitchLinear_html_a9d1dfd7b6d3b2b651009dcba8f5fd5cd"><div class="ttname"><a href="classcutlass_1_1layout_1_1PitchLinear.html#a9d1dfd7b6d3b2b651009dcba8f5fd5cd">cutlass::layout::PitchLinear::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:172</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a2465bdfb439468c269dd32c441b54a86"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a2465bdfb439468c269dd32c441b54a86">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator operator++(int)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1063</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a414f47df2907cd8e2336b8cb75b58308"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a414f47df2907cd8e2336b8cb75b58308">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:777</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a02237c18b5f4662756ef7f1898a75a2d"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a02237c18b5f4662756ef7f1898a75a2d">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::get_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void get_mask(Mask &amp;mask)</div><div class="ttdoc">Gets the mask. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:902</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_ad1ade679ed38f572252668ecbc3b6df4"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#ad1ade679ed38f572252668ecbc3b6df4">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Fragment</a></div><div class="ttdeci">cutlass::Array&lt; Element, ThreadMap::Iterations::kCount *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object to be loaded or stored. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:601</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a0ac0d4198232bc67245b8f7f3536968e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a0ac0d4198232bc67245b8f7f3536968e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::NonConstPointer</a></div><div class="ttdeci">typename platform::remove_const&lt; Element &gt;::type * NonConstPointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:780</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_a3bf5b6d908482f6d98a483b4a7cafdcb"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a3bf5b6d908482f6d98a483b4a7cafdcb">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::store</a></div><div class="ttdeci">CUTLASS_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:545</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_html"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator.html">cutlass::transform::threadblock::PredicatedTileIterator</a></div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:133</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd_html_ac553ce446cfdc6e4a1ccfe443812564e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___004d0f9b5e19c29acc17bcdc360dafebbd.html#ac553ce446cfdc6e4a1ccfe443812564e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(Layout const &amp;layout)</div><div class="ttdoc">Construct the Params object given a pitch-linear tensor&amp;#39;s layout. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:622</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a73c78020399019ab244400b48f43d7cc"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a73c78020399019ab244400b48f43d7cc">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::clear_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:890</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_ac68f091bc3ef944e3476c838f2225157"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ac68f091bc3ef944e3476c838f2225157">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:160</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a77d8f033b1266f6cd90688b363ec71e4"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a77d8f033b1266f6cd90688b363ec71e4">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:946</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a657aa5f22a2bc0cb7558256a7a8bf36e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a657aa5f22a2bc0cb7558256a7a8bf36e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::store</a></div><div class="ttdeci">CUTLASS_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:922</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af_html_ae0f61aca69c0d05ec21684cd08d040fc"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#ae0f61aca69c0d05ec21684cd08d040fc">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::PredicatedTileIterator</a></div><div class="ttdeci">friend PredicatedTileIterator</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:190</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a113aa25ba6653b2590f3995cccc3b8f1"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a113aa25ba6653b2590f3995cccc3b8f1">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:720</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_ab9d91c62b03d9ac31141967f590efdef"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ab9d91c62b03d9ac31141967f590efdef">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::set_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void set_mask(Mask const &amp;mask)</div><div class="ttdoc">Sets the predicate mask, overriding value stored in predicate iterator. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:898</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a5107ffd9ef9d9016b1f810a0d5ecac07"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a5107ffd9ef9d9016b1f810a0d5ecac07">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1087</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a5761c878c096fe769e76b5dbb951d1f5"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a5761c878c096fe769e76b5dbb951d1f5">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Pointer</a></div><div class="ttdeci">Element * Pointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:779</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorInterleaved_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">cutlass::layout::ColumnMajorInterleaved</a></div><div class="ttdef"><b>Definition:</b> layout/matrix.h:343</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1ColumnMajor_html_a6409273d6b40acb27aff0564eb038336"><div class="ttname"><a href="classcutlass_1_1layout_1_1ColumnMajor.html#a6409273d6b40acb27aff0564eb038336">cutlass::layout::ColumnMajor::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:151</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_aa0273c9e8e274e488164729ca5e83e71"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa0273c9e8e274e488164729ca5e83e71">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::NonConstPointer</a></div><div class="ttdeci">typename platform::remove_const&lt; Element &gt;::type * NonConstPointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:961</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_abd7913223c57a050ba28cb3683094384"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#abd7913223c57a050ba28cb3683094384">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:387</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_aa0670788c8e0ec05f173f6fd5b3e6cc2"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aa0670788c8e0ec05f173f6fd5b3e6cc2">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator operator++(int)</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:275</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a608521f07cd7929c312891b6765d4a0e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a608521f07cd7929c312891b6765d4a0e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:726</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_ac9583e3c6f7046733a0fa5671d8f32be"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ac9583e3c6f7046733a0fa5671d8f32be">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Mask</a></div><div class="ttdeci">typename UnderlyingIterator::Mask Mask</div><div class="ttdoc">Predicate vector stores mask to guard accesses. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:795</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_ad7cac32cc4512e994d4da4e3b8930a1a"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ad7cac32cc4512e994d4da4e3b8930a1a">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:330</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f_html_a7e2f044d616b27b0fad5c4fa060acef9"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___009fd89f6dad84238fd7d63df0a0c0364f.html#a7e2f044d616b27b0fad5c4fa060acef9">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(Layout const &amp;layout)</div><div class="ttdoc">Construct the Params object given a pitch-linear tensor&amp;#39;s layout. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:992</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_a4fc50f92e4b27f31123244caddee61cd"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a4fc50f92e4b27f31123244caddee61cd">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:906</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_aec0b17e3c03da63a798a83ea086b380c"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#aec0b17e3c03da63a798a83ea086b380c">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::NonConstPointer</a></div><div class="ttdeci">typename platform::remove_const&lt; Element &gt;::type * NonConstPointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:168</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_ae61cb62d51d3201d350b4f67556f2748"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ae61cb62d51d3201d350b4f67556f2748">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:474</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a9b108452c6733a2de76de611aab8caf6"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a9b108452c6733a2de76de611aab8caf6">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::enable_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void enable_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:287</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a22505310ada0406477bafd4edfbc1ad3"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a22505310ada0406477bafd4edfbc1ad3">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::AccessType</a></div><div class="ttdeci">typename UnderlyingIterator::AccessType AccessType</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:969</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_a36c915019a92458f22a40fbd0bad8363"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a36c915019a92458f22a40fbd0bad8363">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator(Params const &amp;params, Pointer pointer, TensorCoord extent, int thread_id)</div><div class="ttdoc">Construct a PredicatedTileIterator with zero threadblock offset. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:237</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_ada91bd8779c8636da59b7328de635c15"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#ada91bd8779c8636da59b7328de635c15">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::set_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void set_mask(Mask const &amp;mask)</div><div class="ttdoc">Sets the predicate mask, overriding value stored in predicate iterator. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:291</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_a28785f98e2f9204d29cd430b87c33bdf"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a28785f98e2f9204d29cd430b87c33bdf">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::store</a></div><div class="ttdeci">CUTLASS_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:1103</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_acfd766202c25c0323160a91db74ad82b"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#acfd766202c25c0323160a91db74ad82b">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:386</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af_html_a3f17d7ab9151f7204f34da4642b7b790"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___006a5f2f7a8271031e6cdc5daa5441f2af.html#a3f17d7ab9151f7204f34da4642b7b790">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params()</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:202</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1_html_acd68921055f7b559580922bdaa86232e"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#acd68921055f7b559580922bdaa86232e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:165</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e_html_aa047aa70ae6f0eabebbafa2620692833"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#aa047aa70ae6f0eabebbafa2620692833">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::Mask</a></div><div class="ttdeci">typename UnderlyingIterator::Mask Mask</div><div class="ttdoc">Predicate vector stores mask to guard accesses. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:976</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_ac80e1650d1dca537fb577b5330710057"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#ac80e1650d1dca537fb577b5330710057">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store a fragment to memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:539</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b_html_ae26a5a59ceb0def13f9717340b36a147"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#ae26a5a59ceb0def13f9717340b36a147">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::enable_mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void enable_mask()</div><div class="ttdoc">Clears the predicate set efficiently. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:894</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_html"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator.html">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape, Element, Layout, kAdvanceRank, ThreadMap, AccessType &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9_html_aa8647ab882cf2af303ee89beb641e909"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#aa8647ab882cf2af303ee89beb641e909">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:527</div></div>
<div class="ttc" id="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0_html_a54dc1021b1620142b91ed7ace0282b77"><div class="ttname"><a href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a54dc1021b1620142b91ed7ace0282b77">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::NonConstPointer</a></div><div class="ttdeci">typename platform::remove_const&lt; Element &gt;::type * NonConstPointer</div><div class="ttdef"><b>Definition:</b> transform/threadblock/predicated_tile_iterator.h:587</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1RowMajorInterleaved_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1RowMajorInterleaved.html">cutlass::layout::RowMajorInterleaved</a></div><div class="ttdef"><b>Definition:</b> layout/matrix.h:237</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
