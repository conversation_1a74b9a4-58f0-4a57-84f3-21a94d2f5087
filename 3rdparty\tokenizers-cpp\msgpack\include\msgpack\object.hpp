//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2008-2016 FURUHAS<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_OBJECT_HPP
#define MSGPACK_OBJECT_HPP

#include "msgpack/object_decl.hpp"

#include "msgpack/v1/object.hpp"
#include "msgpack/v2/object.hpp"

#endif // MSGPACK_OBJECT_HPP
