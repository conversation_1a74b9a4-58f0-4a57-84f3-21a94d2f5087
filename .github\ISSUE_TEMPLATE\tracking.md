---
name: "Tracking"
about: A tracking issue that tracks ongoing item in the project
title: '[Tracking] '
labels: ['status: tracking']
assignees: ''

---

<!--

A tracking issue contains a list of action items
that can be executed to complete a feature or fix.

We use tracking issues when we have a clear list of action items
related to feature items as they provide fine-grained
view of action items and provide clarity on what it takes to implement a feature.

When to open a tracking issue: Open a new tracking issue when you have
clear, actionable items (as a rule of thumb, make sure action items
items can be carried through if you are assigned to work on it and
you can provide enough guides to others who plan to work on these actions).
-->


## Overview
<!-- A brief overview of the task  -->



## Action Items
<!-- Please list set of action items to complete -->

- [ ]


## Links to Related Issues and PRs

<!-- Cross link feature requests bug report issues related to the tracking item -->
<!-- When there are new PRs, open up new PRs -->
