<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 16 &gt;, 32, int8_t, layout::RowMajor, int8_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAddSaturate &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1arch.html">arch</a></li><li class="navelem"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html">Mma&lt; gemm::GemmShape&lt; 8, 8, 16 &gt;, 32, int8_t, layout::RowMajor, int8_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAddSaturate &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__2b08bf7357f4869709a6071c15462437.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 16 &gt;, 32, int8_t, layout::RowMajor, int8_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAddSaturate &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Matrix multiply-add operation: S32 = S8 * S8 + S32.  
</p>

<p><code>#include &lt;<a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:aceae9dbca71fcb3bf859218ac4714edd"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#aceae9dbca71fcb3bf859218ac4714edd">Shape</a> = <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;</td></tr>
<tr class="separator:aceae9dbca71fcb3bf859218ac4714edd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6065563ea09cc562acea06ea8f21969"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#ad6065563ea09cc562acea06ea8f21969">ElementA</a> = int8_t</td></tr>
<tr class="separator:ad6065563ea09cc562acea06ea8f21969"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab89f8f7b43d94c518012b79e156e7903"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#ab89f8f7b43d94c518012b79e156e7903">LayoutA</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:ab89f8f7b43d94c518012b79e156e7903"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a115ad2a707ac2b877d1c77ee988b06e2"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a115ad2a707ac2b877d1c77ee988b06e2">FragmentA</a> = Array&lt; int8_t, 4 &gt;</td></tr>
<tr class="separator:a115ad2a707ac2b877d1c77ee988b06e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefeb36c014f23ce2f125c9810203f605"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#aefeb36c014f23ce2f125c9810203f605">ElementB</a> = int8_t</td></tr>
<tr class="separator:aefeb36c014f23ce2f125c9810203f605"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b7706ae320c1afb29a00500ce000d73"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a0b7706ae320c1afb29a00500ce000d73">LayoutB</a> = <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td></tr>
<tr class="separator:a0b7706ae320c1afb29a00500ce000d73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62eabe2936fdb0c0f5c6c3b81ff5ee70"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a62eabe2936fdb0c0f5c6c3b81ff5ee70">FragmentB</a> = Array&lt; int8_t, 4 &gt;</td></tr>
<tr class="separator:a62eabe2936fdb0c0f5c6c3b81ff5ee70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5513c0034c89ecbbc2cb27d0ca56be8"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#aa5513c0034c89ecbbc2cb27d0ca56be8">ElementC</a> = int</td></tr>
<tr class="separator:aa5513c0034c89ecbbc2cb27d0ca56be8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e24efa27ed21ebbb29f9d812f91b8c5"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a9e24efa27ed21ebbb29f9d812f91b8c5">LayoutC</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a9e24efa27ed21ebbb29f9d812f91b8c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b9bc96139177e113193192e29f2ecfb"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a7b9bc96139177e113193192e29f2ecfb">FragmentC</a> = Array&lt; int, 2 &gt;</td></tr>
<tr class="separator:a7b9bc96139177e113193192e29f2ecfb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f265d6ec9dfcfd45f1cc45aa059d660"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a4f265d6ec9dfcfd45f1cc45aa059d660">Operator</a> = OpMultiplyAddSaturate</td></tr>
<tr class="separator:a4f265d6ec9dfcfd45f1cc45aa059d660"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aa4adee271cdf27a7e7c3b1f545e2831d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#aa4adee271cdf27a7e7c3b1f545e2831d">operator()</a> (<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a7b9bc96139177e113193192e29f2ecfb">FragmentC</a> &amp;d, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a115ad2a707ac2b877d1c77ee988b06e2">FragmentA</a> const &amp;a, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a62eabe2936fdb0c0f5c6c3b81ff5ee70">FragmentB</a> const &amp;b, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a7b9bc96139177e113193192e29f2ecfb">FragmentC</a> const &amp;c) const </td></tr>
<tr class="memdesc:aa4adee271cdf27a7e7c3b1f545e2831d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes multiply-add.  <a href="#aa4adee271cdf27a7e7c3b1f545e2831d">More...</a><br /></td></tr>
<tr class="separator:aa4adee271cdf27a7e7c3b1f545e2831d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ad6065563ea09cc562acea06ea8f21969"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#ad6065563ea09cc562acea06ea8f21969">ElementA</a> =  int8_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aefeb36c014f23ce2f125c9810203f605"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#aefeb36c014f23ce2f125c9810203f605">ElementB</a> =  int8_t</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa5513c0034c89ecbbc2cb27d0ca56be8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#aa5513c0034c89ecbbc2cb27d0ca56be8">ElementC</a> =  int</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a115ad2a707ac2b877d1c77ee988b06e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a115ad2a707ac2b877d1c77ee988b06e2">FragmentA</a> =  Array&lt;int8_t, 4&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a62eabe2936fdb0c0f5c6c3b81ff5ee70"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a62eabe2936fdb0c0f5c6c3b81ff5ee70">FragmentB</a> =  Array&lt;int8_t, 4&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7b9bc96139177e113193192e29f2ecfb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a7b9bc96139177e113193192e29f2ecfb">FragmentC</a> =  Array&lt;int, 2&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab89f8f7b43d94c518012b79e156e7903"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#ab89f8f7b43d94c518012b79e156e7903">LayoutA</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0b7706ae320c1afb29a00500ce000d73"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a0b7706ae320c1afb29a00500ce000d73">LayoutB</a> =  <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9e24efa27ed21ebbb29f9d812f91b8c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a9e24efa27ed21ebbb29f9d812f91b8c5">LayoutC</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4f265d6ec9dfcfd45f1cc45aa059d660"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a4f265d6ec9dfcfd45f1cc45aa059d660">Operator</a> =  OpMultiplyAddSaturate</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aceae9dbca71fcb3bf859218ac4714edd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#aceae9dbca71fcb3bf859218ac4714edd">Shape</a> =  <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt;8,8,16&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="aa4adee271cdf27a7e7c3b1f545e2831d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 16 &gt;, 32, int8_t, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, int8_t, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAddSaturate &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a7b9bc96139177e113193192e29f2ecfb">FragmentC</a> &amp;&#160;</td>
          <td class="paramname"><em>d</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a115ad2a707ac2b877d1c77ee988b06e2">FragmentA</a> const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a62eabe2936fdb0c0f5c6c3b81ff5ee70">FragmentB</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0116_01_4_00_0132_00_01int8__8ebae0cbdf333fddfe5c24d35ebe8e02.html#a7b9bc96139177e113193192e29f2ecfb">FragmentC</a> const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
