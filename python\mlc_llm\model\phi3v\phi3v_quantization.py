"""This file specifies how MLC's Llama parameters are quantized using group quantization
or other formats."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import FTQuantize, GroupQuantize, NoQuantize

from .phi3v_model import Phi3VConfig, Phi3VForCausalLM


def group_quant(
    model_config: Phi3VConfig,
    quantization: GroupQuantize,
) -> <PERSON><PERSON>[nn.Module, QuantizeMapping]:
    """Quantize a Phi-architecture model using group quantization."""
    model: nn.Module = Phi3VForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def ft_quant(
    model_config: Phi3VConfig,
    quantization: FTQuantize,
) -> <PERSON><PERSON>[nn.Modu<PERSON>, QuantizeMapping]:
    """Quantize a Phi-architecture model using FasterTransformer quantization."""
    model: nn.Module = Phi3VForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def no_quant(
    model_config: Phi3VConfig,
    quantization: NoQuantize,
) -> Tuple[nn.Module, QuantizeMapping]:
    """Quantize a Phi model without quantization."""
    model: nn.Module = Phi3VForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
