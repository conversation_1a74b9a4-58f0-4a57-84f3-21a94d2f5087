<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::KernelLaunchConfiguration Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1KernelLaunchConfiguration.html">KernelLaunchConfiguration</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1KernelLaunchConfiguration-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::KernelLaunchConfiguration Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure containing the basic launch configuration of a CUDA kernel.  
</p>

<p><code>#include &lt;<a class="el" href="kernel__launch_8h_source.html">kernel_launch.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a726db328ccc8f5e186ff8e7cef568eaa"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1KernelLaunchConfiguration.html#a726db328ccc8f5e186ff8e7cef568eaa">KernelLaunchConfiguration</a> (dim3 _grid=dim3(1, 1, 1), dim3 _block=dim3(1, 1, 1), size_t _dynamic_smem=0)</td></tr>
<tr class="memdesc:a726db328ccc8f5e186ff8e7cef568eaa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs a KernellaunchConfiguration object.  <a href="#a726db328ccc8f5e186ff8e7cef568eaa">More...</a><br /></td></tr>
<tr class="separator:a726db328ccc8f5e186ff8e7cef568eaa"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ab86ba1464dd9c6cd15ae0de4a552201b"><td class="memItemLeft" align="right" valign="top">dim3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1KernelLaunchConfiguration.html#ab86ba1464dd9c6cd15ae0de4a552201b">grid</a></td></tr>
<tr class="memdesc:ab86ba1464dd9c6cd15ae0de4a552201b"><td class="mdescLeft">&#160;</td><td class="mdescRight">CUDA grid dimensions.  <a href="#ab86ba1464dd9c6cd15ae0de4a552201b">More...</a><br /></td></tr>
<tr class="separator:ab86ba1464dd9c6cd15ae0de4a552201b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09535026bf08f94c6940c358d95d1edd"><td class="memItemLeft" align="right" valign="top">dim3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1KernelLaunchConfiguration.html#a09535026bf08f94c6940c358d95d1edd">block</a></td></tr>
<tr class="memdesc:a09535026bf08f94c6940c358d95d1edd"><td class="mdescLeft">&#160;</td><td class="mdescRight">CUDA threablock dimensions.  <a href="#a09535026bf08f94c6940c358d95d1edd">More...</a><br /></td></tr>
<tr class="separator:a09535026bf08f94c6940c358d95d1edd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a6ac693d4284c84301279219623e2bc"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1KernelLaunchConfiguration.html#a4a6ac693d4284c84301279219623e2bc">dynamic_smem</a></td></tr>
<tr class="memdesc:a4a6ac693d4284c84301279219623e2bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bytes of dynamically allocated SMEM in addition to static SMEM.  <a href="#a4a6ac693d4284c84301279219623e2bc">More...</a><br /></td></tr>
<tr class="separator:a4a6ac693d4284c84301279219623e2bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a726db328ccc8f5e186ff8e7cef568eaa"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::KernelLaunchConfiguration::KernelLaunchConfiguration </td>
          <td>(</td>
          <td class="paramtype">dim3&#160;</td>
          <td class="paramname"><em>_grid</em> = <code>dim3(1,1,1)</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">dim3&#160;</td>
          <td class="paramname"><em>_block</em> = <code>dim3(1,1,1)</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>_dynamic_smem</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a09535026bf08f94c6940c358d95d1edd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">dim3 cutlass::KernelLaunchConfiguration::block</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4a6ac693d4284c84301279219623e2bc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">size_t cutlass::KernelLaunchConfiguration::dynamic_smem</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab86ba1464dd9c6cd15ae0de4a552201b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">dim3 cutlass::KernelLaunchConfiguration::grid</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="kernel__launch_8h_source.html">kernel_launch.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
