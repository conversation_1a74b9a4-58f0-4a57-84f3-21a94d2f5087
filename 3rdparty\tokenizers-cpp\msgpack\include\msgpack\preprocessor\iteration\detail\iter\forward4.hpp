# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# if defined(MSGPACK_PP_ITERATION_LIMITS)
#    if !defined(MSGPACK_PP_FILENAME_4)
#        error MSGPACK_PP_ERROR:  depth #4 filename is not defined
#    endif
#    define MSGPACK_PP_VALUE MSGPACK_PP_TUPLE_ELEM(2, 0, MSGPACK_PP_ITERATION_LIMITS)
#    include <msgpack/preprocessor/iteration/detail/bounds/lower4.hpp>
#    define MSGPACK_PP_VALUE MSGPACK_PP_TUPLE_ELEM(2, 1, MSGPACK_PP_ITERATION_LIMITS)
#    include <msgpack/preprocessor/iteration/detail/bounds/upper4.hpp>
#    define MSGPACK_PP_ITERATION_FLAGS_4() 0
#    undef MSGPACK_PP_ITERATION_LIMITS
# elif defined(MSGPACK_PP_ITERATION_PARAMS_4)
#    define MSGPACK_PP_VALUE MSGPACK_PP_ARRAY_ELEM(0, MSGPACK_PP_ITERATION_PARAMS_4)
#    include <msgpack/preprocessor/iteration/detail/bounds/lower4.hpp>
#    define MSGPACK_PP_VALUE MSGPACK_PP_ARRAY_ELEM(1, MSGPACK_PP_ITERATION_PARAMS_4)
#    include <msgpack/preprocessor/iteration/detail/bounds/upper4.hpp>
#    define MSGPACK_PP_FILENAME_4 MSGPACK_PP_ARRAY_ELEM(2, MSGPACK_PP_ITERATION_PARAMS_4)
#    if MSGPACK_PP_ARRAY_SIZE(MSGPACK_PP_ITERATION_PARAMS_4) >= 4
#        define MSGPACK_PP_ITERATION_FLAGS_4() MSGPACK_PP_ARRAY_ELEM(3, MSGPACK_PP_ITERATION_PARAMS_4)
#    else
#        define MSGPACK_PP_ITERATION_FLAGS_4() 0
#    endif
# else
#    error MSGPACK_PP_ERROR:  depth #4 iteration boundaries or filename not defined
# endif
#
# undef MSGPACK_PP_ITERATION_DEPTH
# define MSGPACK_PP_ITERATION_DEPTH() 4
#
# if (MSGPACK_PP_ITERATION_START_4) > (MSGPACK_PP_ITERATION_FINISH_4)
#    include <msgpack/preprocessor/iteration/detail/iter/reverse4.hpp>
# else
#    if MSGPACK_PP_ITERATION_START_4 <= 0 && MSGPACK_PP_ITERATION_FINISH_4 >= 0
#        define MSGPACK_PP_ITERATION_4 0
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 1 && MSGPACK_PP_ITERATION_FINISH_4 >= 1
#        define MSGPACK_PP_ITERATION_4 1
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 2 && MSGPACK_PP_ITERATION_FINISH_4 >= 2
#        define MSGPACK_PP_ITERATION_4 2
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 3 && MSGPACK_PP_ITERATION_FINISH_4 >= 3
#        define MSGPACK_PP_ITERATION_4 3
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 4 && MSGPACK_PP_ITERATION_FINISH_4 >= 4
#        define MSGPACK_PP_ITERATION_4 4
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 5 && MSGPACK_PP_ITERATION_FINISH_4 >= 5
#        define MSGPACK_PP_ITERATION_4 5
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 6 && MSGPACK_PP_ITERATION_FINISH_4 >= 6
#        define MSGPACK_PP_ITERATION_4 6
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 7 && MSGPACK_PP_ITERATION_FINISH_4 >= 7
#        define MSGPACK_PP_ITERATION_4 7
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 8 && MSGPACK_PP_ITERATION_FINISH_4 >= 8
#        define MSGPACK_PP_ITERATION_4 8
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 9 && MSGPACK_PP_ITERATION_FINISH_4 >= 9
#        define MSGPACK_PP_ITERATION_4 9
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 10 && MSGPACK_PP_ITERATION_FINISH_4 >= 10
#        define MSGPACK_PP_ITERATION_4 10
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 11 && MSGPACK_PP_ITERATION_FINISH_4 >= 11
#        define MSGPACK_PP_ITERATION_4 11
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 12 && MSGPACK_PP_ITERATION_FINISH_4 >= 12
#        define MSGPACK_PP_ITERATION_4 12
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 13 && MSGPACK_PP_ITERATION_FINISH_4 >= 13
#        define MSGPACK_PP_ITERATION_4 13
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 14 && MSGPACK_PP_ITERATION_FINISH_4 >= 14
#        define MSGPACK_PP_ITERATION_4 14
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 15 && MSGPACK_PP_ITERATION_FINISH_4 >= 15
#        define MSGPACK_PP_ITERATION_4 15
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 16 && MSGPACK_PP_ITERATION_FINISH_4 >= 16
#        define MSGPACK_PP_ITERATION_4 16
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 17 && MSGPACK_PP_ITERATION_FINISH_4 >= 17
#        define MSGPACK_PP_ITERATION_4 17
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 18 && MSGPACK_PP_ITERATION_FINISH_4 >= 18
#        define MSGPACK_PP_ITERATION_4 18
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 19 && MSGPACK_PP_ITERATION_FINISH_4 >= 19
#        define MSGPACK_PP_ITERATION_4 19
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 20 && MSGPACK_PP_ITERATION_FINISH_4 >= 20
#        define MSGPACK_PP_ITERATION_4 20
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 21 && MSGPACK_PP_ITERATION_FINISH_4 >= 21
#        define MSGPACK_PP_ITERATION_4 21
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 22 && MSGPACK_PP_ITERATION_FINISH_4 >= 22
#        define MSGPACK_PP_ITERATION_4 22
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 23 && MSGPACK_PP_ITERATION_FINISH_4 >= 23
#        define MSGPACK_PP_ITERATION_4 23
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 24 && MSGPACK_PP_ITERATION_FINISH_4 >= 24
#        define MSGPACK_PP_ITERATION_4 24
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 25 && MSGPACK_PP_ITERATION_FINISH_4 >= 25
#        define MSGPACK_PP_ITERATION_4 25
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 26 && MSGPACK_PP_ITERATION_FINISH_4 >= 26
#        define MSGPACK_PP_ITERATION_4 26
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 27 && MSGPACK_PP_ITERATION_FINISH_4 >= 27
#        define MSGPACK_PP_ITERATION_4 27
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 28 && MSGPACK_PP_ITERATION_FINISH_4 >= 28
#        define MSGPACK_PP_ITERATION_4 28
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 29 && MSGPACK_PP_ITERATION_FINISH_4 >= 29
#        define MSGPACK_PP_ITERATION_4 29
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 30 && MSGPACK_PP_ITERATION_FINISH_4 >= 30
#        define MSGPACK_PP_ITERATION_4 30
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 31 && MSGPACK_PP_ITERATION_FINISH_4 >= 31
#        define MSGPACK_PP_ITERATION_4 31
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 32 && MSGPACK_PP_ITERATION_FINISH_4 >= 32
#        define MSGPACK_PP_ITERATION_4 32
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 33 && MSGPACK_PP_ITERATION_FINISH_4 >= 33
#        define MSGPACK_PP_ITERATION_4 33
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 34 && MSGPACK_PP_ITERATION_FINISH_4 >= 34
#        define MSGPACK_PP_ITERATION_4 34
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 35 && MSGPACK_PP_ITERATION_FINISH_4 >= 35
#        define MSGPACK_PP_ITERATION_4 35
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 36 && MSGPACK_PP_ITERATION_FINISH_4 >= 36
#        define MSGPACK_PP_ITERATION_4 36
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 37 && MSGPACK_PP_ITERATION_FINISH_4 >= 37
#        define MSGPACK_PP_ITERATION_4 37
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 38 && MSGPACK_PP_ITERATION_FINISH_4 >= 38
#        define MSGPACK_PP_ITERATION_4 38
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 39 && MSGPACK_PP_ITERATION_FINISH_4 >= 39
#        define MSGPACK_PP_ITERATION_4 39
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 40 && MSGPACK_PP_ITERATION_FINISH_4 >= 40
#        define MSGPACK_PP_ITERATION_4 40
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 41 && MSGPACK_PP_ITERATION_FINISH_4 >= 41
#        define MSGPACK_PP_ITERATION_4 41
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 42 && MSGPACK_PP_ITERATION_FINISH_4 >= 42
#        define MSGPACK_PP_ITERATION_4 42
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 43 && MSGPACK_PP_ITERATION_FINISH_4 >= 43
#        define MSGPACK_PP_ITERATION_4 43
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 44 && MSGPACK_PP_ITERATION_FINISH_4 >= 44
#        define MSGPACK_PP_ITERATION_4 44
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 45 && MSGPACK_PP_ITERATION_FINISH_4 >= 45
#        define MSGPACK_PP_ITERATION_4 45
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 46 && MSGPACK_PP_ITERATION_FINISH_4 >= 46
#        define MSGPACK_PP_ITERATION_4 46
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 47 && MSGPACK_PP_ITERATION_FINISH_4 >= 47
#        define MSGPACK_PP_ITERATION_4 47
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 48 && MSGPACK_PP_ITERATION_FINISH_4 >= 48
#        define MSGPACK_PP_ITERATION_4 48
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 49 && MSGPACK_PP_ITERATION_FINISH_4 >= 49
#        define MSGPACK_PP_ITERATION_4 49
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 50 && MSGPACK_PP_ITERATION_FINISH_4 >= 50
#        define MSGPACK_PP_ITERATION_4 50
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 51 && MSGPACK_PP_ITERATION_FINISH_4 >= 51
#        define MSGPACK_PP_ITERATION_4 51
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 52 && MSGPACK_PP_ITERATION_FINISH_4 >= 52
#        define MSGPACK_PP_ITERATION_4 52
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 53 && MSGPACK_PP_ITERATION_FINISH_4 >= 53
#        define MSGPACK_PP_ITERATION_4 53
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 54 && MSGPACK_PP_ITERATION_FINISH_4 >= 54
#        define MSGPACK_PP_ITERATION_4 54
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 55 && MSGPACK_PP_ITERATION_FINISH_4 >= 55
#        define MSGPACK_PP_ITERATION_4 55
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 56 && MSGPACK_PP_ITERATION_FINISH_4 >= 56
#        define MSGPACK_PP_ITERATION_4 56
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 57 && MSGPACK_PP_ITERATION_FINISH_4 >= 57
#        define MSGPACK_PP_ITERATION_4 57
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 58 && MSGPACK_PP_ITERATION_FINISH_4 >= 58
#        define MSGPACK_PP_ITERATION_4 58
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 59 && MSGPACK_PP_ITERATION_FINISH_4 >= 59
#        define MSGPACK_PP_ITERATION_4 59
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 60 && MSGPACK_PP_ITERATION_FINISH_4 >= 60
#        define MSGPACK_PP_ITERATION_4 60
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 61 && MSGPACK_PP_ITERATION_FINISH_4 >= 61
#        define MSGPACK_PP_ITERATION_4 61
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 62 && MSGPACK_PP_ITERATION_FINISH_4 >= 62
#        define MSGPACK_PP_ITERATION_4 62
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 63 && MSGPACK_PP_ITERATION_FINISH_4 >= 63
#        define MSGPACK_PP_ITERATION_4 63
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 64 && MSGPACK_PP_ITERATION_FINISH_4 >= 64
#        define MSGPACK_PP_ITERATION_4 64
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 65 && MSGPACK_PP_ITERATION_FINISH_4 >= 65
#        define MSGPACK_PP_ITERATION_4 65
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 66 && MSGPACK_PP_ITERATION_FINISH_4 >= 66
#        define MSGPACK_PP_ITERATION_4 66
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 67 && MSGPACK_PP_ITERATION_FINISH_4 >= 67
#        define MSGPACK_PP_ITERATION_4 67
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 68 && MSGPACK_PP_ITERATION_FINISH_4 >= 68
#        define MSGPACK_PP_ITERATION_4 68
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 69 && MSGPACK_PP_ITERATION_FINISH_4 >= 69
#        define MSGPACK_PP_ITERATION_4 69
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 70 && MSGPACK_PP_ITERATION_FINISH_4 >= 70
#        define MSGPACK_PP_ITERATION_4 70
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 71 && MSGPACK_PP_ITERATION_FINISH_4 >= 71
#        define MSGPACK_PP_ITERATION_4 71
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 72 && MSGPACK_PP_ITERATION_FINISH_4 >= 72
#        define MSGPACK_PP_ITERATION_4 72
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 73 && MSGPACK_PP_ITERATION_FINISH_4 >= 73
#        define MSGPACK_PP_ITERATION_4 73
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 74 && MSGPACK_PP_ITERATION_FINISH_4 >= 74
#        define MSGPACK_PP_ITERATION_4 74
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 75 && MSGPACK_PP_ITERATION_FINISH_4 >= 75
#        define MSGPACK_PP_ITERATION_4 75
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 76 && MSGPACK_PP_ITERATION_FINISH_4 >= 76
#        define MSGPACK_PP_ITERATION_4 76
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 77 && MSGPACK_PP_ITERATION_FINISH_4 >= 77
#        define MSGPACK_PP_ITERATION_4 77
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 78 && MSGPACK_PP_ITERATION_FINISH_4 >= 78
#        define MSGPACK_PP_ITERATION_4 78
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 79 && MSGPACK_PP_ITERATION_FINISH_4 >= 79
#        define MSGPACK_PP_ITERATION_4 79
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 80 && MSGPACK_PP_ITERATION_FINISH_4 >= 80
#        define MSGPACK_PP_ITERATION_4 80
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 81 && MSGPACK_PP_ITERATION_FINISH_4 >= 81
#        define MSGPACK_PP_ITERATION_4 81
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 82 && MSGPACK_PP_ITERATION_FINISH_4 >= 82
#        define MSGPACK_PP_ITERATION_4 82
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 83 && MSGPACK_PP_ITERATION_FINISH_4 >= 83
#        define MSGPACK_PP_ITERATION_4 83
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 84 && MSGPACK_PP_ITERATION_FINISH_4 >= 84
#        define MSGPACK_PP_ITERATION_4 84
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 85 && MSGPACK_PP_ITERATION_FINISH_4 >= 85
#        define MSGPACK_PP_ITERATION_4 85
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 86 && MSGPACK_PP_ITERATION_FINISH_4 >= 86
#        define MSGPACK_PP_ITERATION_4 86
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 87 && MSGPACK_PP_ITERATION_FINISH_4 >= 87
#        define MSGPACK_PP_ITERATION_4 87
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 88 && MSGPACK_PP_ITERATION_FINISH_4 >= 88
#        define MSGPACK_PP_ITERATION_4 88
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 89 && MSGPACK_PP_ITERATION_FINISH_4 >= 89
#        define MSGPACK_PP_ITERATION_4 89
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 90 && MSGPACK_PP_ITERATION_FINISH_4 >= 90
#        define MSGPACK_PP_ITERATION_4 90
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 91 && MSGPACK_PP_ITERATION_FINISH_4 >= 91
#        define MSGPACK_PP_ITERATION_4 91
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 92 && MSGPACK_PP_ITERATION_FINISH_4 >= 92
#        define MSGPACK_PP_ITERATION_4 92
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 93 && MSGPACK_PP_ITERATION_FINISH_4 >= 93
#        define MSGPACK_PP_ITERATION_4 93
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 94 && MSGPACK_PP_ITERATION_FINISH_4 >= 94
#        define MSGPACK_PP_ITERATION_4 94
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 95 && MSGPACK_PP_ITERATION_FINISH_4 >= 95
#        define MSGPACK_PP_ITERATION_4 95
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 96 && MSGPACK_PP_ITERATION_FINISH_4 >= 96
#        define MSGPACK_PP_ITERATION_4 96
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 97 && MSGPACK_PP_ITERATION_FINISH_4 >= 97
#        define MSGPACK_PP_ITERATION_4 97
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 98 && MSGPACK_PP_ITERATION_FINISH_4 >= 98
#        define MSGPACK_PP_ITERATION_4 98
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 99 && MSGPACK_PP_ITERATION_FINISH_4 >= 99
#        define MSGPACK_PP_ITERATION_4 99
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 100 && MSGPACK_PP_ITERATION_FINISH_4 >= 100
#        define MSGPACK_PP_ITERATION_4 100
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 101 && MSGPACK_PP_ITERATION_FINISH_4 >= 101
#        define MSGPACK_PP_ITERATION_4 101
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 102 && MSGPACK_PP_ITERATION_FINISH_4 >= 102
#        define MSGPACK_PP_ITERATION_4 102
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 103 && MSGPACK_PP_ITERATION_FINISH_4 >= 103
#        define MSGPACK_PP_ITERATION_4 103
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 104 && MSGPACK_PP_ITERATION_FINISH_4 >= 104
#        define MSGPACK_PP_ITERATION_4 104
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 105 && MSGPACK_PP_ITERATION_FINISH_4 >= 105
#        define MSGPACK_PP_ITERATION_4 105
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 106 && MSGPACK_PP_ITERATION_FINISH_4 >= 106
#        define MSGPACK_PP_ITERATION_4 106
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 107 && MSGPACK_PP_ITERATION_FINISH_4 >= 107
#        define MSGPACK_PP_ITERATION_4 107
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 108 && MSGPACK_PP_ITERATION_FINISH_4 >= 108
#        define MSGPACK_PP_ITERATION_4 108
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 109 && MSGPACK_PP_ITERATION_FINISH_4 >= 109
#        define MSGPACK_PP_ITERATION_4 109
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 110 && MSGPACK_PP_ITERATION_FINISH_4 >= 110
#        define MSGPACK_PP_ITERATION_4 110
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 111 && MSGPACK_PP_ITERATION_FINISH_4 >= 111
#        define MSGPACK_PP_ITERATION_4 111
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 112 && MSGPACK_PP_ITERATION_FINISH_4 >= 112
#        define MSGPACK_PP_ITERATION_4 112
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 113 && MSGPACK_PP_ITERATION_FINISH_4 >= 113
#        define MSGPACK_PP_ITERATION_4 113
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 114 && MSGPACK_PP_ITERATION_FINISH_4 >= 114
#        define MSGPACK_PP_ITERATION_4 114
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 115 && MSGPACK_PP_ITERATION_FINISH_4 >= 115
#        define MSGPACK_PP_ITERATION_4 115
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 116 && MSGPACK_PP_ITERATION_FINISH_4 >= 116
#        define MSGPACK_PP_ITERATION_4 116
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 117 && MSGPACK_PP_ITERATION_FINISH_4 >= 117
#        define MSGPACK_PP_ITERATION_4 117
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 118 && MSGPACK_PP_ITERATION_FINISH_4 >= 118
#        define MSGPACK_PP_ITERATION_4 118
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 119 && MSGPACK_PP_ITERATION_FINISH_4 >= 119
#        define MSGPACK_PP_ITERATION_4 119
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 120 && MSGPACK_PP_ITERATION_FINISH_4 >= 120
#        define MSGPACK_PP_ITERATION_4 120
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 121 && MSGPACK_PP_ITERATION_FINISH_4 >= 121
#        define MSGPACK_PP_ITERATION_4 121
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 122 && MSGPACK_PP_ITERATION_FINISH_4 >= 122
#        define MSGPACK_PP_ITERATION_4 122
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 123 && MSGPACK_PP_ITERATION_FINISH_4 >= 123
#        define MSGPACK_PP_ITERATION_4 123
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 124 && MSGPACK_PP_ITERATION_FINISH_4 >= 124
#        define MSGPACK_PP_ITERATION_4 124
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 125 && MSGPACK_PP_ITERATION_FINISH_4 >= 125
#        define MSGPACK_PP_ITERATION_4 125
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 126 && MSGPACK_PP_ITERATION_FINISH_4 >= 126
#        define MSGPACK_PP_ITERATION_4 126
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 127 && MSGPACK_PP_ITERATION_FINISH_4 >= 127
#        define MSGPACK_PP_ITERATION_4 127
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 128 && MSGPACK_PP_ITERATION_FINISH_4 >= 128
#        define MSGPACK_PP_ITERATION_4 128
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 129 && MSGPACK_PP_ITERATION_FINISH_4 >= 129
#        define MSGPACK_PP_ITERATION_4 129
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 130 && MSGPACK_PP_ITERATION_FINISH_4 >= 130
#        define MSGPACK_PP_ITERATION_4 130
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 131 && MSGPACK_PP_ITERATION_FINISH_4 >= 131
#        define MSGPACK_PP_ITERATION_4 131
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 132 && MSGPACK_PP_ITERATION_FINISH_4 >= 132
#        define MSGPACK_PP_ITERATION_4 132
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 133 && MSGPACK_PP_ITERATION_FINISH_4 >= 133
#        define MSGPACK_PP_ITERATION_4 133
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 134 && MSGPACK_PP_ITERATION_FINISH_4 >= 134
#        define MSGPACK_PP_ITERATION_4 134
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 135 && MSGPACK_PP_ITERATION_FINISH_4 >= 135
#        define MSGPACK_PP_ITERATION_4 135
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 136 && MSGPACK_PP_ITERATION_FINISH_4 >= 136
#        define MSGPACK_PP_ITERATION_4 136
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 137 && MSGPACK_PP_ITERATION_FINISH_4 >= 137
#        define MSGPACK_PP_ITERATION_4 137
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 138 && MSGPACK_PP_ITERATION_FINISH_4 >= 138
#        define MSGPACK_PP_ITERATION_4 138
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 139 && MSGPACK_PP_ITERATION_FINISH_4 >= 139
#        define MSGPACK_PP_ITERATION_4 139
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 140 && MSGPACK_PP_ITERATION_FINISH_4 >= 140
#        define MSGPACK_PP_ITERATION_4 140
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 141 && MSGPACK_PP_ITERATION_FINISH_4 >= 141
#        define MSGPACK_PP_ITERATION_4 141
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 142 && MSGPACK_PP_ITERATION_FINISH_4 >= 142
#        define MSGPACK_PP_ITERATION_4 142
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 143 && MSGPACK_PP_ITERATION_FINISH_4 >= 143
#        define MSGPACK_PP_ITERATION_4 143
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 144 && MSGPACK_PP_ITERATION_FINISH_4 >= 144
#        define MSGPACK_PP_ITERATION_4 144
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 145 && MSGPACK_PP_ITERATION_FINISH_4 >= 145
#        define MSGPACK_PP_ITERATION_4 145
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 146 && MSGPACK_PP_ITERATION_FINISH_4 >= 146
#        define MSGPACK_PP_ITERATION_4 146
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 147 && MSGPACK_PP_ITERATION_FINISH_4 >= 147
#        define MSGPACK_PP_ITERATION_4 147
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 148 && MSGPACK_PP_ITERATION_FINISH_4 >= 148
#        define MSGPACK_PP_ITERATION_4 148
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 149 && MSGPACK_PP_ITERATION_FINISH_4 >= 149
#        define MSGPACK_PP_ITERATION_4 149
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 150 && MSGPACK_PP_ITERATION_FINISH_4 >= 150
#        define MSGPACK_PP_ITERATION_4 150
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 151 && MSGPACK_PP_ITERATION_FINISH_4 >= 151
#        define MSGPACK_PP_ITERATION_4 151
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 152 && MSGPACK_PP_ITERATION_FINISH_4 >= 152
#        define MSGPACK_PP_ITERATION_4 152
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 153 && MSGPACK_PP_ITERATION_FINISH_4 >= 153
#        define MSGPACK_PP_ITERATION_4 153
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 154 && MSGPACK_PP_ITERATION_FINISH_4 >= 154
#        define MSGPACK_PP_ITERATION_4 154
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 155 && MSGPACK_PP_ITERATION_FINISH_4 >= 155
#        define MSGPACK_PP_ITERATION_4 155
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 156 && MSGPACK_PP_ITERATION_FINISH_4 >= 156
#        define MSGPACK_PP_ITERATION_4 156
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 157 && MSGPACK_PP_ITERATION_FINISH_4 >= 157
#        define MSGPACK_PP_ITERATION_4 157
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 158 && MSGPACK_PP_ITERATION_FINISH_4 >= 158
#        define MSGPACK_PP_ITERATION_4 158
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 159 && MSGPACK_PP_ITERATION_FINISH_4 >= 159
#        define MSGPACK_PP_ITERATION_4 159
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 160 && MSGPACK_PP_ITERATION_FINISH_4 >= 160
#        define MSGPACK_PP_ITERATION_4 160
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 161 && MSGPACK_PP_ITERATION_FINISH_4 >= 161
#        define MSGPACK_PP_ITERATION_4 161
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 162 && MSGPACK_PP_ITERATION_FINISH_4 >= 162
#        define MSGPACK_PP_ITERATION_4 162
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 163 && MSGPACK_PP_ITERATION_FINISH_4 >= 163
#        define MSGPACK_PP_ITERATION_4 163
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 164 && MSGPACK_PP_ITERATION_FINISH_4 >= 164
#        define MSGPACK_PP_ITERATION_4 164
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 165 && MSGPACK_PP_ITERATION_FINISH_4 >= 165
#        define MSGPACK_PP_ITERATION_4 165
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 166 && MSGPACK_PP_ITERATION_FINISH_4 >= 166
#        define MSGPACK_PP_ITERATION_4 166
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 167 && MSGPACK_PP_ITERATION_FINISH_4 >= 167
#        define MSGPACK_PP_ITERATION_4 167
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 168 && MSGPACK_PP_ITERATION_FINISH_4 >= 168
#        define MSGPACK_PP_ITERATION_4 168
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 169 && MSGPACK_PP_ITERATION_FINISH_4 >= 169
#        define MSGPACK_PP_ITERATION_4 169
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 170 && MSGPACK_PP_ITERATION_FINISH_4 >= 170
#        define MSGPACK_PP_ITERATION_4 170
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 171 && MSGPACK_PP_ITERATION_FINISH_4 >= 171
#        define MSGPACK_PP_ITERATION_4 171
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 172 && MSGPACK_PP_ITERATION_FINISH_4 >= 172
#        define MSGPACK_PP_ITERATION_4 172
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 173 && MSGPACK_PP_ITERATION_FINISH_4 >= 173
#        define MSGPACK_PP_ITERATION_4 173
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 174 && MSGPACK_PP_ITERATION_FINISH_4 >= 174
#        define MSGPACK_PP_ITERATION_4 174
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 175 && MSGPACK_PP_ITERATION_FINISH_4 >= 175
#        define MSGPACK_PP_ITERATION_4 175
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 176 && MSGPACK_PP_ITERATION_FINISH_4 >= 176
#        define MSGPACK_PP_ITERATION_4 176
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 177 && MSGPACK_PP_ITERATION_FINISH_4 >= 177
#        define MSGPACK_PP_ITERATION_4 177
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 178 && MSGPACK_PP_ITERATION_FINISH_4 >= 178
#        define MSGPACK_PP_ITERATION_4 178
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 179 && MSGPACK_PP_ITERATION_FINISH_4 >= 179
#        define MSGPACK_PP_ITERATION_4 179
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 180 && MSGPACK_PP_ITERATION_FINISH_4 >= 180
#        define MSGPACK_PP_ITERATION_4 180
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 181 && MSGPACK_PP_ITERATION_FINISH_4 >= 181
#        define MSGPACK_PP_ITERATION_4 181
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 182 && MSGPACK_PP_ITERATION_FINISH_4 >= 182
#        define MSGPACK_PP_ITERATION_4 182
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 183 && MSGPACK_PP_ITERATION_FINISH_4 >= 183
#        define MSGPACK_PP_ITERATION_4 183
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 184 && MSGPACK_PP_ITERATION_FINISH_4 >= 184
#        define MSGPACK_PP_ITERATION_4 184
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 185 && MSGPACK_PP_ITERATION_FINISH_4 >= 185
#        define MSGPACK_PP_ITERATION_4 185
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 186 && MSGPACK_PP_ITERATION_FINISH_4 >= 186
#        define MSGPACK_PP_ITERATION_4 186
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 187 && MSGPACK_PP_ITERATION_FINISH_4 >= 187
#        define MSGPACK_PP_ITERATION_4 187
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 188 && MSGPACK_PP_ITERATION_FINISH_4 >= 188
#        define MSGPACK_PP_ITERATION_4 188
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 189 && MSGPACK_PP_ITERATION_FINISH_4 >= 189
#        define MSGPACK_PP_ITERATION_4 189
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 190 && MSGPACK_PP_ITERATION_FINISH_4 >= 190
#        define MSGPACK_PP_ITERATION_4 190
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 191 && MSGPACK_PP_ITERATION_FINISH_4 >= 191
#        define MSGPACK_PP_ITERATION_4 191
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 192 && MSGPACK_PP_ITERATION_FINISH_4 >= 192
#        define MSGPACK_PP_ITERATION_4 192
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 193 && MSGPACK_PP_ITERATION_FINISH_4 >= 193
#        define MSGPACK_PP_ITERATION_4 193
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 194 && MSGPACK_PP_ITERATION_FINISH_4 >= 194
#        define MSGPACK_PP_ITERATION_4 194
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 195 && MSGPACK_PP_ITERATION_FINISH_4 >= 195
#        define MSGPACK_PP_ITERATION_4 195
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 196 && MSGPACK_PP_ITERATION_FINISH_4 >= 196
#        define MSGPACK_PP_ITERATION_4 196
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 197 && MSGPACK_PP_ITERATION_FINISH_4 >= 197
#        define MSGPACK_PP_ITERATION_4 197
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 198 && MSGPACK_PP_ITERATION_FINISH_4 >= 198
#        define MSGPACK_PP_ITERATION_4 198
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 199 && MSGPACK_PP_ITERATION_FINISH_4 >= 199
#        define MSGPACK_PP_ITERATION_4 199
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 200 && MSGPACK_PP_ITERATION_FINISH_4 >= 200
#        define MSGPACK_PP_ITERATION_4 200
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 201 && MSGPACK_PP_ITERATION_FINISH_4 >= 201
#        define MSGPACK_PP_ITERATION_4 201
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 202 && MSGPACK_PP_ITERATION_FINISH_4 >= 202
#        define MSGPACK_PP_ITERATION_4 202
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 203 && MSGPACK_PP_ITERATION_FINISH_4 >= 203
#        define MSGPACK_PP_ITERATION_4 203
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 204 && MSGPACK_PP_ITERATION_FINISH_4 >= 204
#        define MSGPACK_PP_ITERATION_4 204
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 205 && MSGPACK_PP_ITERATION_FINISH_4 >= 205
#        define MSGPACK_PP_ITERATION_4 205
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 206 && MSGPACK_PP_ITERATION_FINISH_4 >= 206
#        define MSGPACK_PP_ITERATION_4 206
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 207 && MSGPACK_PP_ITERATION_FINISH_4 >= 207
#        define MSGPACK_PP_ITERATION_4 207
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 208 && MSGPACK_PP_ITERATION_FINISH_4 >= 208
#        define MSGPACK_PP_ITERATION_4 208
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 209 && MSGPACK_PP_ITERATION_FINISH_4 >= 209
#        define MSGPACK_PP_ITERATION_4 209
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 210 && MSGPACK_PP_ITERATION_FINISH_4 >= 210
#        define MSGPACK_PP_ITERATION_4 210
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 211 && MSGPACK_PP_ITERATION_FINISH_4 >= 211
#        define MSGPACK_PP_ITERATION_4 211
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 212 && MSGPACK_PP_ITERATION_FINISH_4 >= 212
#        define MSGPACK_PP_ITERATION_4 212
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 213 && MSGPACK_PP_ITERATION_FINISH_4 >= 213
#        define MSGPACK_PP_ITERATION_4 213
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 214 && MSGPACK_PP_ITERATION_FINISH_4 >= 214
#        define MSGPACK_PP_ITERATION_4 214
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 215 && MSGPACK_PP_ITERATION_FINISH_4 >= 215
#        define MSGPACK_PP_ITERATION_4 215
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 216 && MSGPACK_PP_ITERATION_FINISH_4 >= 216
#        define MSGPACK_PP_ITERATION_4 216
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 217 && MSGPACK_PP_ITERATION_FINISH_4 >= 217
#        define MSGPACK_PP_ITERATION_4 217
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 218 && MSGPACK_PP_ITERATION_FINISH_4 >= 218
#        define MSGPACK_PP_ITERATION_4 218
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 219 && MSGPACK_PP_ITERATION_FINISH_4 >= 219
#        define MSGPACK_PP_ITERATION_4 219
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 220 && MSGPACK_PP_ITERATION_FINISH_4 >= 220
#        define MSGPACK_PP_ITERATION_4 220
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 221 && MSGPACK_PP_ITERATION_FINISH_4 >= 221
#        define MSGPACK_PP_ITERATION_4 221
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 222 && MSGPACK_PP_ITERATION_FINISH_4 >= 222
#        define MSGPACK_PP_ITERATION_4 222
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 223 && MSGPACK_PP_ITERATION_FINISH_4 >= 223
#        define MSGPACK_PP_ITERATION_4 223
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 224 && MSGPACK_PP_ITERATION_FINISH_4 >= 224
#        define MSGPACK_PP_ITERATION_4 224
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 225 && MSGPACK_PP_ITERATION_FINISH_4 >= 225
#        define MSGPACK_PP_ITERATION_4 225
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 226 && MSGPACK_PP_ITERATION_FINISH_4 >= 226
#        define MSGPACK_PP_ITERATION_4 226
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 227 && MSGPACK_PP_ITERATION_FINISH_4 >= 227
#        define MSGPACK_PP_ITERATION_4 227
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 228 && MSGPACK_PP_ITERATION_FINISH_4 >= 228
#        define MSGPACK_PP_ITERATION_4 228
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 229 && MSGPACK_PP_ITERATION_FINISH_4 >= 229
#        define MSGPACK_PP_ITERATION_4 229
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 230 && MSGPACK_PP_ITERATION_FINISH_4 >= 230
#        define MSGPACK_PP_ITERATION_4 230
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 231 && MSGPACK_PP_ITERATION_FINISH_4 >= 231
#        define MSGPACK_PP_ITERATION_4 231
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 232 && MSGPACK_PP_ITERATION_FINISH_4 >= 232
#        define MSGPACK_PP_ITERATION_4 232
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 233 && MSGPACK_PP_ITERATION_FINISH_4 >= 233
#        define MSGPACK_PP_ITERATION_4 233
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 234 && MSGPACK_PP_ITERATION_FINISH_4 >= 234
#        define MSGPACK_PP_ITERATION_4 234
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 235 && MSGPACK_PP_ITERATION_FINISH_4 >= 235
#        define MSGPACK_PP_ITERATION_4 235
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 236 && MSGPACK_PP_ITERATION_FINISH_4 >= 236
#        define MSGPACK_PP_ITERATION_4 236
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 237 && MSGPACK_PP_ITERATION_FINISH_4 >= 237
#        define MSGPACK_PP_ITERATION_4 237
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 238 && MSGPACK_PP_ITERATION_FINISH_4 >= 238
#        define MSGPACK_PP_ITERATION_4 238
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 239 && MSGPACK_PP_ITERATION_FINISH_4 >= 239
#        define MSGPACK_PP_ITERATION_4 239
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 240 && MSGPACK_PP_ITERATION_FINISH_4 >= 240
#        define MSGPACK_PP_ITERATION_4 240
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 241 && MSGPACK_PP_ITERATION_FINISH_4 >= 241
#        define MSGPACK_PP_ITERATION_4 241
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 242 && MSGPACK_PP_ITERATION_FINISH_4 >= 242
#        define MSGPACK_PP_ITERATION_4 242
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 243 && MSGPACK_PP_ITERATION_FINISH_4 >= 243
#        define MSGPACK_PP_ITERATION_4 243
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 244 && MSGPACK_PP_ITERATION_FINISH_4 >= 244
#        define MSGPACK_PP_ITERATION_4 244
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 245 && MSGPACK_PP_ITERATION_FINISH_4 >= 245
#        define MSGPACK_PP_ITERATION_4 245
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 246 && MSGPACK_PP_ITERATION_FINISH_4 >= 246
#        define MSGPACK_PP_ITERATION_4 246
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 247 && MSGPACK_PP_ITERATION_FINISH_4 >= 247
#        define MSGPACK_PP_ITERATION_4 247
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 248 && MSGPACK_PP_ITERATION_FINISH_4 >= 248
#        define MSGPACK_PP_ITERATION_4 248
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 249 && MSGPACK_PP_ITERATION_FINISH_4 >= 249
#        define MSGPACK_PP_ITERATION_4 249
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 250 && MSGPACK_PP_ITERATION_FINISH_4 >= 250
#        define MSGPACK_PP_ITERATION_4 250
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 251 && MSGPACK_PP_ITERATION_FINISH_4 >= 251
#        define MSGPACK_PP_ITERATION_4 251
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 252 && MSGPACK_PP_ITERATION_FINISH_4 >= 252
#        define MSGPACK_PP_ITERATION_4 252
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 253 && MSGPACK_PP_ITERATION_FINISH_4 >= 253
#        define MSGPACK_PP_ITERATION_4 253
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 254 && MSGPACK_PP_ITERATION_FINISH_4 >= 254
#        define MSGPACK_PP_ITERATION_4 254
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 255 && MSGPACK_PP_ITERATION_FINISH_4 >= 255
#        define MSGPACK_PP_ITERATION_4 255
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
#    if MSGPACK_PP_ITERATION_START_4 <= 256 && MSGPACK_PP_ITERATION_FINISH_4 >= 256
#        define MSGPACK_PP_ITERATION_4 256
#        include MSGPACK_PP_FILENAME_4
#        undef MSGPACK_PP_ITERATION_4
#    endif
# endif
#
# undef MSGPACK_PP_ITERATION_DEPTH
# define MSGPACK_PP_ITERATION_DEPTH() 3
#
# undef MSGPACK_PP_ITERATION_START_4
# undef MSGPACK_PP_ITERATION_FINISH_4
# undef MSGPACK_PP_FILENAME_4
#
# undef MSGPACK_PP_ITERATION_FLAGS_4
# undef MSGPACK_PP_ITERATION_PARAMS_4
