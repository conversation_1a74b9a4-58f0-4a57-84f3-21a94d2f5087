<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::Coord&lt; Rank_, Index_, LongIndex_ &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1Coord.html">Coord</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1Coord-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::Coord&lt; Rank_, Index_, LongIndex_ &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Statically-sized array specifying Coords within a tensor.  
</p>

<p><code>#include &lt;<a class="el" href="coord_8h_source.html">coord.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a7a89e5661ef391dd9f4fe81f0c982b75"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> = Index_</td></tr>
<tr class="memdesc:a7a89e5661ef391dd9f4fe81f0c982b75"><td class="mdescLeft">&#160;</td><td class="mdescRight">Index type used to store elements.  <a href="#a7a89e5661ef391dd9f4fe81f0c982b75">More...</a><br /></td></tr>
<tr class="separator:a7a89e5661ef391dd9f4fe81f0c982b75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab61db7c2bfacaf0b7ce465e70d48c44f"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> = LongIndex_</td></tr>
<tr class="memdesc:ab61db7c2bfacaf0b7ce465e70d48c44f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type used to represent linear offsets.  <a href="#ab61db7c2bfacaf0b7ce465e70d48c44f">More...</a><br /></td></tr>
<tr class="separator:ab61db7c2bfacaf0b7ce465e70d48c44f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a5281db2419b5567db4265dead7ac02cc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a5281db2419b5567db4265dead7ac02cc">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> value=<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>(0))</td></tr>
<tr class="memdesc:a5281db2419b5567db4265dead7ac02cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default ctor initializes uniformly.  <a href="#a5281db2419b5567db4265dead7ac02cc">More...</a><br /></td></tr>
<tr class="separator:a5281db2419b5567db4265dead7ac02cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7094975a4b7471315ca083ae575030a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ab7094975a4b7471315ca083ae575030a">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const (&amp;_idx)[<a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a>])</td></tr>
<tr class="memdesc:ab7094975a4b7471315ca083ae575030a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs from an array of integers.  <a href="#ab7094975a4b7471315ca083ae575030a">More...</a><br /></td></tr>
<tr class="separator:ab7094975a4b7471315ca083ae575030a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42aefbb547e39b8cc7267c58a610c147"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a42aefbb547e39b8cc7267c58a610c147">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; <a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a>, <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>, <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> &gt; const &amp;coord)</td></tr>
<tr class="memdesc:a42aefbb547e39b8cc7267c58a610c147"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="#a42aefbb547e39b8cc7267c58a610c147">More...</a><br /></td></tr>
<tr class="separator:a42aefbb547e39b8cc7267c58a610c147"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a329f97d4a09ef34e8470fe55800871f8"><td class="memTemplParams" colspan="2">template&lt;int Slice&gt; </td></tr>
<tr class="memitem:a329f97d4a09ef34e8470fe55800871f8"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Slice &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a329f97d4a09ef34e8470fe55800871f8">slice</a> (int start=0, <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> identity=0) const </td></tr>
<tr class="separator:a329f97d4a09ef34e8470fe55800871f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae89e8a9fa3f07308f8938052ef1aa1fb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ae89e8a9fa3f07308f8938052ef1aa1fb">min_dim_index</a> () const </td></tr>
<tr class="memdesc:ae89e8a9fa3f07308f8938052ef1aa1fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the index of the dimension with least value.  <a href="#ae89e8a9fa3f07308f8938052ef1aa1fb">More...</a><br /></td></tr>
<tr class="separator:ae89e8a9fa3f07308f8938052ef1aa1fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe58b7c8f153a6029c2adc173f340fe0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#abe58b7c8f153a6029c2adc173f340fe0">max_dim_index</a> () const </td></tr>
<tr class="memdesc:abe58b7c8f153a6029c2adc173f340fe0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the index of the dimension with greatest value.  <a href="#abe58b7c8f153a6029c2adc173f340fe0">More...</a><br /></td></tr>
<tr class="separator:abe58b7c8f153a6029c2adc173f340fe0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88096d051dd05111cf265a011a89f7f6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a88096d051dd05111cf265a011a89f7f6">operator bool</a> () const </td></tr>
<tr class="memdesc:a88096d051dd05111cf265a011a89f7f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> is non-zero.  <a href="#a88096d051dd05111cf265a011a89f7f6">More...</a><br /></td></tr>
<tr class="separator:a88096d051dd05111cf265a011a89f7f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa733c6fae0da553053530cba2dddcaa0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#aa733c6fae0da553053530cba2dddcaa0">operator!</a> () const </td></tr>
<tr class="memdesc:aa733c6fae0da553053530cba2dddcaa0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> is uniformly zero.  <a href="#aa733c6fae0da553053530cba2dddcaa0">More...</a><br /></td></tr>
<tr class="separator:aa733c6fae0da553053530cba2dddcaa0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec4c529a728118c0df6a3f527daba746"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#aec4c529a728118c0df6a3f527daba746">operator+</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:aec4c529a728118c0df6a3f527daba746"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise addition.  <a href="#aec4c529a728118c0df6a3f527daba746">More...</a><br /></td></tr>
<tr class="separator:aec4c529a728118c0df6a3f527daba746"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e1a706629eae28128230a0fa34b84a0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a2e1a706629eae28128230a0fa34b84a0">operator-</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:a2e1a706629eae28128230a0fa34b84a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise subtraction.  <a href="#a2e1a706629eae28128230a0fa34b84a0">More...</a><br /></td></tr>
<tr class="separator:a2e1a706629eae28128230a0fa34b84a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6b5fd8d0e5cb856d363fbff9a5b89dd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ac6b5fd8d0e5cb856d363fbff9a5b89dd">operator*</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:ac6b5fd8d0e5cb856d363fbff9a5b89dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise multiplication.  <a href="#ac6b5fd8d0e5cb856d363fbff9a5b89dd">More...</a><br /></td></tr>
<tr class="separator:ac6b5fd8d0e5cb856d363fbff9a5b89dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ae9f189d1a7a5ce7bb5e4416559c79f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a6ae9f189d1a7a5ce7bb5e4416559c79f">operator/</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:a6ae9f189d1a7a5ce7bb5e4416559c79f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise division.  <a href="#a6ae9f189d1a7a5ce7bb5e4416559c79f">More...</a><br /></td></tr>
<tr class="separator:a6ae9f189d1a7a5ce7bb5e4416559c79f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb799faf60a17b708d0802f9e23c812f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#acb799faf60a17b708d0802f9e23c812f">operator+=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:acb799faf60a17b708d0802f9e23c812f"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place addition.  <a href="#acb799faf60a17b708d0802f9e23c812f">More...</a><br /></td></tr>
<tr class="separator:acb799faf60a17b708d0802f9e23c812f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15ac170c861b34d418432aeb62ea86e0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a15ac170c861b34d418432aeb62ea86e0">operator-=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:a15ac170c861b34d418432aeb62ea86e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place subtraction.  <a href="#a15ac170c861b34d418432aeb62ea86e0">More...</a><br /></td></tr>
<tr class="separator:a15ac170c861b34d418432aeb62ea86e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00e618bc944d355badf67c0edd791412"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a00e618bc944d355badf67c0edd791412">operator*=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:a00e618bc944d355badf67c0edd791412"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place multiplication.  <a href="#a00e618bc944d355badf67c0edd791412">More...</a><br /></td></tr>
<tr class="separator:a00e618bc944d355badf67c0edd791412"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af515e669363986dbbd60951ea6b69e14"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#af515e669363986dbbd60951ea6b69e14">operator/=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:af515e669363986dbbd60951ea6b69e14"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place division.  <a href="#af515e669363986dbbd60951ea6b69e14">More...</a><br /></td></tr>
<tr class="separator:af515e669363986dbbd60951ea6b69e14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6eee93e5fdbe147f751ec108b28275a1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a6eee93e5fdbe147f751ec108b28275a1">operator[]</a> (int dim)</td></tr>
<tr class="memdesc:a6eee93e5fdbe147f751ec108b28275a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Member access operator.  <a href="#a6eee93e5fdbe147f751ec108b28275a1">More...</a><br /></td></tr>
<tr class="separator:a6eee93e5fdbe147f751ec108b28275a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accf5689f0d6a6f91965bff0cfd9ec296"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#accf5689f0d6a6f91965bff0cfd9ec296">operator[]</a> (int dim) const </td></tr>
<tr class="memdesc:accf5689f0d6a6f91965bff0cfd9ec296"><td class="mdescLeft">&#160;</td><td class="mdescRight">Member access operator.  <a href="#accf5689f0d6a6f91965bff0cfd9ec296">More...</a><br /></td></tr>
<tr class="separator:accf5689f0d6a6f91965bff0cfd9ec296"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a057a417a4d4a6e2f69e0b55a6f7ee902"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a057a417a4d4a6e2f69e0b55a6f7ee902">dot</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b, <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> <a class="el" href="structcutlass_1_1Coord.html#a49bb1a68198bd4c520d15efe3e84f757">sum</a>=<a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>(0)) const </td></tr>
<tr class="memdesc:a057a417a4d4a6e2f69e0b55a6f7ee902"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes the dot product with anotherCoord object.  <a href="#a057a417a4d4a6e2f69e0b55a6f7ee902">More...</a><br /></td></tr>
<tr class="separator:a057a417a4d4a6e2f69e0b55a6f7ee902"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a65128c86b236cd2bea875b85a34bc1"><td class="memTemplParams" colspan="2">template&lt;int Dim&gt; </td></tr>
<tr class="memitem:a8a65128c86b236cd2bea875b85a34bc1"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a> ()</td></tr>
<tr class="memdesc:a8a65128c86b236cd2bea875b85a34bc1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the index of a given <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> element.  <a href="#a8a65128c86b236cd2bea875b85a34bc1">More...</a><br /></td></tr>
<tr class="separator:a8a65128c86b236cd2bea875b85a34bc1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a358dde78a1c2105a9aeb4adee8bb3d2d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a358dde78a1c2105a9aeb4adee8bb3d2d">at</a> (int dim)</td></tr>
<tr class="memdesc:a358dde78a1c2105a9aeb4adee8bb3d2d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access via index; may limit unrolling potential.  <a href="#a358dde78a1c2105a9aeb4adee8bb3d2d">More...</a><br /></td></tr>
<tr class="separator:a358dde78a1c2105a9aeb4adee8bb3d2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7379275d7431ead927af7966b6fa0ec"><td class="memTemplParams" colspan="2">template&lt;int Dim&gt; </td></tr>
<tr class="memitem:ac7379275d7431ead927af7966b6fa0ec"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ac7379275d7431ead927af7966b6fa0ec">at</a> () const </td></tr>
<tr class="memdesc:ac7379275d7431ead927af7966b6fa0ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the index of a given <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> element.  <a href="#ac7379275d7431ead927af7966b6fa0ec">More...</a><br /></td></tr>
<tr class="separator:ac7379275d7431ead927af7966b6fa0ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06046c22b877abfb277d3f0fe4f8578a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a06046c22b877abfb277d3f0fe4f8578a">at</a> (int dim) const </td></tr>
<tr class="memdesc:a06046c22b877abfb277d3f0fe4f8578a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access via index; may limit unrolling potential.  <a href="#a06046c22b877abfb277d3f0fe4f8578a">More...</a><br /></td></tr>
<tr class="separator:a06046c22b877abfb277d3f0fe4f8578a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8424ccd74e7e0ff1cf358ef571779cba"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8424ccd74e7e0ff1cf358ef571779cba">operator==</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:a8424ccd74e7e0ff1cf358ef571779cba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determines if two Coord&lt;&gt; objects are equal.  <a href="#a8424ccd74e7e0ff1cf358ef571779cba">More...</a><br /></td></tr>
<tr class="separator:a8424ccd74e7e0ff1cf358ef571779cba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8183b9203a213d4b6381ad7dc120deea"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8183b9203a213d4b6381ad7dc120deea">operator!=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:a8183b9203a213d4b6381ad7dc120deea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not equal.  <a href="#a8183b9203a213d4b6381ad7dc120deea">More...</a><br /></td></tr>
<tr class="separator:a8183b9203a213d4b6381ad7dc120deea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40e145063833155c800b38f82cee7461"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a40e145063833155c800b38f82cee7461">clamp</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;max, <a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;min=<a class="el" href="structcutlass_1_1Coord.html">Coord</a>())</td></tr>
<tr class="memdesc:a40e145063833155c800b38f82cee7461"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clamps a coordinate to a range specified by maximum and minimum values.  <a href="#a40e145063833155c800b38f82cee7461">More...</a><br /></td></tr>
<tr class="separator:a40e145063833155c800b38f82cee7461"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49bb1a68198bd4c520d15efe3e84f757"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a49bb1a68198bd4c520d15efe3e84f757">sum</a> () const </td></tr>
<tr class="memdesc:a49bb1a68198bd4c520d15efe3e84f757"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the sum of all elements.  <a href="#a49bb1a68198bd4c520d15efe3e84f757">More...</a><br /></td></tr>
<tr class="separator:a49bb1a68198bd4c520d15efe3e84f757"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5a2fb5b6b57e0726624c2b6e7c6545c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ad5a2fb5b6b57e0726624c2b6e7c6545c">product</a> () const </td></tr>
<tr class="memdesc:ad5a2fb5b6b57e0726624c2b6e7c6545c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the product of all elements.  <a href="#ad5a2fb5b6b57e0726624c2b6e7c6545c">More...</a><br /></td></tr>
<tr class="separator:ad5a2fb5b6b57e0726624c2b6e7c6545c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47ad37153eb8d291266a51b39ead5948"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a47ad37153eb8d291266a51b39ead5948">operator&lt;</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:a47ad37153eb8d291266a51b39ead5948"><td class="mdescLeft">&#160;</td><td class="mdescRight">Less than operator.  <a href="#a47ad37153eb8d291266a51b39ead5948">More...</a><br /></td></tr>
<tr class="separator:a47ad37153eb8d291266a51b39ead5948"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e6405e081936a4fb23f15160e94ad08"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a0e6405e081936a4fb23f15160e94ad08">operator&lt;=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:a0e6405e081936a4fb23f15160e94ad08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Less than or equals operator.  <a href="#a0e6405e081936a4fb23f15160e94ad08">More...</a><br /></td></tr>
<tr class="separator:a0e6405e081936a4fb23f15160e94ad08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08507ff13f518a93a7d16ea0018f8a53"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a08507ff13f518a93a7d16ea0018f8a53">operator&gt;</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:a08507ff13f518a93a7d16ea0018f8a53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Greater than operator.  <a href="#a08507ff13f518a93a7d16ea0018f8a53">More...</a><br /></td></tr>
<tr class="separator:a08507ff13f518a93a7d16ea0018f8a53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae37243e9f51b2b92b5fd09de69392657"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ae37243e9f51b2b92b5fd09de69392657">operator&gt;=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const </td></tr>
<tr class="memdesc:ae37243e9f51b2b92b5fd09de69392657"><td class="mdescLeft">&#160;</td><td class="mdescRight">Greater than or equals operator.  <a href="#ae37243e9f51b2b92b5fd09de69392657">More...</a><br /></td></tr>
<tr class="separator:ae37243e9f51b2b92b5fd09de69392657"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a2b07d7291d175920274c5e3346e5b68b"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a> = Rank_</td></tr>
<tr class="memdesc:a2b07d7291d175920274c5e3346e5b68b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of elements in <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a>.  <a href="#a2b07d7291d175920274c5e3346e5b68b">More...</a><br /></td></tr>
<tr class="separator:a2b07d7291d175920274c5e3346e5b68b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a7a89e5661ef391dd9f4fe81f0c982b75"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> =  Index_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab61db7c2bfacaf0b7ce465e70d48c44f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::<a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> =  LongIndex_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a5281db2419b5567db4265dead7ac02cc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::<a class="el" href="structcutlass_1_1Coord.html">Coord</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&#160;</td>
          <td class="paramname"><em>value</em> = <code><a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>(0)</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">explicit</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab7094975a4b7471315ca083ae575030a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::<a class="el" href="structcutlass_1_1Coord.html">Coord</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const (&amp;)&#160;</td>
          <td class="paramname"><em>_idx</em>[kRank]</td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a42aefbb547e39b8cc7267c58a610c147"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::<a class="el" href="structcutlass_1_1Coord.html">Coord</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; <a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a>, <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>, <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> &gt; const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a8a65128c86b236cd2bea875b85a34bc1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<div class="memtemplate">
template&lt;int Dim&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::at </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a358dde78a1c2105a9aeb4adee8bb3d2d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::at </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dim</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac7379275d7431ead927af7966b6fa0ec"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<div class="memtemplate">
template&lt;int Dim&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::at </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a06046c22b877abfb277d3f0fe4f8578a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::at </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dim</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a40e145063833155c800b38f82cee7461"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::clamp </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>max</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>min</em> = <code><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;&#160;Rank_,&#160;Index_,&#160;LongIndex_&#160;&gt;()</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a057a417a4d4a6e2f69e0b55a6f7ee902"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::dot </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>&#160;</td>
          <td class="paramname"><em>sum</em> = <code><a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>(0)</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abe58b7c8f153a6029c2adc173f340fe0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::max_dim_index </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae89e8a9fa3f07308f8938052ef1aa1fb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::min_dim_index </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a88096d051dd05111cf265a011a89f7f6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator bool </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">explicit</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa733c6fae0da553053530cba2dddcaa0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator! </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8183b9203a213d4b6381ad7dc120deea"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::<a class="el" href="structcutlass_1_1Coord.html#aa733c6fae0da553053530cba2dddcaa0">operator!</a>= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac6b5fd8d0e5cb856d363fbff9a5b89dd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator* </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a00e618bc944d355badf67c0edd791412"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator*= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aec4c529a728118c0df6a3f527daba746"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator+ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acb799faf60a17b708d0802f9e23c812f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator+= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2e1a706629eae28128230a0fa34b84a0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator- </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a15ac170c861b34d418432aeb62ea86e0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator-= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6ae9f189d1a7a5ce7bb5e4416559c79f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator/ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af515e669363986dbbd60951ea6b69e14"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator/= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a47ad37153eb8d291266a51b39ead5948"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator&lt; </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0e6405e081936a4fb23f15160e94ad08"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator&lt;= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8424ccd74e7e0ff1cf358ef571779cba"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator== </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a08507ff13f518a93a7d16ea0018f8a53"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator&gt; </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae37243e9f51b2b92b5fd09de69392657"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator&gt;= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Rank_, Index_, LongIndex_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6eee93e5fdbe147f751ec108b28275a1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator[] </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dim</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="accf5689f0d6a6f91965bff0cfd9ec296"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const&amp; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::operator[] </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dim</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad5a2fb5b6b57e0726624c2b6e7c6545c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::product </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a329f97d4a09ef34e8470fe55800871f8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<div class="memtemplate">
template&lt;int Slice&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;Slice&gt; <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::slice </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>start</em> = <code>0</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&#160;</td>
          <td class="paramname"><em>identity</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a slice of the <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> which may be larger or smaller in rank than this. </p>

</div>
</div>
<a class="anchor" id="a49bb1a68198bd4c520d15efe3e84f757"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::sum </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a2b07d7291d175920274c5e3346e5b68b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Rank_, typename Index_ = int, typename LongIndex_ = int64_t&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord</a>&lt; Rank_, Index_, LongIndex_ &gt;::kRank = Rank_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="coord_8h_source.html">coord.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
