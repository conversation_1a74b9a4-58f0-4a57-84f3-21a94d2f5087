/*
Copyright <PERSON> 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(MSGPACK_PREDEF_LIBRARY_C_H) || defined(MSGPACK_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef MSGPACK_PREDEF_LIBRARY_C_H
#define MSGPACK_PREDEF_LIBRARY_C_H
#endif

#include <msgpack/predef/library/c/_prefix.h>

#include <msgpack/predef/library/c/cloudabi.h>
#include <msgpack/predef/library/c/gnu.h>
#include <msgpack/predef/library/c/uc.h>
#include <msgpack/predef/library/c/vms.h>
#include <msgpack/predef/library/c/zos.h>

#endif
