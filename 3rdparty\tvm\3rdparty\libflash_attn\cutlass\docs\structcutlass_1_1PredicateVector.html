<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1PredicateVector-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt; Struct Template Reference<div class="ingroups"><a class="el" href="group__predicate__vector__concept.html">Predicate Vector Concept</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Statically sized array of bits implementing.  
</p>

<p><code>#include &lt;<a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">An iterator implementing <a class="el" href="group__predicate__iterator__concept.html">Predicate Iterator Concept</a> enabling sequential read and write access to predicates.  <a href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">An iterator implementing <a class="el" href="group__predicate__iterator__concept.html">Predicate Iterator Concept</a> enabling sequential read and write access to predicates.  <a href="classcutlass_1_1PredicateVector_1_1Iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html">TrivialIterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">Iterator</a> that always returns true.  <a href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:afe85a07b9f311327c6bf04e3a5f94e5a"><td class="memItemLeft" align="right" valign="top">typedef uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#afe85a07b9f311327c6bf04e3a5f94e5a">Storage</a></td></tr>
<tr class="memdesc:afe85a07b9f311327c6bf04e3a5f94e5a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Storage type of individual elements.  <a href="#afe85a07b9f311327c6bf04e3a5f94e5a">More...</a><br /></td></tr>
<tr class="separator:afe85a07b9f311327c6bf04e3a5f94e5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aec1201df19c0ed0516810a3f19353c21"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#aec1201df19c0ed0516810a3f19353c21">PredicateVector</a> (bool value=true)</td></tr>
<tr class="memdesc:aec1201df19c0ed0516810a3f19353c21"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize the predicate vector.  <a href="#aec1201df19c0ed0516810a3f19353c21">More...</a><br /></td></tr>
<tr class="separator:aec1201df19c0ed0516810a3f19353c21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a236bd1a822479750a809452fd58dd917"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a236bd1a822479750a809452fd58dd917">fill</a> (bool value=true)</td></tr>
<tr class="memdesc:a236bd1a822479750a809452fd58dd917"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fills all predicates with a given value.  <a href="#a236bd1a822479750a809452fd58dd917">More...</a><br /></td></tr>
<tr class="separator:a236bd1a822479750a809452fd58dd917"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51d9239e76ec040819333022fcecdb55"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a51d9239e76ec040819333022fcecdb55">clear</a> ()</td></tr>
<tr class="memdesc:a51d9239e76ec040819333022fcecdb55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clears all predicates.  <a href="#a51d9239e76ec040819333022fcecdb55">More...</a><br /></td></tr>
<tr class="separator:a51d9239e76ec040819333022fcecdb55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfabd17462a21adf71e37b2dacd1f45f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#adfabd17462a21adf71e37b2dacd1f45f">enable</a> ()</td></tr>
<tr class="memdesc:adfabd17462a21adf71e37b2dacd1f45f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets all predicates to true.  <a href="#adfabd17462a21adf71e37b2dacd1f45f">More...</a><br /></td></tr>
<tr class="separator:adfabd17462a21adf71e37b2dacd1f45f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a759fda07cb29bfa9041f45e1725a07"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a5a759fda07cb29bfa9041f45e1725a07">operator[]</a> (int idx) const </td></tr>
<tr class="memdesc:a5a759fda07cb29bfa9041f45e1725a07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses a bit within the predicate vector.  <a href="#a5a759fda07cb29bfa9041f45e1725a07">More...</a><br /></td></tr>
<tr class="separator:a5a759fda07cb29bfa9041f45e1725a07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af14b5caa5b8722f06726681a1a985031"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#af14b5caa5b8722f06726681a1a985031">at</a> (int idx) const </td></tr>
<tr class="memdesc:af14b5caa5b8722f06726681a1a985031"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses a bit within the predicate vector.  <a href="#af14b5caa5b8722f06726681a1a985031">More...</a><br /></td></tr>
<tr class="separator:af14b5caa5b8722f06726681a1a985031"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a062fa8a8df725ef08ced2ffcca8336af"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a062fa8a8df725ef08ced2ffcca8336af">set</a> (int idx, bool value=true)</td></tr>
<tr class="memdesc:a062fa8a8df725ef08ced2ffcca8336af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a bit within the predicate vector.  <a href="#a062fa8a8df725ef08ced2ffcca8336af">More...</a><br /></td></tr>
<tr class="separator:a062fa8a8df725ef08ced2ffcca8336af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe47d16e877fc7e02f2a693b5bd4b6d0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#afe47d16e877fc7e02f2a693b5bd4b6d0">operator&amp;=</a> (<a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> const &amp;predicates)</td></tr>
<tr class="memdesc:afe47d16e877fc7e02f2a693b5bd4b6d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes the intersection of two identical predicate vectors.  <a href="#afe47d16e877fc7e02f2a693b5bd4b6d0">More...</a><br /></td></tr>
<tr class="separator:afe47d16e877fc7e02f2a693b5bd4b6d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab9de134132c62de1c062ca57582cdbc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#aab9de134132c62de1c062ca57582cdbc">operator|=</a> (<a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> const &amp;predicates)</td></tr>
<tr class="memdesc:aab9de134132c62de1c062ca57582cdbc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes the union of two identical predicate vectors.  <a href="#aab9de134132c62de1c062ca57582cdbc">More...</a><br /></td></tr>
<tr class="separator:aab9de134132c62de1c062ca57582cdbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29b6a3044b89d0b3ff98fd571e12cdd8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a29b6a3044b89d0b3ff98fd571e12cdd8">is_zero</a> () const </td></tr>
<tr class="memdesc:a29b6a3044b89d0b3ff98fd571e12cdd8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if entire predicate array is zero.  <a href="#a29b6a3044b89d0b3ff98fd571e12cdd8">More...</a><br /></td></tr>
<tr class="separator:a29b6a3044b89d0b3ff98fd571e12cdd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a649045d8224514a4c28bcaf4b247b4a5"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a649045d8224514a4c28bcaf4b247b4a5">begin</a> ()</td></tr>
<tr class="memdesc:a649045d8224514a4c28bcaf4b247b4a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an iterator to the start of the bit vector.  <a href="#a649045d8224514a4c28bcaf4b247b4a5">More...</a><br /></td></tr>
<tr class="separator:a649045d8224514a4c28bcaf4b247b4a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9493fc80fdc33330cc15641779cc275"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#ad9493fc80fdc33330cc15641779cc275">end</a> ()</td></tr>
<tr class="memdesc:ad9493fc80fdc33330cc15641779cc275"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an iterator.  <a href="#ad9493fc80fdc33330cc15641779cc275">More...</a><br /></td></tr>
<tr class="separator:ad9493fc80fdc33330cc15641779cc275"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a505db4b5fba1671ee8362c18e2ccce1b"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a505db4b5fba1671ee8362c18e2ccce1b">const_begin</a> () const </td></tr>
<tr class="memdesc:a505db4b5fba1671ee8362c18e2ccce1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">ConstIterator</a>.  <a href="#a505db4b5fba1671ee8362c18e2ccce1b">More...</a><br /></td></tr>
<tr class="separator:a505db4b5fba1671ee8362c18e2ccce1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8017a90ffe8a8039fff56b6956739045"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a8017a90ffe8a8039fff56b6956739045">const_end</a> () const </td></tr>
<tr class="memdesc:a8017a90ffe8a8039fff56b6956739045"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">ConstIterator</a>.  <a href="#a8017a90ffe8a8039fff56b6956739045">More...</a><br /></td></tr>
<tr class="separator:a8017a90ffe8a8039fff56b6956739045"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:afff3a2142d9853606d6ad7c3a459f492"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#afff3a2142d9853606d6ad7c3a459f492">kPredicates</a> = kPredicates_</td></tr>
<tr class="memdesc:afff3a2142d9853606d6ad7c3a459f492"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of bits stored by the <a class="el" href="structcutlass_1_1PredicateVector.html" title="Statically sized array of bits implementing. ">PredicateVector</a>.  <a href="#afff3a2142d9853606d6ad7c3a459f492">More...</a><br /></td></tr>
<tr class="separator:afff3a2142d9853606d6ad7c3a459f492"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1387c4a964f971ed4611d750a09ec0b5"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a1387c4a964f971ed4611d750a09ec0b5">kPredicatesPerByte</a> = kPredicatesPerByte_</td></tr>
<tr class="memdesc:a1387c4a964f971ed4611d750a09ec0b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of bits stored within each byte of the predicate bit vector.  <a href="#a1387c4a964f971ed4611d750a09ec0b5">More...</a><br /></td></tr>
<tr class="separator:a1387c4a964f971ed4611d750a09ec0b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf848dce84c01453ab8a2d00c8d4f86e"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#acf848dce84c01453ab8a2d00c8d4f86e">kPredicateStart</a> = kPredicateStart_</td></tr>
<tr class="memdesc:acf848dce84c01453ab8a2d00c8d4f86e"><td class="mdescLeft">&#160;</td><td class="mdescRight">First bit withing each byte containing predicates.  <a href="#acf848dce84c01453ab8a2d00c8d4f86e">More...</a><br /></td></tr>
<tr class="separator:acf848dce84c01453ab8a2d00c8d4f86e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab870e074b33c598f69fe11e104615c5a"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#ab870e074b33c598f69fe11e104615c5a">kBytes</a> = (<a class="el" href="structcutlass_1_1PredicateVector.html#afff3a2142d9853606d6ad7c3a459f492">kPredicates</a> + <a class="el" href="structcutlass_1_1PredicateVector.html#a1387c4a964f971ed4611d750a09ec0b5">kPredicatesPerByte</a> - 1) / <a class="el" href="structcutlass_1_1PredicateVector.html#a1387c4a964f971ed4611d750a09ec0b5">kPredicatesPerByte</a></td></tr>
<tr class="memdesc:ab870e074b33c598f69fe11e104615c5a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of bytes needed.  <a href="#ab870e074b33c598f69fe11e104615c5a">More...</a><br /></td></tr>
<tr class="separator:ab870e074b33c598f69fe11e104615c5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a734bbfaf3829f73ef0b44fa7db4ccd42"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector.html#a734bbfaf3829f73ef0b44fa7db4ccd42">kWordCount</a> = (<a class="el" href="structcutlass_1_1PredicateVector.html#ab870e074b33c598f69fe11e104615c5a">kBytes</a> + sizeof(<a class="el" href="structcutlass_1_1PredicateVector.html#afe85a07b9f311327c6bf04e3a5f94e5a">Storage</a>) - 1) / sizeof(<a class="el" href="structcutlass_1_1PredicateVector.html#afe85a07b9f311327c6bf04e3a5f94e5a">Storage</a>)</td></tr>
<tr class="memdesc:a734bbfaf3829f73ef0b44fa7db4ccd42"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of storage elements needed.  <a href="#a734bbfaf3829f73ef0b44fa7db4ccd42">More...</a><br /></td></tr>
<tr class="separator:a734bbfaf3829f73ef0b44fa7db4ccd42"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="afe85a07b9f311327c6bf04e3a5f94e5a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef uint32_t <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::<a class="el" href="structcutlass_1_1PredicateVector.html#afe85a07b9f311327c6bf04e3a5f94e5a">Storage</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="aec1201df19c0ed0516810a3f19353c21"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::<a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>value</em> = <code>true</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="af14b5caa5b8722f06726681a1a985031"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::at </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>idx</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a649045d8224514a4c28bcaf4b247b4a5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::begin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a51d9239e76ec040819333022fcecdb55"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::clear </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a505db4b5fba1671ee8362c18e2ccce1b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::const_begin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8017a90ffe8a8039fff56b6956739045"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::const_end </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adfabd17462a21adf71e37b2dacd1f45f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::enable </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad9493fc80fdc33330cc15641779cc275"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::end </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a236bd1a822479750a809452fd58dd917"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::fill </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>value</em> = <code>true</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a29b6a3044b89d0b3ff98fd571e12cdd8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::is_zero </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afe47d16e877fc7e02f2a693b5bd4b6d0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::operator&amp;= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>predicates</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5a759fda07cb29bfa9041f45e1725a07"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::operator[] </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>idx</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aab9de134132c62de1c062ca57582cdbc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::operator|= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt; const &amp;&#160;</td>
          <td class="paramname"><em>predicates</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a062fa8a8df725ef08ced2ffcca8336af"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::set </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>idx</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>value</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="ab870e074b33c598f69fe11e104615c5a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::kBytes = (<a class="el" href="structcutlass_1_1PredicateVector.html#afff3a2142d9853606d6ad7c3a459f492">kPredicates</a> + <a class="el" href="structcutlass_1_1PredicateVector.html#a1387c4a964f971ed4611d750a09ec0b5">kPredicatesPerByte</a> - 1) / <a class="el" href="structcutlass_1_1PredicateVector.html#a1387c4a964f971ed4611d750a09ec0b5">kPredicatesPerByte</a></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afff3a2142d9853606d6ad7c3a459f492"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::kPredicates = kPredicates_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1387c4a964f971ed4611d750a09ec0b5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::kPredicatesPerByte = kPredicatesPerByte_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acf848dce84c01453ab8a2d00c8d4f86e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::kPredicateStart = kPredicateStart_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a734bbfaf3829f73ef0b44fa7db4ccd42"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::kWordCount = (<a class="el" href="structcutlass_1_1PredicateVector.html#ab870e074b33c598f69fe11e104615c5a">kBytes</a> + sizeof(<a class="el" href="structcutlass_1_1PredicateVector.html#afe85a07b9f311327c6bf04e3a5f94e5a">Storage</a>) - 1) / sizeof(<a class="el" href="structcutlass_1_1PredicateVector.html#afe85a07b9f311327c6bf04e3a5f94e5a">Storage</a>)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
