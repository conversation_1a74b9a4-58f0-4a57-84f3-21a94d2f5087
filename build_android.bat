@echo off
chcp 65001
echo Starting MLC-LLM Android Build Process...

REM Set environment variables
set ANDROID_NDK=C:\Android\Sdk\ndk\25.2.9519653
set PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\amd64;C:\Program Files\CMake\bin;C:\Program Files\Git\cmd;%PATH%

REM Create Android NDK structure
mkdir "C:\Android\Sdk\ndk\25.2.9519653\build\cmake" 2>nul
echo set(CMAKE_SYSTEM_NAME Android) > "C:\Android\Sdk\ndk\25.2.9519653\build\cmake\android.toolchain.cmake"
echo set(CMAKE_ANDROID_ARCH_ABI arm64-v8a) >> "C:\Android\Sdk\ndk\25.2.9519653\build\cmake\android.toolchain.cmake"
echo set(CMAKE_ANDROID_NDK %ANDROID_NDK%) >> "C:\Android\Sdk\ndk\25.2.9519653\build\cmake\android.toolchain.cmake"

REM Create minimal NDK toolchain
mkdir "C:\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin" 2>nul

REM Create build directory
rmdir /s /q build 2>nul
mkdir build
cd build

REM Configure with CMake
echo Configuring CMake...
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release -DCMAKE_POLICY_VERSION_MINIMUM=3.5

if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release --parallel 4

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Checking for generated files...
dir Release\*.dll
dir Release\*.exe

REM Try to install Python module
echo Installing Python module...
cd ..\python
python -m pip install -e .

echo Android build process completed!
pause
