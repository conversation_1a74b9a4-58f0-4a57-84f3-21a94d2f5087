name: CMake
on: [push, pull_request]
jobs:
  ci-cmake:
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Ubuntu GCC
            os: ubuntu-latest
            compiler: gcc
            cflags: -Werror -Wall -Wextra

          # Test out of source builds
          - name: Ubuntu GCC OSB
            os: ubuntu-latest
            compiler: gcc
            cflags: -Werror -Wall -Wextra
            build-dir: ../build
            src-dir: ../zlib

          - name: Ubuntu GCC -O3
            os: ubuntu-latest
            compiler: gcc
            cflags: -O3 -Werror -Wall -Wextra

          - name: Ubuntu Clang
            os: ubuntu-latest
            compiler: clang
            cflags: -Werror -Wall -Wextra

          - name: Ubuntu Clang Debug
            os: ubuntu-latest
            compiler: clang
            cflags: -Werror -Wall -Wextra
            build-config: Debug

          - name: Windows MSVC Win32
            os: windows-latest
            compiler: cl
            cflags: /WX /W3
            cmake-args: -A Win32

          - name: Windows MSVC Win64
            os: windows-latest
            compiler: cl
            cflags: /WX /W3
            cmake-args: -A x64

          - name: Windows GCC
            os: windows-latest
            compiler: gcc
            cflags: -Werror -Wall -Wextra
            cmake-args: -G Ninja

          - name: macOS Clang
            os: macos-latest
            compiler: clang
            cflags: -Werror -Wall -Wextra

          - name: macOS GCC
            os: macos-latest
            compiler: gcc-12
            cflags: -Werror -Wall -Wextra

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install packages (Windows)
      if: runner.os == 'Windows'
      run: |
        choco install --no-progress ninja ${{ matrix.packages }}

    - name: Generate project files
      run: cmake -S ${{ matrix.src-dir || '.' }} -B ${{ matrix.build-dir || '.' }} ${{ matrix.cmake-args }} -D CMAKE_BUILD_TYPE=${{ matrix.build-config || 'Release' }}
      env:
        CC: ${{ matrix.compiler }}
        CFLAGS: ${{ matrix.cflags }}

    - name: Compile source code
      run: cmake --build ${{ matrix.build-dir || '.' }} --config ${{ matrix.build-config || 'Release' }}

    - name: Run test cases
      run: ctest -C Release --output-on-failure --max-width 120
      working-directory: ${{ matrix.build-dir || '.' }}

    - name: Upload build errors
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: ${{ matrix.name }} (cmake)
        path: |
          **/CMakeFiles/CMakeOutput.log
          **/CMakeFiles/CMakeError.log
        retention-days: 7
