//
// MessagePack for C++ deflate buffer implementation
//
// Copyright (C) 2010-2013 FURUHASH<PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_ZBUFFER_HPP
#define MSGPACK_ZBUFFER_HPP

#include "msgpack/zbuffer_decl.hpp"

#include "msgpack/v1/zbuffer.hpp"

#endif // MSGPACK_ZBUFFER_HPP
