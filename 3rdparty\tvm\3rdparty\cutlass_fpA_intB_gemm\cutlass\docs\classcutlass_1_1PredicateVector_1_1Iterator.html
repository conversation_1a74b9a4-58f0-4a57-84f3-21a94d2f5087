<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a></li><li class="navelem"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classcutlass_1_1PredicateVector_1_1Iterator-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator Class Reference<div class="ingroups"><a class="el" href="group__predicate__iterator__concept.html">Predicate Iterator Concept</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>An iterator implementing <a class="el" href="group__predicate__iterator__concept.html">Predicate Iterator Concept</a> enabling sequential read and write access to predicates.  
</p>

<p><code>#include &lt;<a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a91b7d25cbd64e696ef23c87671f0b077"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a91b7d25cbd64e696ef23c87671f0b077">Iterator</a> (<a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;it)</td></tr>
<tr class="memdesc:a91b7d25cbd64e696ef23c87671f0b077"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="#a91b7d25cbd64e696ef23c87671f0b077">More...</a><br /></td></tr>
<tr class="separator:a91b7d25cbd64e696ef23c87671f0b077"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08a7c4bd292f3dde6fdb7b8ae3eac4eb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a08a7c4bd292f3dde6fdb7b8ae3eac4eb">Iterator</a> (<a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> &amp;vec, int _start=0)</td></tr>
<tr class="memdesc:a08a7c4bd292f3dde6fdb7b8ae3eac4eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs an iterator from a <a class="el" href="structcutlass_1_1PredicateVector.html" title="Statically sized array of bits implementing. ">PredicateVector</a>.  <a href="#a08a7c4bd292f3dde6fdb7b8ae3eac4eb">More...</a><br /></td></tr>
<tr class="separator:a08a7c4bd292f3dde6fdb7b8ae3eac4eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dddc0a6b5c958156beef29bedfd1bd3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a7dddc0a6b5c958156beef29bedfd1bd3">operator++</a> ()</td></tr>
<tr class="memdesc:a7dddc0a6b5c958156beef29bedfd1bd3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pre-increment.  <a href="#a7dddc0a6b5c958156beef29bedfd1bd3">More...</a><br /></td></tr>
<tr class="separator:a7dddc0a6b5c958156beef29bedfd1bd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5dcf36597690ce9ad7ef95d82b50654e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a5dcf36597690ce9ad7ef95d82b50654e">operator+=</a> (int offset)</td></tr>
<tr class="memdesc:a5dcf36597690ce9ad7ef95d82b50654e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Increment.  <a href="#a5dcf36597690ce9ad7ef95d82b50654e">More...</a><br /></td></tr>
<tr class="separator:a5dcf36597690ce9ad7ef95d82b50654e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69fb5b24eeb43331b7401768e8584e61"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a69fb5b24eeb43331b7401768e8584e61">operator--</a> ()</td></tr>
<tr class="memdesc:a69fb5b24eeb43331b7401768e8584e61"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pre-decrement.  <a href="#a69fb5b24eeb43331b7401768e8584e61">More...</a><br /></td></tr>
<tr class="separator:a69fb5b24eeb43331b7401768e8584e61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13a19c8d85bf415fe5f4c17fce9cf6ca"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a13a19c8d85bf415fe5f4c17fce9cf6ca">operator-=</a> (int offset)</td></tr>
<tr class="memdesc:a13a19c8d85bf415fe5f4c17fce9cf6ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decrement.  <a href="#a13a19c8d85bf415fe5f4c17fce9cf6ca">More...</a><br /></td></tr>
<tr class="separator:a13a19c8d85bf415fe5f4c17fce9cf6ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c7333ad14d545cafc707e78752bf1e3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a6c7333ad14d545cafc707e78752bf1e3">operator++</a> (int)</td></tr>
<tr class="memdesc:a6c7333ad14d545cafc707e78752bf1e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Post-increment.  <a href="#a6c7333ad14d545cafc707e78752bf1e3">More...</a><br /></td></tr>
<tr class="separator:a6c7333ad14d545cafc707e78752bf1e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad709a11f43b84c88e3ce3a0394f8e8a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#aad709a11f43b84c88e3ce3a0394f8e8a">operator--</a> (int)</td></tr>
<tr class="memdesc:aad709a11f43b84c88e3ce3a0394f8e8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Post-decrement.  <a href="#aad709a11f43b84c88e3ce3a0394f8e8a">More...</a><br /></td></tr>
<tr class="separator:aad709a11f43b84c88e3ce3a0394f8e8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a831f15d9f01c7896dab70b94dfad660f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a831f15d9f01c7896dab70b94dfad660f">operator+</a> (int offset)</td></tr>
<tr class="memdesc:a831f15d9f01c7896dab70b94dfad660f"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">Iterator</a> advances by some amount.  <a href="#a831f15d9f01c7896dab70b94dfad660f">More...</a><br /></td></tr>
<tr class="separator:a831f15d9f01c7896dab70b94dfad660f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca8c844b4e4444fa6dc9779761356a75"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#aca8c844b4e4444fa6dc9779761356a75">operator-</a> (int offset)</td></tr>
<tr class="memdesc:aca8c844b4e4444fa6dc9779761356a75"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">Iterator</a> recedes by some amount.  <a href="#aca8c844b4e4444fa6dc9779761356a75">More...</a><br /></td></tr>
<tr class="separator:aca8c844b4e4444fa6dc9779761356a75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a349edcbf86ca0a6827462a07e8754320"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a349edcbf86ca0a6827462a07e8754320">operator==</a> (<a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;it) const </td></tr>
<tr class="memdesc:a349edcbf86ca0a6827462a07e8754320"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if iterators point to the same bit.  <a href="#a349edcbf86ca0a6827462a07e8754320">More...</a><br /></td></tr>
<tr class="separator:a349edcbf86ca0a6827462a07e8754320"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95d40d20eb0749e2916d03088d49f680"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a95d40d20eb0749e2916d03088d49f680">operator!=</a> (<a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;it) const </td></tr>
<tr class="memdesc:a95d40d20eb0749e2916d03088d49f680"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns false if iterators point to the same bit.  <a href="#a95d40d20eb0749e2916d03088d49f680">More...</a><br /></td></tr>
<tr class="separator:a95d40d20eb0749e2916d03088d49f680"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af035589126434bd2dbef4000cd864b8b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#af035589126434bd2dbef4000cd864b8b">get</a> ()</td></tr>
<tr class="memdesc:af035589126434bd2dbef4000cd864b8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the bit at the pointed to location.  <a href="#af035589126434bd2dbef4000cd864b8b">More...</a><br /></td></tr>
<tr class="separator:af035589126434bd2dbef4000cd864b8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36d28f662e1ab2f8b0b9da9e6863c1de"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a36d28f662e1ab2f8b0b9da9e6863c1de">at</a> () const </td></tr>
<tr class="memdesc:a36d28f662e1ab2f8b0b9da9e6863c1de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the bit at the pointed to location.  <a href="#a36d28f662e1ab2f8b0b9da9e6863c1de">More...</a><br /></td></tr>
<tr class="separator:a36d28f662e1ab2f8b0b9da9e6863c1de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57dd1dfd84701160273fff79789f1137"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#a57dd1dfd84701160273fff79789f1137">operator*</a> () const </td></tr>
<tr class="memdesc:a57dd1dfd84701160273fff79789f1137"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dereferences iterator.  <a href="#a57dd1dfd84701160273fff79789f1137">More...</a><br /></td></tr>
<tr class="separator:a57dd1dfd84701160273fff79789f1137"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadfd039b5622098c9e46706a27122575"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#aadfd039b5622098c9e46706a27122575">set</a> (bool value=true)</td></tr>
<tr class="memdesc:aadfd039b5622098c9e46706a27122575"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the bit at the pointed to location.  <a href="#aadfd039b5622098c9e46706a27122575">More...</a><br /></td></tr>
<tr class="separator:aadfd039b5622098c9e46706a27122575"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a91b7d25cbd64e696ef23c87671f0b077"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::Iterator </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;&#160;</td>
          <td class="paramname"><em>it</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a08a7c4bd292f3dde6fdb7b8ae3eac4eb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::Iterator </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> &amp;&#160;</td>
          <td class="paramname"><em>vec</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>_start</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a36d28f662e1ab2f8b0b9da9e6863c1de"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::at </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af035589126434bd2dbef4000cd864b8b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::get </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a95d40d20eb0749e2916d03088d49f680"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator!= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;&#160;</td>
          <td class="paramname"><em>it</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a57dd1dfd84701160273fff79789f1137"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator* </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a831f15d9f01c7896dab70b94dfad660f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator+ </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7dddc0a6b5c958156beef29bedfd1bd3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator++ </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6c7333ad14d545cafc707e78752bf1e3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator++ </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5dcf36597690ce9ad7ef95d82b50654e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator+= </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aca8c844b4e4444fa6dc9779761356a75"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator- </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a69fb5b24eeb43331b7401768e8584e61"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator-- </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aad709a11f43b84c88e3ce3a0394f8e8a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator-- </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a13a19c8d85bf415fe5f4c17fce9cf6ca"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator-= </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a349edcbf86ca0a6827462a07e8754320"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::operator== </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;&#160;</td>
          <td class="paramname"><em>it</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aadfd039b5622098c9e46706a27122575"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator::set </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>value</em> = <code>true</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
