<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::Array&lt; T, N, false &gt; Class Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html">Array&lt; T, N, false &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::Array&lt; T, N, false &gt; Class Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Statically sized array for any data type.  
</p>

<p><code>#include &lt;<a class="el" href="array__subbyte_8h_source.html">array_subbyte.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1const__iterator.html">const_iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional constant iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1const__iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1const__reference.html">const_reference</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reference object extracts sub-byte items.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1const__reference.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1const__reverse__iterator.html">const_reverse_iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional constant iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1const__reverse__iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1iterator.html">iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reference.html">reference</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reference object inserts or extracts sub-byte items.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reference.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reverse__iterator.html">reverse_iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reverse__iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a878e152905d602bcdb98e0e6acd8bd82"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a> = typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; ((<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a45932cad6b905c9ab72889c53112d529">kSizeBits</a>%32)!=0), typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; ((<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a45932cad6b905c9ab72889c53112d529">kSizeBits</a>%16)!=0), uint8_t, uint16_t &gt;::type, uint32_t &gt;::type</td></tr>
<tr class="memdesc:a878e152905d602bcdb98e0e6acd8bd82"><td class="mdescLeft">&#160;</td><td class="mdescRight">Storage type.  <a href="#a878e152905d602bcdb98e0e6acd8bd82">More...</a><br /></td></tr>
<tr class="separator:a878e152905d602bcdb98e0e6acd8bd82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a700940b7ec4aa2c10506b8109b58b709"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a700940b7ec4aa2c10506b8109b58b709">Element</a> = T</td></tr>
<tr class="memdesc:a700940b7ec4aa2c10506b8109b58b709"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element type.  <a href="#a700940b7ec4aa2c10506b8109b58b709">More...</a><br /></td></tr>
<tr class="separator:a700940b7ec4aa2c10506b8109b58b709"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1a07d3bbf76e850a948c8efe864acdb"><td class="memItemLeft" align="right" valign="top">typedef T&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ac1a07d3bbf76e850a948c8efe864acdb">value_type</a></td></tr>
<tr class="separator:ac1a07d3bbf76e850a948c8efe864acdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a714f3275de8a7f9d14f8b04aed45988d"><td class="memItemLeft" align="right" valign="top">typedef size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a></td></tr>
<tr class="separator:a714f3275de8a7f9d14f8b04aed45988d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8dd11bf19216707ab3340b66833c9c9"><td class="memItemLeft" align="right" valign="top">typedef ptrdiff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#af8dd11bf19216707ab3340b66833c9c9">difference_type</a></td></tr>
<tr class="separator:af8dd11bf19216707ab3340b66833c9c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a77712281a0ddbf880a4f6fb9aa2ea3"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ac1a07d3bbf76e850a948c8efe864acdb">value_type</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a2a77712281a0ddbf880a4f6fb9aa2ea3">pointer</a></td></tr>
<tr class="separator:a2a77712281a0ddbf880a4f6fb9aa2ea3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a90423fc5483b3ee1d31f377321e9e0"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ac1a07d3bbf76e850a948c8efe864acdb">value_type</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a8a90423fc5483b3ee1d31f377321e9e0">const_pointer</a></td></tr>
<tr class="separator:a8a90423fc5483b3ee1d31f377321e9e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac37d0c85dd6246ff7e08d12903f49c4d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ac37d0c85dd6246ff7e08d12903f49c4d">Array</a> ()</td></tr>
<tr class="separator:ac37d0c85dd6246ff7e08d12903f49c4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d4667c3c9ebf3322ba94d43421e2577"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a5d4667c3c9ebf3322ba94d43421e2577">Array</a> (Array const &amp;x)</td></tr>
<tr class="separator:a5d4667c3c9ebf3322ba94d43421e2577"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b84c4dc5257f31108a0598915f03f94"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a5b84c4dc5257f31108a0598915f03f94">clear</a> ()</td></tr>
<tr class="memdesc:a5b84c4dc5257f31108a0598915f03f94"><td class="mdescLeft">&#160;</td><td class="mdescRight">Efficient clear method.  <a href="#a5b84c4dc5257f31108a0598915f03f94">More...</a><br /></td></tr>
<tr class="separator:a5b84c4dc5257f31108a0598915f03f94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6268f2bbbdfc671cf7066ea0ee1bb46f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a6268f2bbbdfc671cf7066ea0ee1bb46f">at</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a> pos)</td></tr>
<tr class="separator:a6268f2bbbdfc671cf7066ea0ee1bb46f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0443a4af7c9594492bfb8a84bbd12a52"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a0443a4af7c9594492bfb8a84bbd12a52">at</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a> pos) const </td></tr>
<tr class="separator:a0443a4af7c9594492bfb8a84bbd12a52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeaeeb7bddb6824adc6feb5ab912d65dc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#aeaeeb7bddb6824adc6feb5ab912d65dc">operator[]</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a> pos)</td></tr>
<tr class="separator:aeaeeb7bddb6824adc6feb5ab912d65dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35db1c6ac0d42a486eb3a0a0eee95c80"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a35db1c6ac0d42a486eb3a0a0eee95c80">operator[]</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a> pos) const </td></tr>
<tr class="separator:a35db1c6ac0d42a486eb3a0a0eee95c80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa89dd0781c0a81421589182a5402df8b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#aa89dd0781c0a81421589182a5402df8b">front</a> ()</td></tr>
<tr class="separator:aa89dd0781c0a81421589182a5402df8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7ebd33505e48ab3beb6b551e8b762e5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ab7ebd33505e48ab3beb6b551e8b762e5">front</a> () const </td></tr>
<tr class="separator:ab7ebd33505e48ab3beb6b551e8b762e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a693677ee48012a4d013d55741d38764e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a693677ee48012a4d013d55741d38764e">back</a> ()</td></tr>
<tr class="separator:a693677ee48012a4d013d55741d38764e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c1665d0eff4c1788b0a5a3bfa3bc63e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a2c1665d0eff4c1788b0a5a3bfa3bc63e">back</a> () const </td></tr>
<tr class="separator:a2c1665d0eff4c1788b0a5a3bfa3bc63e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1949c8a8c81dc2743328a56ff19fc933"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a2a77712281a0ddbf880a4f6fb9aa2ea3">pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a1949c8a8c81dc2743328a56ff19fc933">data</a> ()</td></tr>
<tr class="separator:a1949c8a8c81dc2743328a56ff19fc933"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab617ed6c9cc6336baf1030713d6dfbbb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a8a90423fc5483b3ee1d31f377321e9e0">const_pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ab617ed6c9cc6336baf1030713d6dfbbb">data</a> () const </td></tr>
<tr class="separator:ab617ed6c9cc6336baf1030713d6dfbbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66e2465301e46afebf9e56c4060fb3cb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a66e2465301e46afebf9e56c4060fb3cb">raw_data</a> ()</td></tr>
<tr class="separator:a66e2465301e46afebf9e56c4060fb3cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16e55f7c4ae1700ae09c2bce137d06ae"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a16e55f7c4ae1700ae09c2bce137d06ae">raw_data</a> () const </td></tr>
<tr class="separator:a16e55f7c4ae1700ae09c2bce137d06ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40829269d53d097b5b7bfce32e4afcc4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a40829269d53d097b5b7bfce32e4afcc4">empty</a> () const </td></tr>
<tr class="separator:a40829269d53d097b5b7bfce32e4afcc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae1b48e77c8381a8059a09a791d6b8d37"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ae1b48e77c8381a8059a09a791d6b8d37">size</a> () const </td></tr>
<tr class="separator:ae1b48e77c8381a8059a09a791d6b8d37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f982c95366ce4fda90e35281adfe63c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a8f982c95366ce4fda90e35281adfe63c">max_size</a> () const </td></tr>
<tr class="separator:a8f982c95366ce4fda90e35281adfe63c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c31d3673a48b2ed275bd56714fbcfbe"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a1c31d3673a48b2ed275bd56714fbcfbe">fill</a> (T const &amp;value)</td></tr>
<tr class="separator:a1c31d3673a48b2ed275bd56714fbcfbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e9dbf4a486f07dc72dd5140a7628971"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a6e9dbf4a486f07dc72dd5140a7628971">begin</a> ()</td></tr>
<tr class="separator:a6e9dbf4a486f07dc72dd5140a7628971"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a86a56cc907c8566068034ef8294cf7c2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a86a56cc907c8566068034ef8294cf7c2">cbegin</a> () const </td></tr>
<tr class="separator:a86a56cc907c8566068034ef8294cf7c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80258d6b5e43ae529cd726f0d4292619"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a80258d6b5e43ae529cd726f0d4292619">end</a> ()</td></tr>
<tr class="separator:a80258d6b5e43ae529cd726f0d4292619"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6106b72ee9035389afb313801561b16"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ae6106b72ee9035389afb313801561b16">cend</a> () const </td></tr>
<tr class="separator:ae6106b72ee9035389afb313801561b16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2098c88aed61f9b27bac37a083130336"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a2098c88aed61f9b27bac37a083130336">rbegin</a> ()</td></tr>
<tr class="separator:a2098c88aed61f9b27bac37a083130336"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01b9f76c6052dc2467095b91c1ebe34e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a01b9f76c6052dc2467095b91c1ebe34e">crbegin</a> () const </td></tr>
<tr class="separator:a01b9f76c6052dc2467095b91c1ebe34e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39c08a75c7cc22fcd296e6c9fefe754e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a39c08a75c7cc22fcd296e6c9fefe754e">rend</a> ()</td></tr>
<tr class="separator:a39c08a75c7cc22fcd296e6c9fefe754e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abbc436f18649c1578ef95eb501872094"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#abbc436f18649c1578ef95eb501872094">crend</a> () const </td></tr>
<tr class="separator:abbc436f18649c1578ef95eb501872094"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a45932cad6b905c9ab72889c53112d529"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a45932cad6b905c9ab72889c53112d529">kSizeBits</a> = <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;T&gt;::value * N</td></tr>
<tr class="separator:a45932cad6b905c9ab72889c53112d529"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a6f489743eb03c5c97fe6bb3ed2fa22"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a4a6f489743eb03c5c97fe6bb3ed2fa22">kElementsPerStoredItem</a> = (sizeof(<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a>) * 8) / <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;T&gt;::value</td></tr>
<tr class="memdesc:a4a6f489743eb03c5c97fe6bb3ed2fa22"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of logical elements per stored object.  <a href="#a4a6f489743eb03c5c97fe6bb3ed2fa22">More...</a><br /></td></tr>
<tr class="separator:a4a6f489743eb03c5c97fe6bb3ed2fa22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbe4f574d87e61bf18ac5b9f5a6ea8aa"><td class="memItemLeft" align="right" valign="top">static size_t const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#afbe4f574d87e61bf18ac5b9f5a6ea8aa">kStorageElements</a> = N / <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a4a6f489743eb03c5c97fe6bb3ed2fa22">kElementsPerStoredItem</a></td></tr>
<tr class="memdesc:afbe4f574d87e61bf18ac5b9f5a6ea8aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of storage elements.  <a href="#afbe4f574d87e61bf18ac5b9f5a6ea8aa">More...</a><br /></td></tr>
<tr class="separator:afbe4f574d87e61bf18ac5b9f5a6ea8aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56c28da772c3cf49799eeef4ee1eb981"><td class="memItemLeft" align="right" valign="top">static size_t const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a56c28da772c3cf49799eeef4ee1eb981">kElements</a> = N</td></tr>
<tr class="memdesc:a56c28da772c3cf49799eeef4ee1eb981"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of logical elements.  <a href="#a56c28da772c3cf49799eeef4ee1eb981">More...</a><br /></td></tr>
<tr class="separator:a56c28da772c3cf49799eeef4ee1eb981"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6981c3aa259d3a1cc4818e29fa1d1423"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a> const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a6981c3aa259d3a1cc4818e29fa1d1423">kMask</a> = ((<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a>(1) &lt;&lt; <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;T&gt;::value) - 1)</td></tr>
<tr class="memdesc:a6981c3aa259d3a1cc4818e29fa1d1423"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bitmask for covering one item.  <a href="#a6981c3aa259d3a1cc4818e29fa1d1423">More...</a><br /></td></tr>
<tr class="separator:a6981c3aa259d3a1cc4818e29fa1d1423"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a8a90423fc5483b3ee1d31f377321e9e0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ac1a07d3bbf76e850a948c8efe864acdb">value_type</a> const* cutlass::Array&lt; T, N, false &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a8a90423fc5483b3ee1d31f377321e9e0">const_pointer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af8dd11bf19216707ab3340b66833c9c9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ptrdiff_t cutlass::Array&lt; T, N, false &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#af8dd11bf19216707ab3340b66833c9c9">difference_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a700940b7ec4aa2c10506b8109b58b709"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using cutlass::Array&lt; T, N, false &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a700940b7ec4aa2c10506b8109b58b709">Element</a> =  T</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2a77712281a0ddbf880a4f6fb9aa2ea3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ac1a07d3bbf76e850a948c8efe864acdb">value_type</a>* cutlass::Array&lt; T, N, false &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a2a77712281a0ddbf880a4f6fb9aa2ea3">pointer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a714f3275de8a7f9d14f8b04aed45988d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef size_t cutlass::Array&lt; T, N, false &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a878e152905d602bcdb98e0e6acd8bd82"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using cutlass::Array&lt; T, N, false &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a> =  typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; ((<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a45932cad6b905c9ab72889c53112d529">kSizeBits</a> % 32) != 0), typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; ((<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a45932cad6b905c9ab72889c53112d529">kSizeBits</a> % 16) != 0), uint8_t, uint16_t &gt;::type, uint32_t &gt;::type</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac1a07d3bbf76e850a948c8efe864acdb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef T cutlass::Array&lt; T, N, false &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#ac1a07d3bbf76e850a948c8efe864acdb">value_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="ac37d0c85dd6246ff7e08d12903f49c4d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::Array&lt; T, N, false &gt;::Array </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5d4667c3c9ebf3322ba94d43421e2577"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::Array&lt; T, N, false &gt;::Array </td>
          <td>(</td>
          <td class="paramtype">Array&lt; T, N, false &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a6268f2bbbdfc671cf7066ea0ee1bb46f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference cutlass::Array&lt; T, N, false &gt;::at </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0443a4af7c9594492bfb8a84bbd12a52"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference cutlass::Array&lt; T, N, false &gt;::at </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a693677ee48012a4d013d55741d38764e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference cutlass::Array&lt; T, N, false &gt;::back </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2c1665d0eff4c1788b0a5a3bfa3bc63e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference cutlass::Array&lt; T, N, false &gt;::back </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6e9dbf4a486f07dc72dd5140a7628971"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator cutlass::Array&lt; T, N, false &gt;::begin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a86a56cc907c8566068034ef8294cf7c2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator cutlass::Array&lt; T, N, false &gt;::cbegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae6106b72ee9035389afb313801561b16"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator cutlass::Array&lt; T, N, false &gt;::cend </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5b84c4dc5257f31108a0598915f03f94"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void cutlass::Array&lt; T, N, false &gt;::clear </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a01b9f76c6052dc2467095b91c1ebe34e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator cutlass::Array&lt; T, N, false &gt;::crbegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abbc436f18649c1578ef95eb501872094"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator cutlass::Array&lt; T, N, false &gt;::crend </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1949c8a8c81dc2743328a56ff19fc933"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a2a77712281a0ddbf880a4f6fb9aa2ea3">pointer</a> cutlass::Array&lt; T, N, false &gt;::data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab617ed6c9cc6336baf1030713d6dfbbb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a8a90423fc5483b3ee1d31f377321e9e0">const_pointer</a> cutlass::Array&lt; T, N, false &gt;::data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a40829269d53d097b5b7bfce32e4afcc4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> bool cutlass::Array&lt; T, N, false &gt;::empty </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a80258d6b5e43ae529cd726f0d4292619"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator cutlass::Array&lt; T, N, false &gt;::end </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1c31d3673a48b2ed275bd56714fbcfbe"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void cutlass::Array&lt; T, N, false &gt;::fill </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa89dd0781c0a81421589182a5402df8b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference cutlass::Array&lt; T, N, false &gt;::front </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab7ebd33505e48ab3beb6b551e8b762e5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference cutlass::Array&lt; T, N, false &gt;::front </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8f982c95366ce4fda90e35281adfe63c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a> cutlass::Array&lt; T, N, false &gt;::max_size </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aeaeeb7bddb6824adc6feb5ab912d65dc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reference cutlass::Array&lt; T, N, false &gt;::operator[] </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a35db1c6ac0d42a486eb3a0a0eee95c80"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reference cutlass::Array&lt; T, N, false &gt;::operator[] </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a66e2465301e46afebf9e56c4060fb3cb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a>* cutlass::Array&lt; T, N, false &gt;::raw_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a16e55f7c4ae1700ae09c2bce137d06ae"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a> const* cutlass::Array&lt; T, N, false &gt;::raw_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2098c88aed61f9b27bac37a083130336"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator cutlass::Array&lt; T, N, false &gt;::rbegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a39c08a75c7cc22fcd296e6c9fefe754e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator cutlass::Array&lt; T, N, false &gt;::rend </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae1b48e77c8381a8059a09a791d6b8d37"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a714f3275de8a7f9d14f8b04aed45988d">size_type</a> cutlass::Array&lt; T, N, false &gt;::size </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a56c28da772c3cf49799eeef4ee1eb981"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t const cutlass::Array&lt; T, N, false &gt;::kElements = N</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4a6f489743eb03c5c97fe6bb3ed2fa22"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const cutlass::Array&lt; T, N, false &gt;::kElementsPerStoredItem = (sizeof(<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a>) * 8) / <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;T&gt;::value</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6981c3aa259d3a1cc4818e29fa1d1423"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a> const cutlass::Array&lt; T, N, false &gt;::kMask = ((<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a878e152905d602bcdb98e0e6acd8bd82">Storage</a>(1) &lt;&lt; <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;T&gt;::value) - 1)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a45932cad6b905c9ab72889c53112d529"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const cutlass::Array&lt; T, N, false &gt;::kSizeBits = <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;T&gt;::value * N</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afbe4f574d87e61bf18ac5b9f5a6ea8aa"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t const cutlass::Array&lt; T, N, false &gt;::kStorageElements = N / <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a4a6f489743eb03c5c97fe6bb3ed2fa22">kElementsPerStoredItem</a></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="array__subbyte_8h_source.html">array_subbyte.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
