//
// MessagePack for C++ deserializing routine
//
// Copyright (C) 2017 <PERSON><PERSON><PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_UNPACK_EXCEPTION_HPP
#define MSGPACK_UNPACK_EXCEPTION_HPP

#include "msgpack/v1/unpack_exception.hpp"

#endif // MSGPACK_UNPACK_EXCEPTION_HPP
