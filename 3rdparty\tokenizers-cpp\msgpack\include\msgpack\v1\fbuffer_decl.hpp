//
// MessagePack for C++ FILE* buffer adaptor
//
// Copyright (C) 2013-2016 <PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_V1_FBUFFER_DECL_HPP
#define MSGPACK_V1_FBUFFER_DECL_HPP

#include "msgpack/versioning.hpp"

#include <cstdio>
#include <stdexcept>

namespace msgpack {

/// @cond
MSGPACK_API_VERSION_NAMESPACE(v1) {
/// @endcond

class fbuffer;

/// @cond
}  // MSGPACK_API_VERSION_NAMESPACE(v1)
/// @endcond

}  // namespace msgpack

#endif // MSGPACK_V1_FBUFFER_DECL_HPP
