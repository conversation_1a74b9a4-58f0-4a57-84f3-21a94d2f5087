<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::HostTensor&lt; Element_, Layout_ &gt; Class Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="classcutlass_1_1HostTensor.html">HostTensor</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classcutlass_1_1HostTensor-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::HostTensor&lt; Element_, Layout_ &gt; Class Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Host tensor.  
</p>

<p><code>#include &lt;<a class="el" href="host__tensor_8h_source.html">host_tensor.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a99748a0d7625419be1bf1b5edea8960f"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> = Element_</td></tr>
<tr class="memdesc:a99748a0d7625419be1bf1b5edea8960f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Data type of individual access.  <a href="#a99748a0d7625419be1bf1b5edea8960f">More...</a><br /></td></tr>
<tr class="separator:a99748a0d7625419be1bf1b5edea8960f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49c87d7b2438c8350b736ab6ea31c38a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> = Layout_</td></tr>
<tr class="memdesc:a49c87d7b2438c8350b736ab6ea31c38a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mapping function from logical coordinate to linear memory.  <a href="#a49c87d7b2438c8350b736ab6ea31c38a">More...</a><br /></td></tr>
<tr class="separator:a49c87d7b2438c8350b736ab6ea31c38a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33679b84f59829c70457b9ff438101bf"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a33679b84f59829c70457b9ff438101bf">Index</a> = typename Layout::Index</td></tr>
<tr class="memdesc:a33679b84f59829c70457b9ff438101bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Index type.  <a href="#a33679b84f59829c70457b9ff438101bf">More...</a><br /></td></tr>
<tr class="separator:a33679b84f59829c70457b9ff438101bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa42c24683584f21aee1a4333ebaefccc"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> = typename Layout::LongIndex</td></tr>
<tr class="memdesc:aa42c24683584f21aee1a4333ebaefccc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Long index used for pointer offsets.  <a href="#aa42c24683584f21aee1a4333ebaefccc">More...</a><br /></td></tr>
<tr class="separator:aa42c24683584f21aee1a4333ebaefccc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab777a9a19d9354b4011eef09d0105900"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> = typename Layout::TensorCoord</td></tr>
<tr class="memdesc:ab777a9a19d9354b4011eef09d0105900"><td class="mdescLeft">&#160;</td><td class="mdescRight">Coordinate in logical tensor space.  <a href="#ab777a9a19d9354b4011eef09d0105900">More...</a><br /></td></tr>
<tr class="separator:ab777a9a19d9354b4011eef09d0105900"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8775e63b9e6f320fb8611f5e8f7fc3b9"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a8775e63b9e6f320fb8611f5e8f7fc3b9">Stride</a> = typename Layout::Stride</td></tr>
<tr class="memdesc:a8775e63b9e6f320fb8611f5e8f7fc3b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Layout's stride vector.  <a href="#a8775e63b9e6f320fb8611f5e8f7fc3b9">More...</a><br /></td></tr>
<tr class="separator:a8775e63b9e6f320fb8611f5e8f7fc3b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add43f2f758df7f5e6e028c3cba0cf277"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a> = <a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a>&lt; <a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>, <a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> &gt;</td></tr>
<tr class="memdesc:add43f2f758df7f5e6e028c3cba0cf277"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor reference to device memory.  <a href="#add43f2f758df7f5e6e028c3cba0cf277">More...</a><br /></td></tr>
<tr class="separator:add43f2f758df7f5e6e028c3cba0cf277"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae76a55dd7f4d827f042f9c9018567271"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ae76a55dd7f4d827f042f9c9018567271">ConstTensorRef</a> = typename <a class="el" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">TensorRef::ConstTensorRef</a></td></tr>
<tr class="memdesc:ae76a55dd7f4d827f042f9c9018567271"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor reference to constant device memory.  <a href="#ae76a55dd7f4d827f042f9c9018567271">More...</a><br /></td></tr>
<tr class="separator:ae76a55dd7f4d827f042f9c9018567271"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78bc9c976c204822680075685cb8363e"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a> = <a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a>&lt; <a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>, <a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> &gt;</td></tr>
<tr class="memdesc:a78bc9c976c204822680075685cb8363e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor reference to device memory.  <a href="#a78bc9c976c204822680075685cb8363e">More...</a><br /></td></tr>
<tr class="separator:a78bc9c976c204822680075685cb8363e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4445444c8f092dd1be47f1530affbaf"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ac4445444c8f092dd1be47f1530affbaf">ConstTensorView</a> = typename <a class="el" href="classcutlass_1_1TensorView.html#a71def6d54ed28dfe3b17fde3e6461578">TensorView::ConstTensorView</a></td></tr>
<tr class="memdesc:ac4445444c8f092dd1be47f1530affbaf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor reference to constant device memory.  <a href="#ac4445444c8f092dd1be47f1530affbaf">More...</a><br /></td></tr>
<tr class="separator:ac4445444c8f092dd1be47f1530affbaf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33698c7aa33255e7a2e7abc298e28f39"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39">Reference</a> = typename <a class="el" href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">TensorRef::Reference</a></td></tr>
<tr class="memdesc:a33698c7aa33255e7a2e7abc298e28f39"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reference to element in tensor.  <a href="#a33698c7aa33255e7a2e7abc298e28f39">More...</a><br /></td></tr>
<tr class="separator:a33698c7aa33255e7a2e7abc298e28f39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c6d967596e266ceab3d36f6c8d05152"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a4c6d967596e266ceab3d36f6c8d05152">ConstReference</a> = typename ConstTensorRef::Reference</td></tr>
<tr class="memdesc:a4c6d967596e266ceab3d36f6c8d05152"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constant reference to element in tensor.  <a href="#a4c6d967596e266ceab3d36f6c8d05152">More...</a><br /></td></tr>
<tr class="separator:a4c6d967596e266ceab3d36f6c8d05152"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:af50ce8b091c106ac88b75e15ab028868"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#af50ce8b091c106ac88b75e15ab028868">HostTensor</a> ()</td></tr>
<tr class="memdesc:af50ce8b091c106ac88b75e15ab028868"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default constructor.  <a href="#af50ce8b091c106ac88b75e15ab028868">More...</a><br /></td></tr>
<tr class="separator:af50ce8b091c106ac88b75e15ab028868"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf381d496b50aad3df4bb72e3d891bac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#abf381d496b50aad3df4bb72e3d891bac">HostTensor</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a>, bool <a class="el" href="classcutlass_1_1HostTensor.html#a73430856f79bedb64f9cf6b2044f38e3">device_backed</a>=true)</td></tr>
<tr class="memdesc:abf381d496b50aad3df4bb72e3d891bac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs a tensor given an extent. Assumes a packed layout.  <a href="#abf381d496b50aad3df4bb72e3d891bac">More...</a><br /></td></tr>
<tr class="separator:abf381d496b50aad3df4bb72e3d891bac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d71a824b0e1a4f1e8a88abecdff10b0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a9d71a824b0e1a4f1e8a88abecdff10b0">HostTensor</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a>, <a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#a9b78ff4fb7f317809b8cb8f70c557bea">layout</a>, bool <a class="el" href="classcutlass_1_1HostTensor.html#a73430856f79bedb64f9cf6b2044f38e3">device_backed</a>=true)</td></tr>
<tr class="memdesc:a9d71a824b0e1a4f1e8a88abecdff10b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs a tensor given an extent and layout.  <a href="#a9d71a824b0e1a4f1e8a88abecdff10b0">More...</a><br /></td></tr>
<tr class="separator:a9d71a824b0e1a4f1e8a88abecdff10b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a068d76dabce39c48b617ee7fe8d7edb8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a068d76dabce39c48b617ee7fe8d7edb8">~HostTensor</a> ()</td></tr>
<tr class="separator:a068d76dabce39c48b617ee7fe8d7edb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a898fc427538275635a55e7633cd89140"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a898fc427538275635a55e7633cd89140">reset</a> ()</td></tr>
<tr class="memdesc:a898fc427538275635a55e7633cd89140"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clears the <a class="el" href="classcutlass_1_1HostTensor.html" title="Host tensor. ">HostTensor</a> allocation to size/capacity = 0.  <a href="#a898fc427538275635a55e7633cd89140">More...</a><br /></td></tr>
<tr class="separator:a898fc427538275635a55e7633cd89140"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6405f217fc2a52f8e23180c9f8160899"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a6405f217fc2a52f8e23180c9f8160899">reserve</a> (size_t count, bool device_backed_=true)</td></tr>
<tr class="memdesc:a6405f217fc2a52f8e23180c9f8160899"><td class="mdescLeft">&#160;</td><td class="mdescRight">Resizes internal memory allocations without affecting layout or extent.  <a href="#a6405f217fc2a52f8e23180c9f8160899">More...</a><br /></td></tr>
<tr class="separator:a6405f217fc2a52f8e23180c9f8160899"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f23ad5953f9cd5dff8e97d9edbc0cf0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a4f23ad5953f9cd5dff8e97d9edbc0cf0">reset</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a>, <a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#a9b78ff4fb7f317809b8cb8f70c557bea">layout</a>, bool device_backed_=true)</td></tr>
<tr class="separator:a4f23ad5953f9cd5dff8e97d9edbc0cf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab91b456881d5f9c0f52a4bc89d448d55"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ab91b456881d5f9c0f52a4bc89d448d55">reset</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a>, bool device_backed_=true)</td></tr>
<tr class="separator:ab91b456881d5f9c0f52a4bc89d448d55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac61cee5b25585439f4ccc7d7249ee853"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ac61cee5b25585439f4ccc7d7249ee853">resize</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a>, <a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#a9b78ff4fb7f317809b8cb8f70c557bea">layout</a>, bool device_backed_=true)</td></tr>
<tr class="separator:ac61cee5b25585439f4ccc7d7249ee853"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a784d0b84de512c1bff131710226e2338"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a784d0b84de512c1bff131710226e2338">resize</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;<a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a>, bool device_backed_=true)</td></tr>
<tr class="separator:a784d0b84de512c1bff131710226e2338"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aacf36383bd4608f6d30c561ce4851b83"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#aacf36383bd4608f6d30c561ce4851b83">size</a> () const </td></tr>
<tr class="memdesc:aacf36383bd4608f6d30c561ce4851b83"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the number of elements stored in the host tensor.  <a href="#aacf36383bd4608f6d30c561ce4851b83">More...</a><br /></td></tr>
<tr class="separator:aacf36383bd4608f6d30c561ce4851b83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ea111e8fcba15c07f0cf679e1eec7f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#aa6ea111e8fcba15c07f0cf679e1eec7f">capacity</a> () const </td></tr>
<tr class="memdesc:aa6ea111e8fcba15c07f0cf679e1eec7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the logical capacity based on extent and layout. May differ from <a class="el" href="classcutlass_1_1HostTensor.html#aacf36383bd4608f6d30c561ce4851b83" title="Returns the number of elements stored in the host tensor. ">size()</a>.  <a href="#aa6ea111e8fcba15c07f0cf679e1eec7f">More...</a><br /></td></tr>
<tr class="separator:aa6ea111e8fcba15c07f0cf679e1eec7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d34307e09d4ea09d68abbf31fe33788"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a7d34307e09d4ea09d68abbf31fe33788">host_data</a> ()</td></tr>
<tr class="memdesc:a7d34307e09d4ea09d68abbf31fe33788"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets pointer to host data.  <a href="#a7d34307e09d4ea09d68abbf31fe33788">More...</a><br /></td></tr>
<tr class="separator:a7d34307e09d4ea09d68abbf31fe33788"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a252ce9281a40863f32be25d3b40d6373"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a252ce9281a40863f32be25d3b40d6373">host_data_ptr_offset</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset)</td></tr>
<tr class="memdesc:a252ce9281a40863f32be25d3b40d6373"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets pointer to host data with a pointer offset.  <a href="#a252ce9281a40863f32be25d3b40d6373">More...</a><br /></td></tr>
<tr class="separator:a252ce9281a40863f32be25d3b40d6373"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adafe2bee53260d47bc60479c50953f57"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39">Reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#adafe2bee53260d47bc60479c50953f57">host_data</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> idx)</td></tr>
<tr class="memdesc:adafe2bee53260d47bc60479c50953f57"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets a reference to an element in host memory.  <a href="#adafe2bee53260d47bc60479c50953f57">More...</a><br /></td></tr>
<tr class="separator:adafe2bee53260d47bc60479c50953f57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3dd62ca461666b3ac7e690e2befecaae"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a3dd62ca461666b3ac7e690e2befecaae">host_data</a> () const </td></tr>
<tr class="memdesc:a3dd62ca461666b3ac7e690e2befecaae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets pointer to host data.  <a href="#a3dd62ca461666b3ac7e690e2befecaae">More...</a><br /></td></tr>
<tr class="separator:a3dd62ca461666b3ac7e690e2befecaae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7741d51bf7d241d4821e52e47b704c69"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a4c6d967596e266ceab3d36f6c8d05152">ConstReference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a7741d51bf7d241d4821e52e47b704c69">host_data</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> idx) const </td></tr>
<tr class="memdesc:a7741d51bf7d241d4821e52e47b704c69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets a constant reference to an element in host memory.  <a href="#a7741d51bf7d241d4821e52e47b704c69">More...</a><br /></td></tr>
<tr class="separator:a7741d51bf7d241d4821e52e47b704c69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca2b28a16fc92d29102d00f154a1dfd1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#aca2b28a16fc92d29102d00f154a1dfd1">device_data</a> ()</td></tr>
<tr class="memdesc:aca2b28a16fc92d29102d00f154a1dfd1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets pointer to device data.  <a href="#aca2b28a16fc92d29102d00f154a1dfd1">More...</a><br /></td></tr>
<tr class="separator:aca2b28a16fc92d29102d00f154a1dfd1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81043b0539c8d18c40957411dd149e28"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a81043b0539c8d18c40957411dd149e28">device_data_ptr_offset</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset)</td></tr>
<tr class="memdesc:a81043b0539c8d18c40957411dd149e28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets pointer to device data with a pointer offset.  <a href="#a81043b0539c8d18c40957411dd149e28">More...</a><br /></td></tr>
<tr class="separator:a81043b0539c8d18c40957411dd149e28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abecb0dce978ea2c542d7d87a35f7997a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#abecb0dce978ea2c542d7d87a35f7997a">device_data</a> () const </td></tr>
<tr class="memdesc:abecb0dce978ea2c542d7d87a35f7997a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets pointer to device data.  <a href="#abecb0dce978ea2c542d7d87a35f7997a">More...</a><br /></td></tr>
<tr class="separator:abecb0dce978ea2c542d7d87a35f7997a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac921be812016052c690d2dc808d415f1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ac921be812016052c690d2dc808d415f1">host_ref</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0)</td></tr>
<tr class="memdesc:ac921be812016052c690d2dc808d415f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#ac921be812016052c690d2dc808d415f1">More...</a><br /></td></tr>
<tr class="separator:ac921be812016052c690d2dc808d415f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c5b60678dfa105b10c87dfaab4cf395"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#ae76a55dd7f4d827f042f9c9018567271">ConstTensorRef</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a3c5b60678dfa105b10c87dfaab4cf395">host_ref</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0) const </td></tr>
<tr class="memdesc:a3c5b60678dfa105b10c87dfaab4cf395"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#a3c5b60678dfa105b10c87dfaab4cf395">More...</a><br /></td></tr>
<tr class="separator:a3c5b60678dfa105b10c87dfaab4cf395"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a55a73e5ff7c7404c0bdee5f2b578b876"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a55a73e5ff7c7404c0bdee5f2b578b876">device_ref</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0)</td></tr>
<tr class="memdesc:a55a73e5ff7c7404c0bdee5f2b578b876"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#a55a73e5ff7c7404c0bdee5f2b578b876">More...</a><br /></td></tr>
<tr class="separator:a55a73e5ff7c7404c0bdee5f2b578b876"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bf91f711ef17492809c09d53364cb35"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#ae76a55dd7f4d827f042f9c9018567271">ConstTensorRef</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a4bf91f711ef17492809c09d53364cb35">device_ref</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0) const </td></tr>
<tr class="memdesc:a4bf91f711ef17492809c09d53364cb35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#a4bf91f711ef17492809c09d53364cb35">More...</a><br /></td></tr>
<tr class="separator:a4bf91f711ef17492809c09d53364cb35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75ce60d02c9d8fdcbcaceecc6b3ec7fa"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a75ce60d02c9d8fdcbcaceecc6b3ec7fa">host_view</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0)</td></tr>
<tr class="memdesc:a75ce60d02c9d8fdcbcaceecc6b3ec7fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#a75ce60d02c9d8fdcbcaceecc6b3ec7fa">More...</a><br /></td></tr>
<tr class="separator:a75ce60d02c9d8fdcbcaceecc6b3ec7fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a232e640ebb4b8cf21b44653d7800c5a7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#ac4445444c8f092dd1be47f1530affbaf">ConstTensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a232e640ebb4b8cf21b44653d7800c5a7">host_view</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0) const </td></tr>
<tr class="memdesc:a232e640ebb4b8cf21b44653d7800c5a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#a232e640ebb4b8cf21b44653d7800c5a7">More...</a><br /></td></tr>
<tr class="separator:a232e640ebb4b8cf21b44653d7800c5a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a075b666917a43c9bc168bfff6db27203"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a075b666917a43c9bc168bfff6db27203">device_view</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0)</td></tr>
<tr class="memdesc:a075b666917a43c9bc168bfff6db27203"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#a075b666917a43c9bc168bfff6db27203">More...</a><br /></td></tr>
<tr class="separator:a075b666917a43c9bc168bfff6db27203"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d1c49888cf678d3d5469eba4e911337"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#ac4445444c8f092dd1be47f1530affbaf">ConstTensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a6d1c49888cf678d3d5469eba4e911337">device_view</a> (<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> ptr_element_offset=0) const </td></tr>
<tr class="memdesc:a6d1c49888cf678d3d5469eba4e911337"><td class="mdescLeft">&#160;</td><td class="mdescRight">Accesses the tensor reference pointing to data.  <a href="#a6d1c49888cf678d3d5469eba4e911337">More...</a><br /></td></tr>
<tr class="separator:a6d1c49888cf678d3d5469eba4e911337"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73430856f79bedb64f9cf6b2044f38e3"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a73430856f79bedb64f9cf6b2044f38e3">device_backed</a> () const </td></tr>
<tr class="memdesc:a73430856f79bedb64f9cf6b2044f38e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if device memory is allocated.  <a href="#a73430856f79bedb64f9cf6b2044f38e3">More...</a><br /></td></tr>
<tr class="separator:a73430856f79bedb64f9cf6b2044f38e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b78ff4fb7f317809b8cb8f70c557bea"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a9b78ff4fb7f317809b8cb8f70c557bea">layout</a> ()</td></tr>
<tr class="memdesc:a9b78ff4fb7f317809b8cb8f70c557bea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout object.  <a href="#a9b78ff4fb7f317809b8cb8f70c557bea">More...</a><br /></td></tr>
<tr class="separator:a9b78ff4fb7f317809b8cb8f70c557bea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61fe837629d7663cbb93ea8cfc177077"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a61fe837629d7663cbb93ea8cfc177077">layout</a> () const </td></tr>
<tr class="memdesc:a61fe837629d7663cbb93ea8cfc177077"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout object.  <a href="#a61fe837629d7663cbb93ea8cfc177077">More...</a><br /></td></tr>
<tr class="separator:a61fe837629d7663cbb93ea8cfc177077"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23976f8c123de032cf4a2632a894fcf2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a8775e63b9e6f320fb8611f5e8f7fc3b9">Stride</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a23976f8c123de032cf4a2632a894fcf2">stride</a> () const </td></tr>
<tr class="memdesc:a23976f8c123de032cf4a2632a894fcf2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout object's stride vector.  <a href="#a23976f8c123de032cf4a2632a894fcf2">More...</a><br /></td></tr>
<tr class="separator:a23976f8c123de032cf4a2632a894fcf2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a241cd2fe7dfe62b3e68ef75334bd2fda"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a8775e63b9e6f320fb8611f5e8f7fc3b9">Stride</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a241cd2fe7dfe62b3e68ef75334bd2fda">stride</a> ()</td></tr>
<tr class="memdesc:a241cd2fe7dfe62b3e68ef75334bd2fda"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout object's stride vector.  <a href="#a241cd2fe7dfe62b3e68ef75334bd2fda">More...</a><br /></td></tr>
<tr class="separator:a241cd2fe7dfe62b3e68ef75334bd2fda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a16e522cac85735cfcb056dc928de18"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a33679b84f59829c70457b9ff438101bf">Index</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a4a16e522cac85735cfcb056dc928de18">stride</a> (int dim) const </td></tr>
<tr class="memdesc:a4a16e522cac85735cfcb056dc928de18"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout object's stride in a given physical dimension.  <a href="#a4a16e522cac85735cfcb056dc928de18">More...</a><br /></td></tr>
<tr class="separator:a4a16e522cac85735cfcb056dc928de18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a439278acead2d26cb453d2949019fb68"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a33679b84f59829c70457b9ff438101bf">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a439278acead2d26cb453d2949019fb68">stride</a> (int dim)</td></tr>
<tr class="memdesc:a439278acead2d26cb453d2949019fb68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the layout object's stride in a given physical dimension.  <a href="#a439278acead2d26cb453d2949019fb68">More...</a><br /></td></tr>
<tr class="separator:a439278acead2d26cb453d2949019fb68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3e08629bab5304f999fb5115453a714"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ac3e08629bab5304f999fb5115453a714">offset</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;coord) const </td></tr>
<tr class="memdesc:ac3e08629bab5304f999fb5115453a714"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes the offset of an index from the origin of the tensor.  <a href="#ac3e08629bab5304f999fb5115453a714">More...</a><br /></td></tr>
<tr class="separator:ac3e08629bab5304f999fb5115453a714"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1253b57fd95f16b2dec9e36e84051d4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39">Reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ab1253b57fd95f16b2dec9e36e84051d4">at</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;coord)</td></tr>
<tr class="memdesc:ab1253b57fd95f16b2dec9e36e84051d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a reference to the element at the logical <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> in host memory.  <a href="#ab1253b57fd95f16b2dec9e36e84051d4">More...</a><br /></td></tr>
<tr class="separator:ab1253b57fd95f16b2dec9e36e84051d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae261f1b7ef78cc52dcb77f1a2fd05b69"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#a4c6d967596e266ceab3d36f6c8d05152">ConstReference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ae261f1b7ef78cc52dcb77f1a2fd05b69">at</a> (<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;coord) const </td></tr>
<tr class="memdesc:ae261f1b7ef78cc52dcb77f1a2fd05b69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a const reference to the element at the logical <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> in host memory.  <a href="#ae261f1b7ef78cc52dcb77f1a2fd05b69">More...</a><br /></td></tr>
<tr class="separator:ae261f1b7ef78cc52dcb77f1a2fd05b69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1264450c122c1853231fc64b35a47b8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#af1264450c122c1853231fc64b35a47b8">extent</a> () const </td></tr>
<tr class="memdesc:af1264450c122c1853231fc64b35a47b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the extent of the tensor.  <a href="#af1264450c122c1853231fc64b35a47b8">More...</a><br /></td></tr>
<tr class="separator:af1264450c122c1853231fc64b35a47b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a947176a4ddab5b784c98a0f1896892"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a8a947176a4ddab5b784c98a0f1896892">extent</a> ()</td></tr>
<tr class="memdesc:a8a947176a4ddab5b784c98a0f1896892"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the extent of the tensor.  <a href="#a8a947176a4ddab5b784c98a0f1896892">More...</a><br /></td></tr>
<tr class="separator:a8a947176a4ddab5b784c98a0f1896892"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aea45851485df33ee2afb2a30bb82ebfc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#aea45851485df33ee2afb2a30bb82ebfc">sync_host</a> ()</td></tr>
<tr class="memdesc:aea45851485df33ee2afb2a30bb82ebfc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copies data from device to host.  <a href="#aea45851485df33ee2afb2a30bb82ebfc">More...</a><br /></td></tr>
<tr class="separator:aea45851485df33ee2afb2a30bb82ebfc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af339765c90429fe99b98a0da6a627421"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#af339765c90429fe99b98a0da6a627421">sync_device</a> ()</td></tr>
<tr class="memdesc:af339765c90429fe99b98a0da6a627421"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copies data from host to device.  <a href="#af339765c90429fe99b98a0da6a627421">More...</a><br /></td></tr>
<tr class="separator:af339765c90429fe99b98a0da6a627421"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b4858efb0356a6fc01bb9f55f0ad3b2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a2b4858efb0356a6fc01bb9f55f0ad3b2">copy_in_device_to_host</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *ptr_device, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1)</td></tr>
<tr class="memdesc:a2b4858efb0356a6fc01bb9f55f0ad3b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#a2b4858efb0356a6fc01bb9f55f0ad3b2">More...</a><br /></td></tr>
<tr class="separator:a2b4858efb0356a6fc01bb9f55f0ad3b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5686ea068c8f3e820ccff015e95bc474"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a5686ea068c8f3e820ccff015e95bc474">copy_in_device_to_device</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *ptr_device, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1)</td></tr>
<tr class="memdesc:a5686ea068c8f3e820ccff015e95bc474"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#a5686ea068c8f3e820ccff015e95bc474">More...</a><br /></td></tr>
<tr class="separator:a5686ea068c8f3e820ccff015e95bc474"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad385330c69ecd7bd0b6c3660815253fa"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ad385330c69ecd7bd0b6c3660815253fa">copy_in_host_to_device</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *ptr_host, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1)</td></tr>
<tr class="memdesc:ad385330c69ecd7bd0b6c3660815253fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#ad385330c69ecd7bd0b6c3660815253fa">More...</a><br /></td></tr>
<tr class="separator:ad385330c69ecd7bd0b6c3660815253fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae11229ea69460ca174c5e6f9815eb97f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ae11229ea69460ca174c5e6f9815eb97f">copy_in_host_to_host</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *ptr_host, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1)</td></tr>
<tr class="memdesc:ae11229ea69460ca174c5e6f9815eb97f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#ae11229ea69460ca174c5e6f9815eb97f">More...</a><br /></td></tr>
<tr class="separator:ae11229ea69460ca174c5e6f9815eb97f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3051d2842b3aa3815e2ea5f53abfc2a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ab3051d2842b3aa3815e2ea5f53abfc2a">copy_out_device_to_host</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *ptr_host, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1) const </td></tr>
<tr class="memdesc:ab3051d2842b3aa3815e2ea5f53abfc2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#ab3051d2842b3aa3815e2ea5f53abfc2a">More...</a><br /></td></tr>
<tr class="separator:ab3051d2842b3aa3815e2ea5f53abfc2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7179440d39b0445113b30b7a460a1ec"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ab7179440d39b0445113b30b7a460a1ec">copy_out_device_to_device</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *ptr_device, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1) const </td></tr>
<tr class="memdesc:ab7179440d39b0445113b30b7a460a1ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#ab7179440d39b0445113b30b7a460a1ec">More...</a><br /></td></tr>
<tr class="separator:ab7179440d39b0445113b30b7a460a1ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abeefdb8bccb2d8d751fdb22fa7e8ef0c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#abeefdb8bccb2d8d751fdb22fa7e8ef0c">copy_out_host_to_device</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *ptr_device, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1) const </td></tr>
<tr class="memdesc:abeefdb8bccb2d8d751fdb22fa7e8ef0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#abeefdb8bccb2d8d751fdb22fa7e8ef0c">More...</a><br /></td></tr>
<tr class="separator:abeefdb8bccb2d8d751fdb22fa7e8ef0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40da221db96cfda76ba5623856c66bf1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#a40da221db96cfda76ba5623856c66bf1">copy_out_host_to_host</a> (<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *ptr_host, <a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> count=-1) const </td></tr>
<tr class="memdesc:a40da221db96cfda76ba5623856c66bf1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy data from a caller-supplied device pointer into host memory.  <a href="#a40da221db96cfda76ba5623856c66bf1">More...</a><br /></td></tr>
<tr class="separator:a40da221db96cfda76ba5623856c66bf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:aa3458af41bf057e8bcfd895388a52659"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#aa3458af41bf057e8bcfd895388a52659">kRank</a> = Layout::kRank</td></tr>
<tr class="memdesc:aa3458af41bf057e8bcfd895388a52659"><td class="mdescLeft">&#160;</td><td class="mdescRight">Logical rank of tensor index space.  <a href="#aa3458af41bf057e8bcfd895388a52659">More...</a><br /></td></tr>
<tr class="separator:aa3458af41bf057e8bcfd895388a52659"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8d8df8fbb877de1d29978288405bf5f"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1HostTensor.html#ad8d8df8fbb877de1d29978288405bf5f">kElementsPerStoredItem</a> = (<a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>&gt;::value &lt; 8 ? sizeof(<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>) * 8 / <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>&gt;::value : 1)</td></tr>
<tr class="memdesc:ad8d8df8fbb877de1d29978288405bf5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Used to handle packing of subbyte elements.  <a href="#ad8d8df8fbb877de1d29978288405bf5f">More...</a><br /></td></tr>
<tr class="separator:ad8d8df8fbb877de1d29978288405bf5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a4c6d967596e266ceab3d36f6c8d05152"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#a4c6d967596e266ceab3d36f6c8d05152">ConstReference</a> =  typename ConstTensorRef::Reference</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae76a55dd7f4d827f042f9c9018567271"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#ae76a55dd7f4d827f042f9c9018567271">ConstTensorRef</a> =  typename <a class="el" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">TensorRef::ConstTensorRef</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac4445444c8f092dd1be47f1530affbaf"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#ac4445444c8f092dd1be47f1530affbaf">ConstTensorView</a> =  typename <a class="el" href="classcutlass_1_1TensorView.html#a71def6d54ed28dfe3b17fde3e6461578">TensorView::ConstTensorView</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a99748a0d7625419be1bf1b5edea8960f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> =  Element_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a33679b84f59829c70457b9ff438101bf"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#a33679b84f59829c70457b9ff438101bf">Index</a> =  typename Layout::Index</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a49c87d7b2438c8350b736ab6ea31c38a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> =  Layout_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa42c24683584f21aee1a4333ebaefccc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> =  typename Layout::LongIndex</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a33698c7aa33255e7a2e7abc298e28f39"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39">Reference</a> =  typename <a class="el" href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">TensorRef::Reference</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8775e63b9e6f320fb8611f5e8f7fc3b9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#a8775e63b9e6f320fb8611f5e8f7fc3b9">Stride</a> =  typename Layout::Stride</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab777a9a19d9354b4011eef09d0105900"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> =  typename Layout::TensorCoord</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="add43f2f758df7f5e6e028c3cba0cf277"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a> =  <a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a>&lt;<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>, <a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a78bc9c976c204822680075685cb8363e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a> =  <a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a>&lt;<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>, <a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="af50ce8b091c106ac88b75e15ab028868"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html">HostTensor</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abf381d496b50aad3df4bb72e3d891bac"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html">HostTensor</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>extent</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>device_backed</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9d71a824b0e1a4f1e8a88abecdff10b0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::<a class="el" href="classcutlass_1_1HostTensor.html">HostTensor</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>extent</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> const &amp;&#160;</td>
          <td class="paramname"><em>layout</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>device_backed</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a068d76dabce39c48b617ee7fe8d7edb8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::~<a class="el" href="classcutlass_1_1HostTensor.html">HostTensor</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ab1253b57fd95f16b2dec9e36e84051d4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39">Reference</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::at </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae261f1b7ef78cc52dcb77f1a2fd05b69"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a4c6d967596e266ceab3d36f6c8d05152">ConstReference</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::at </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa6ea111e8fcba15c07f0cf679e1eec7f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::capacity </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5686ea068c8f3e820ccff015e95bc474"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_in_device_to_device </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *&#160;</td>
          <td class="paramname"><em>ptr_device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_device</td><td>source device memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="a2b4858efb0356a6fc01bb9f55f0ad3b2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_in_device_to_host </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *&#160;</td>
          <td class="paramname"><em>ptr_device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_device</td><td>source device memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ad385330c69ecd7bd0b6c3660815253fa"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_in_host_to_device </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *&#160;</td>
          <td class="paramname"><em>ptr_host</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_host</td><td>source host memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ae11229ea69460ca174c5e6f9815eb97f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_in_host_to_host </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const *&#160;</td>
          <td class="paramname"><em>ptr_host</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_host</td><td>source host memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ab7179440d39b0445113b30b7a460a1ec"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_out_device_to_device </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td>
          <td class="paramname"><em>ptr_device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_device</td><td>source device memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ab3051d2842b3aa3815e2ea5f53abfc2a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_out_device_to_host </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td>
          <td class="paramname"><em>ptr_host</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_host</td><td>source device memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="abeefdb8bccb2d8d751fdb22fa7e8ef0c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_out_host_to_device </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td>
          <td class="paramname"><em>ptr_device</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_device</td><td>source host memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="a40da221db96cfda76ba5623856c66bf1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::copy_out_host_to_host </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> *&#160;</td>
          <td class="paramname"><em>ptr_host</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>count</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; number of elements to transfer; if negative, entire tensor is overwritten. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptr_host</td><td>source host memory </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="a73430856f79bedb64f9cf6b2044f38e3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_backed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aca2b28a16fc92d29102d00f154a1dfd1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>* <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abecb0dce978ea2c542d7d87a35f7997a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const* <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a81043b0539c8d18c40957411dd149e28"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>* <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_data_ptr_offset </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a55a73e5ff7c7404c0bdee5f2b578b876"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_ref </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4bf91f711ef17492809c09d53364cb35"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#ae76a55dd7f4d827f042f9c9018567271">ConstTensorRef</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_ref </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a075b666917a43c9bc168bfff6db27203"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_view </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6d1c49888cf678d3d5469eba4e911337"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#ac4445444c8f092dd1be47f1530affbaf">ConstTensorView</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::device_view </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af1264450c122c1853231fc64b35a47b8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::extent </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8a947176a4ddab5b784c98a0f1896892"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a>&amp; <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::extent </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7d34307e09d4ea09d68abbf31fe33788"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>* <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adafe2bee53260d47bc60479c50953f57"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a33698c7aa33255e7a2e7abc298e28f39">Reference</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_data </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>idx</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3dd62ca461666b3ac7e690e2befecaae"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a> const* <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7741d51bf7d241d4821e52e47b704c69"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a4c6d967596e266ceab3d36f6c8d05152">ConstReference</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_data </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>idx</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a252ce9281a40863f32be25d3b40d6373"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>* <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_data_ptr_offset </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac921be812016052c690d2dc808d415f1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#add43f2f758df7f5e6e028c3cba0cf277">TensorRef</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_ref </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3c5b60678dfa105b10c87dfaab4cf395"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#ae76a55dd7f4d827f042f9c9018567271">ConstTensorRef</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_ref </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a75ce60d02c9d8fdcbcaceecc6b3ec7fa"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a78bc9c976c204822680075685cb8363e">TensorView</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_view </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a232e640ebb4b8cf21b44653d7800c5a7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#ac4445444c8f092dd1be47f1530affbaf">ConstTensorView</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::host_view </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a>&#160;</td>
          <td class="paramname"><em>ptr_element_offset</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9b78ff4fb7f317809b8cb8f70c557bea"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a>&amp; <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::layout </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a61fe837629d7663cbb93ea8cfc177077"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::layout </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac3e08629bab5304f999fb5115453a714"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#aa42c24683584f21aee1a4333ebaefccc">LongIndex</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::offset </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6405f217fc2a52f8e23180c9f8160899"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::reserve </td>
          <td>(</td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>count</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>device_backed_</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>&lt; if true, device memory is also allocated </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">count</td><td>size of tensor in elements </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="a898fc427538275635a55e7633cd89140"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::reset </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4f23ad5953f9cd5dff8e97d9edbc0cf0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::reset </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>extent</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> const &amp;&#160;</td>
          <td class="paramname"><em>layout</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>device_backed_</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Updates the extent and layout of the <a class="el" href="classcutlass_1_1HostTensor.html" title="Host tensor. ">HostTensor</a>. Allocates memory according to the new extent and layout. </p>
<p>&lt; if true, device memory is also allocated. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">extent</td><td>extent of logical tensor </td></tr>
    <tr><td class="paramname">layout</td><td>layout object of tensor </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ab91b456881d5f9c0f52a4bc89d448d55"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::reset </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>extent</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>device_backed_</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Updates the extent and layout of the <a class="el" href="classcutlass_1_1HostTensor.html" title="Host tensor. ">HostTensor</a>. Allocates memory according to the new extent and layout. Assumes a packed tensor configuration. </p>
<p>&lt; if true, device memory is also allocated. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">extent</td><td>extent of logical tensor </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ac61cee5b25585439f4ccc7d7249ee853"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::resize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>extent</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#a49c87d7b2438c8350b736ab6ea31c38a">Layout</a> const &amp;&#160;</td>
          <td class="paramname"><em>layout</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>device_backed_</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Changes the size of the logical tensor. Only allocates memory if new capacity exceeds reserved capacity. To force allocation, call <a class="el" href="classcutlass_1_1HostTensor.html#a898fc427538275635a55e7633cd89140" title="Clears the HostTensor allocation to size/capacity = 0. ">reset()</a>. </p>
<p>&lt; if true, device memory is also allocated. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">extent</td><td>extent of logical tensor </td></tr>
    <tr><td class="paramname">layout</td><td>layout object of tensor </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="a784d0b84de512c1bff131710226e2338"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::resize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1HostTensor.html#ab777a9a19d9354b4011eef09d0105900">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>extent</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>device_backed_</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Changes the size of the logical tensor. Only allocates memory if new capacity exceeds reserved capacity. To force allocation, call <a class="el" href="classcutlass_1_1HostTensor.html#a898fc427538275635a55e7633cd89140" title="Clears the HostTensor allocation to size/capacity = 0. ">reset()</a>. Note, this form of <a class="el" href="classcutlass_1_1HostTensor.html#ac61cee5b25585439f4ccc7d7249ee853">resize()</a> assumes a packed tensor configuration. </p>
<p>&lt; if true, device memory is also allocated. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">extent</td><td>extent of logical tensor </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="aacf36383bd4608f6d30c561ce4851b83"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::size </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a23976f8c123de032cf4a2632a894fcf2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a8775e63b9e6f320fb8611f5e8f7fc3b9">Stride</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::stride </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a241cd2fe7dfe62b3e68ef75334bd2fda"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a8775e63b9e6f320fb8611f5e8f7fc3b9">Stride</a>&amp; <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::stride </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4a16e522cac85735cfcb056dc928de18"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a33679b84f59829c70457b9ff438101bf">Index</a> <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::stride </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dim</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a439278acead2d26cb453d2949019fb68"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1HostTensor.html#a33679b84f59829c70457b9ff438101bf">Index</a>&amp; <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::stride </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dim</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af339765c90429fe99b98a0da6a627421"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::sync_device </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aea45851485df33ee2afb2a30bb82ebfc"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::sync_host </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="ad8d8df8fbb877de1d29978288405bf5f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::kElementsPerStoredItem = (<a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>&gt;::value &lt; 8 ? sizeof(<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>) * 8 / <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;<a class="el" href="classcutlass_1_1HostTensor.html#a99748a0d7625419be1bf1b5edea8960f">Element</a>&gt;::value : 1)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa3458af41bf057e8bcfd895388a52659"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element_ , typename Layout_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="classcutlass_1_1HostTensor.html">cutlass::HostTensor</a>&lt; Element_, Layout_ &gt;::kRank = Layout::kRank</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="host__tensor_8h_source.html">host_tensor.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
