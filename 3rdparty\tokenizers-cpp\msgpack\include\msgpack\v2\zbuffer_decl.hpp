//
// MessagePack for C++ deflate buffer implementation
//
// Copyright (C) 2016 <PERSON>ON<PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_V2_ZBUFFER_DECL_HPP
#define MSGPACK_V2_ZBUFFER_DECL_HPP

#include "msgpack/v1/zbuffer_decl.hpp"

namespace msgpack {

/// @cond
MSGPACK_API_VERSION_NAMESPACE(v2) {
/// @endcond

using v1::zbuffer;

/// @cond
}  // MSGPACK_API_VERSION_NAMESPACE(v2)
/// @endcond

}  // namespace msgpack

#endif // MSGPACK_V2_ZBUFFER_DECL_HPP
