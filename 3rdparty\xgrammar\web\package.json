{"name": "@mlc-ai/web-xgrammar", "version": "0.1.0", "description": "", "main": "lib/index.js", "types": "lib/index.d.ts", "type": "module", "scripts": {"build": "./build.sh; rollup -c", "lint": "npx eslint .", "test": "./run_test.sh"}, "files": ["lib"], "repository": {"type": "git", "url": "git+https://github.com/mlc-ai/xgrammar"}, "keywords": ["machine_learning", "llm", "nlp"], "license": "Apache-2.0", "homepage": "https://github.com/mlc-ai/xgrammar/tree/main/web", "devDependencies": {"@jest/globals": "^29.7.0", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-wasm": "^5.1.2", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "eslint": "^8.41.0", "jest": "^29.7.0", "rollup": "^2.56.2", "rollup-plugin-typescript2": "^0.34.1", "ts-jest": "^29.2.5", "tslib": "^2.3.1", "typescript": "^4.9.5", "@mlc-ai/web-tokenizers": "^0.1.5"}}