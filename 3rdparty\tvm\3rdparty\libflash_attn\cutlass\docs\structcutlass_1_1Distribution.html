<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::Distribution Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1Distribution-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::Distribution Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="structcutlass_1_1Distribution.html" title="Distribution type. ">Distribution</a> type.  
</p>

<p><code>#include &lt;<a class="el" href="distribution_8h_source.html">distribution.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a499f4023e0d42356ce71d38cc32bf92a"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">Kind</a> { <br />
&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa2ff14122c59a823654b84764f68e597b">Invalid</a>, 
<a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6">Uniform</a>, 
<a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5">Gaussian</a>, 
<a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6">Identity</a>, 
<br />
&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc">Sequential</a>
<br />
 }<tr class="memdesc:a499f4023e0d42356ce71d38cc32bf92a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Variant types.  <a href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">More...</a><br /></td></tr>
</td></tr>
<tr class="separator:a499f4023e0d42356ce71d38cc32bf92a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a40f0b9d0f92199f8a49c931d34dd8c8a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#a40f0b9d0f92199f8a49c931d34dd8c8a">Distribution</a> ()</td></tr>
<tr class="separator:a40f0b9d0f92199f8a49c931d34dd8c8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ef87d3af6af0a815a56e74645f32991"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#a5ef87d3af6af0a815a56e74645f32991">set_uniform</a> (double _min, double _max, int _int_scale=0)</td></tr>
<tr class="memdesc:a5ef87d3af6af0a815a56e74645f32991"><td class="mdescLeft">&#160;</td><td class="mdescRight">Configures distribution as uniform random.  <a href="#a5ef87d3af6af0a815a56e74645f32991">More...</a><br /></td></tr>
<tr class="separator:a5ef87d3af6af0a815a56e74645f32991"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad594b5ec1d577e8ef03d4d808a8220b1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#ad594b5ec1d577e8ef03d4d808a8220b1">set_gaussian</a> (double _mean, double _stddev, int _int_scale=0)</td></tr>
<tr class="memdesc:ad594b5ec1d577e8ef03d4d808a8220b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Configures distribution as Gaussian distribution.  <a href="#ad594b5ec1d577e8ef03d4d808a8220b1">More...</a><br /></td></tr>
<tr class="separator:ad594b5ec1d577e8ef03d4d808a8220b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad2cf02af3d520544d89843cc4295858"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#aad2cf02af3d520544d89843cc4295858">set_identity</a> ()</td></tr>
<tr class="memdesc:aad2cf02af3d520544d89843cc4295858"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets identity.  <a href="#aad2cf02af3d520544d89843cc4295858">More...</a><br /></td></tr>
<tr class="separator:aad2cf02af3d520544d89843cc4295858"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7fb9689c8ae17d5c72c7d0376fa93767"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#a7fb9689c8ae17d5c72c7d0376fa93767">set_sequential</a> (double <a class="el" href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">start</a>, double <a class="el" href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">delta</a>, int _int_scale=0)</td></tr>
<tr class="memdesc:a7fb9689c8ae17d5c72c7d0376fa93767"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets sequential.  <a href="#a7fb9689c8ae17d5c72c7d0376fa93767">More...</a><br /></td></tr>
<tr class="separator:a7fb9689c8ae17d5c72c7d0376fa93767"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ab073733c9661885698700933820aef8e"><td class="memItemLeft" >union {</td></tr>
<tr class="memitem:a09337148a14a8e3283a0c6954fa7c910"><td class="memItemLeft" >&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:aeb204740b737b0a91eeca5d147281446"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;double&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#a846430e3a21ed25c779fc6e714bc1bcc">min</a></td></tr>
<tr class="separator:aeb204740b737b0a91eeca5d147281446"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aceb7acc9655fc5576caea4e62cc40394"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;double&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#a824641fd3addfa360999614970adfac0">max</a></td></tr>
<tr class="separator:aceb7acc9655fc5576caea4e62cc40394"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09337148a14a8e3283a0c6954fa7c910"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">uniform</a></td></tr>
<tr class="memdesc:a09337148a14a8e3283a0c6954fa7c910"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uniform distribution.  <a href="#a09337148a14a8e3283a0c6954fa7c910">More...</a><br /></td></tr>
<tr class="separator:a09337148a14a8e3283a0c6954fa7c910"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac495e24b3cb8ca6907aae675354eae59"><td class="memItemLeft" >&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:a4aadc81412d11ce52edeb03bcd150a8e"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;double&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#a776df53c7ad1b7de983c9f9d17d7438c">mean</a></td></tr>
<tr class="separator:a4aadc81412d11ce52edeb03bcd150a8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed6233a07ff0ecfb00667b2e5f60c5b2"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;double&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#aee3bd32372426422bb02b335704965aa">stddev</a></td></tr>
<tr class="separator:aed6233a07ff0ecfb00667b2e5f60c5b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac495e24b3cb8ca6907aae675354eae59"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">gaussian</a></td></tr>
<tr class="memdesc:ac495e24b3cb8ca6907aae675354eae59"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gaussian distribution.  <a href="#ac495e24b3cb8ca6907aae675354eae59">More...</a><br /></td></tr>
<tr class="separator:ac495e24b3cb8ca6907aae675354eae59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98baf1e7ba5bcddcdf5117316192934c"><td class="memItemLeft" >&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:a6a847cdf0ae4ce61841128c140bc58b3"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;double&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">start</a></td></tr>
<tr class="separator:a6a847cdf0ae4ce61841128c140bc58b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44aee521cd51708d0d5bbc9a17ecd6c4"><td class="memItemLeft" >&#160;&#160;&#160;&#160;&#160;&#160;double&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">delta</a></td></tr>
<tr class="separator:a44aee521cd51708d0d5bbc9a17ecd6c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98baf1e7ba5bcddcdf5117316192934c"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<a class="el" href="structcutlass_1_1Distribution.html#ab86d975567ef141ff82067b1f41cd3ee">sequential</a></td></tr>
<tr class="memdesc:a98baf1e7ba5bcddcdf5117316192934c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Elements are linear combination of row and column index.  <a href="#a98baf1e7ba5bcddcdf5117316192934c">More...</a><br /></td></tr>
<tr class="separator:a98baf1e7ba5bcddcdf5117316192934c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab073733c9661885698700933820aef8e"><td class="memItemLeft" valign="top">};&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:ab073733c9661885698700933820aef8e"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="structcutlass_1_1Distribution.html" title="Distribution type. ">Distribution</a> state.  <a href="#ab073733c9661885698700933820aef8e">More...</a><br /></td></tr>
<tr class="separator:ab073733c9661885698700933820aef8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07cb089b346ef06e198f6043128264fb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">Kind</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#a07cb089b346ef06e198f6043128264fb">kind</a></td></tr>
<tr class="memdesc:a07cb089b346ef06e198f6043128264fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Active variant kind.  <a href="#a07cb089b346ef06e198f6043128264fb">More...</a><br /></td></tr>
<tr class="separator:a07cb089b346ef06e198f6043128264fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a676b1d8b87691b4218f6ed308e6adfc1"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Distribution.html#a676b1d8b87691b4218f6ed308e6adfc1">int_scale</a></td></tr>
<tr class="memdesc:a676b1d8b87691b4218f6ed308e6adfc1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Random values are cast to integer after scaling by this power of two.  <a href="#a676b1d8b87691b4218f6ed308e6adfc1">More...</a><br /></td></tr>
<tr class="separator:a676b1d8b87691b4218f6ed308e6adfc1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Enumeration Documentation</h2>
<a class="anchor" id="a499f4023e0d42356ce71d38cc32bf92a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">cutlass::Distribution::Kind</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a class="anchor" id="a499f4023e0d42356ce71d38cc32bf92aa2ff14122c59a823654b84764f68e597b"></a>Invalid&#160;</td><td class="fielddoc">
</td></tr>
<tr><td class="fieldname"><a class="anchor" id="a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6"></a>Uniform&#160;</td><td class="fielddoc">
</td></tr>
<tr><td class="fieldname"><a class="anchor" id="a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5"></a>Gaussian&#160;</td><td class="fielddoc">
</td></tr>
<tr><td class="fieldname"><a class="anchor" id="a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6"></a>Identity&#160;</td><td class="fielddoc">
</td></tr>
<tr><td class="fieldname"><a class="anchor" id="a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc"></a>Sequential&#160;</td><td class="fielddoc">
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a40f0b9d0f92199f8a49c931d34dd8c8a"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">cutlass::Distribution::Distribution </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ad594b5ec1d577e8ef03d4d808a8220b1"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a>&amp; cutlass::Distribution::set_gaussian </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>_mean</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>_stddev</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>_int_scale</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aad2cf02af3d520544d89843cc4295858"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a>&amp; cutlass::Distribution::set_identity </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7fb9689c8ae17d5c72c7d0376fa93767"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a>&amp; cutlass::Distribution::set_sequential </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>start</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>delta</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>_int_scale</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5ef87d3af6af0a815a56e74645f32991"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a>&amp; cutlass::Distribution::set_uniform </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>_min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>_max</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>_int_scale</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="ab073733c9661885698700933820aef8e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">union { ... } </td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a77613df810c3f8f68b595599802cedb4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">double cutlass::Distribution::delta</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ada9c50671b405fabbb0841a093f809de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   cutlass::Distribution::gaussian</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a676b1d8b87691b4218f6ed308e6adfc1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int cutlass::Distribution::int_scale</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a07cb089b346ef06e198f6043128264fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">Kind</a> cutlass::Distribution::kind</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a824641fd3addfa360999614970adfac0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">double cutlass::Distribution::max</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a776df53c7ad1b7de983c9f9d17d7438c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">double cutlass::Distribution::mean</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a846430e3a21ed25c779fc6e714bc1bcc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">double cutlass::Distribution::min</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab86d975567ef141ff82067b1f41cd3ee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   cutlass::Distribution::sequential</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a69408e1ae607e1bf16a9e7fea1d04617"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">double cutlass::Distribution::start</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aee3bd32372426422bb02b335704965aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">double cutlass::Distribution::stddev</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afc30b6976acb39e54f061af1bf2870db"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct { ... }   cutlass::Distribution::uniform</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="distribution_8h_source.html">distribution.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
