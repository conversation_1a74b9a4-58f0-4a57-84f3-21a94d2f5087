<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::PitchLinearStripminedThreadMap&lt; Shape_, Threads, ElementsPerAccess &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">PitchLinearStripminedThreadMap</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::PitchLinearStripminedThreadMap&lt; Shape_, Threads, ElementsPerAccess &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap_1_1Detail.html">Detail</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal implementation details.  <a href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap_1_1Detail.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:adaa891e374d5b4a3f418a02bd0ddd22d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#adaa891e374d5b4a3f418a02bd0ddd22d">TensorCoord</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td></tr>
<tr class="memdesc:adaa891e374d5b4a3f418a02bd0ddd22d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor coordinate.  <a href="#adaa891e374d5b4a3f418a02bd0ddd22d">More...</a><br /></td></tr>
<tr class="separator:adaa891e374d5b4a3f418a02bd0ddd22d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab85c489634b7d04093bebeeff86d375a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ab85c489634b7d04093bebeeff86d375a">Shape</a> = Shape_</td></tr>
<tr class="memdesc:ab85c489634b7d04093bebeeff86d375a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile shape.  <a href="#ab85c489634b7d04093bebeeff86d375a">More...</a><br /></td></tr>
<tr class="separator:ab85c489634b7d04093bebeeff86d375a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a148282537189adeb25e88a35cc486802"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a148282537189adeb25e88a35cc486802">ThreadAccessShape</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a79dd4ad5e5e96c8c992d683825f32c48">kElementsPerAccess</a>, 1 &gt;</td></tr>
<tr class="memdesc:a148282537189adeb25e88a35cc486802"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shape of access by each thread.  <a href="#a148282537189adeb25e88a35cc486802">More...</a><br /></td></tr>
<tr class="separator:a148282537189adeb25e88a35cc486802"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a784640fa418b1d9b55f912f65925e705"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a784640fa418b1d9b55f912f65925e705">Iterations</a> = typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;=<a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 1,(Threads &gt;=<a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>?<a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::ShapeVec::kStrided</a>/(<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a>/<a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>):0) &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>/<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::ShapeVec::kStrided</a> &gt; &gt;::type</td></tr>
<tr class="memdesc:a784640fa418b1d9b55f912f65925e705"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of iterations by each thread.  <a href="#a784640fa418b1d9b55f912f65925e705">More...</a><br /></td></tr>
<tr class="separator:a784640fa418b1d9b55f912f65925e705"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed1e512ae8dde49bdbba25a966026e27"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#aed1e512ae8dde49bdbba25a966026e27">Delta</a> = typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;=<a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 1, <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a>/<a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a> &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a> *<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a79dd4ad5e5e96c8c992d683825f32c48">kElementsPerAccess</a>, 1 &gt; &gt;::type</td></tr>
<tr class="separator:aed1e512ae8dde49bdbba25a966026e27"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:aeb09f5131cac18bfd820d2ab4cb06c49"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#adaa891e374d5b4a3f418a02bd0ddd22d">TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#aeb09f5131cac18bfd820d2ab4cb06c49">initial_offset</a> (int thread_id)</td></tr>
<tr class="separator:aeb09f5131cac18bfd820d2ab4cb06c49"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:ad4089315fc07133a415189b088a6d04c"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a> = Threads</td></tr>
<tr class="memdesc:ad4089315fc07133a415189b088a6d04c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads total.  <a href="#ad4089315fc07133a415189b088a6d04c">More...</a><br /></td></tr>
<tr class="separator:ad4089315fc07133a415189b088a6d04c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79dd4ad5e5e96c8c992d683825f32c48"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a79dd4ad5e5e96c8c992d683825f32c48">kElementsPerAccess</a> = ElementsPerAccess</td></tr>
<tr class="memdesc:a79dd4ad5e5e96c8c992d683825f32c48"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract vector length from Layout.  <a href="#a79dd4ad5e5e96c8c992d683825f32c48">More...</a><br /></td></tr>
<tr class="separator:a79dd4ad5e5e96c8c992d683825f32c48"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename Shape_, int Threads, int ElementsPerAccess = 1&gt;<br />
struct cutlass::transform::PitchLinearStripminedThreadMap&lt; Shape_, Threads, ElementsPerAccess &gt;</h3>

<p>Strip-mines a pitch-linear tile among a given number of threads, first along the contiguous dimension then along the strided dimension.</p>
<p>The tile must be divisible by the thread count such that all threads may execute the same number of iterations with the same delta to exhaustively cover the tile.</p>
<p>This class satisfies the "RegularThreadMapping" concept. </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="aed1e512ae8dde49bdbba25a966026e27"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#aed1e512ae8dde49bdbba25a966026e27">Delta</a> =  typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;= <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 1, <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a> / <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a> &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a> * <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a79dd4ad5e5e96c8c992d683825f32c48">kElementsPerAccess</a>, 1 &gt; &gt;::type</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Interval between accesses along each dimension of the tensor's logical coordinate space (in units of Elements) </p>

</div>
</div>
<a class="anchor" id="a784640fa418b1d9b55f912f65925e705"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a784640fa418b1d9b55f912f65925e705">Iterations</a> =  typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;= <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 1, (Threads &gt;= <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a> ? <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::ShapeVec::kStrided</a> / (<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a> / <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a>) : 0) &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::ShapeVec::kContiguous</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ad4089315fc07133a415189b088a6d04c">kThreads</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::ShapeVec::kStrided</a> &gt; &gt;::type</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab85c489634b7d04093bebeeff86d375a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#ab85c489634b7d04093bebeeff86d375a">Shape</a> =  Shape_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adaa891e374d5b4a3f418a02bd0ddd22d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#adaa891e374d5b4a3f418a02bd0ddd22d">TensorCoord</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a148282537189adeb25e88a35cc486802"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a148282537189adeb25e88a35cc486802">ThreadAccessShape</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;<a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#a79dd4ad5e5e96c8c992d683825f32c48">kElementsPerAccess</a>, 1&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="aeb09f5131cac18bfd820d2ab4cb06c49"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html#adaa891e374d5b4a3f418a02bd0ddd22d">TensorCoord</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::initial_offset </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>thread_id</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Maps thread ID to a coordinate offset within the tensor's logical coordinate space (in units of Elements) </p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a79dd4ad5e5e96c8c992d683825f32c48"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::kElementsPerAccess = ElementsPerAccess</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad4089315fc07133a415189b088a6d04c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearStripminedThreadMap.html">cutlass::transform::PitchLinearStripminedThreadMap</a>&lt; Shape_, Threads, ElementsPerAccess &gt;::kThreads = Threads</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
