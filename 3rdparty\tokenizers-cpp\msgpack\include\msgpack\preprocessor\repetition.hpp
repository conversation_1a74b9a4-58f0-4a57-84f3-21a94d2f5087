# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_REPETITION_HPP
# define MSGPACK_PREPROCESSOR_REPETITION_HPP
#
# include <msgpack/preprocessor/repetition/deduce_r.hpp>
# include <msgpack/preprocessor/repetition/deduce_z.hpp>
# include <msgpack/preprocessor/repetition/enum.hpp>
# include <msgpack/preprocessor/repetition/enum_binary_params.hpp>
# include <msgpack/preprocessor/repetition/enum_params.hpp>
# include <msgpack/preprocessor/repetition/enum_params_with_a_default.hpp>
# include <msgpack/preprocessor/repetition/enum_params_with_defaults.hpp>
# include <msgpack/preprocessor/repetition/enum_shifted.hpp>
# include <msgpack/preprocessor/repetition/enum_shifted_binary_params.hpp>
# include <msgpack/preprocessor/repetition/enum_shifted_params.hpp>
# include <msgpack/preprocessor/repetition/enum_trailing.hpp>
# include <msgpack/preprocessor/repetition/enum_trailing_binary_params.hpp>
# include <msgpack/preprocessor/repetition/enum_trailing_params.hpp>
# include <msgpack/preprocessor/repetition/for.hpp>
# include <msgpack/preprocessor/repetition/repeat.hpp>
# include <msgpack/preprocessor/repetition/repeat_from_to.hpp>
#
# endif
