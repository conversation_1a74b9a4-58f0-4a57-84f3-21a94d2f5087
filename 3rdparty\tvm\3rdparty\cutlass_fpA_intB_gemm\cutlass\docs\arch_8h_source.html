<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: arch.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">arch.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="arch_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1arch.html">   34</a></span>&#160;<span class="keyword">namespace </span>arch {</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm50.html">   37</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Sm50.html">Sm50</a> {</div><div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">   38</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">kMinComputeCapability</a> = 50;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;}; </div><div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm60.html">   40</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Sm60.html">Sm60</a> {</div><div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm60.html#a35d6ac621f22c1936ccd65c961c2398e">   41</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">kMinComputeCapability</a> = 60;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;}; </div><div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm61.html">   43</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Sm61.html">Sm61</a> {</div><div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm61.html#aa81fbe4d676a8d23be330af8cb2cbecf">   44</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">kMinComputeCapability</a> = 61;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;};</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm70.html">   46</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Sm70.html">Sm70</a> {</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm70.html#a6db30019eb2008480b217f035661b1bc">   47</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">kMinComputeCapability</a> = 70;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;};</div><div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm72.html">   49</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Sm72.html">Sm72</a> {</div><div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm72.html#a109bf335d29769ed08570cff7e709242">   50</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">kMinComputeCapability</a> = 72;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;};</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm75.html">   52</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Sm75.html">Sm75</a> {</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Sm75.html#a1e2a32a34aeb5b8dced1106b67456daa">   53</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">kMinComputeCapability</a> = 75;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;};</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;} <span class="comment">// namespace arch</span></div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Sm50_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Sm50.html">cutlass::arch::Sm50</a></div><div class="ttdef"><b>Definition:</b> arch.h:37</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Sm70_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Sm70.html">cutlass::arch::Sm70</a></div><div class="ttdef"><b>Definition:</b> arch.h:46</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Sm61_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Sm61.html">cutlass::arch::Sm61</a></div><div class="ttdef"><b>Definition:</b> arch.h:43</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Sm75_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Sm75.html">cutlass::arch::Sm75</a></div><div class="ttdef"><b>Definition:</b> arch.h:52</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Sm60_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Sm60.html">cutlass::arch::Sm60</a></div><div class="ttdef"><b>Definition:</b> arch.h:40</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Sm50_html_ae14b658c3b0caa882d22ab3dac49f1f4"><div class="ttname"><a href="structcutlass_1_1arch_1_1Sm50.html#ae14b658c3b0caa882d22ab3dac49f1f4">cutlass::arch::Sm50::kMinComputeCapability</a></div><div class="ttdeci">static int const kMinComputeCapability</div><div class="ttdef"><b>Definition:</b> arch.h:38</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Sm72_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Sm72.html">cutlass::arch::Sm72</a></div><div class="ttdef"><b>Definition:</b> arch.h:49</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
