# MLC-LLM Android Library

A high-performance Android library for running Large Language Models on mobile devices using MLC-LLM.

## Features

- ✅ **Multi-Architecture Support**: ARM64, ARM32, x86, x86_64
- ✅ **Efficient Memory Management**: Optimized for mobile devices
- ✅ **Easy Integration**: Simple Java/Kotlin API
- ✅ **Model Loading**: Support for various model formats
- ✅ **Text Generation**: High-quality text generation
- ✅ **Tokenization**: Built-in tokenizer support
- ✅ **Error Handling**: Comprehensive error handling and logging

## Requirements

- Android API Level 24+ (Android 7.0)
- Device with at least 4GB RAM (recommended)
- ARM64 or x86_64 architecture (recommended)

## Installation

### Method 1: Add as Module

1. Copy the `mlc4j` directory to your Android project
2. Add to your `settings.gradle`:
```gradle
include ':mlc4j'
```

3. Add dependency in your app's `build.gradle`:
```gradle
dependencies {
    implementation project(':mlc4j')
}
```

### Method 2: AAR Library

1. Build the AAR file:
```bash
./gradlew assembleRelease
```

2. Add the generated AAR to your project

## Quick Start

### Basic Usage

```java
import ai.mlc.mlcllm.MLCEngine;

public class MainActivity extends AppCompatActivity {
    private MLCEngine mlcEngine;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize the engine
        mlcEngine = new MLCEngine();
        
        // Load a model
        String modelPath = "/path/to/your/model";
        if (mlcEngine.loadModel(modelPath)) {
            // Generate text
            String response = mlcEngine.generate("Hello, how are you?", 100);
            Log.d("MLC", "Generated: " + response);
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mlcEngine != null) {
            mlcEngine.release();
        }
    }
}
```

### Advanced Usage

```java
import ai.mlc.mlcllm.MLCEngine;
import ai.mlc.mlcllm.MLCUtils;

// Check device compatibility
if (MLCUtils.isArchitectureSupported("arm64-v8a")) {
    Log.d("MLC", "Device supports ARM64");
}

// Get system information
String systemInfo = MLCUtils.getSystemInfo();
Log.d("MLC", systemInfo);

// Initialize engine with error handling
MLCEngine engine = new MLCEngine();
if (engine.isReady()) {
    // Load model with progress tracking
    String modelPath = MLCUtils.getModelPath(context, "my_model");
    
    if (MLCUtils.isFileValid(modelPath)) {
        boolean loaded = engine.loadModel(modelPath);
        if (loaded) {
            // Generate with custom parameters
            String result = engine.generate("Tell me a story", 200, 0.8f);
            
            // Get model information
            String modelInfo = engine.getModelInfo();
            int vocabSize = engine.getVocabSize();
            
            Log.d("MLC", "Model info: " + modelInfo);
            Log.d("MLC", "Vocab size: " + vocabSize);
        }
    }
}
```

## API Reference

### MLCEngine

Main class for interacting with MLC-LLM models.

#### Methods

- `MLCEngine()` - Constructor
- `boolean loadModel(String modelPath)` - Load a model from file
- `String generate(String prompt, int maxTokens)` - Generate text
- `String generate(String prompt, int maxTokens, float temperature)` - Generate with temperature
- `String[] tokenize(String text)` - Tokenize text
- `String detokenize(int[] tokens)` - Convert tokens back to text
- `int getVocabSize()` - Get vocabulary size
- `String getModelInfo()` - Get model information
- `boolean isModelLoaded()` - Check if model is loaded
- `boolean isReady()` - Check if engine is ready
- `String getVersion()` - Get library version
- `void release()` - Release resources

### MLCUtils

Utility class with helper functions.

#### Methods

- `String getModelPath(Context context, String modelName)` - Get model path
- `String getCachePath(Context context)` - Get cache directory
- `boolean copyAssetToFile(Context context, String assetName, String targetPath)` - Copy asset
- `boolean extractZip(String zipPath, String extractPath)` - Extract ZIP file
- `String getDeviceArchitecture()` - Get device architecture
- `String[] getSupportedArchitectures()` - Get all supported architectures
- `boolean isArchitectureSupported(String arch)` - Check architecture support
- `long getAvailableMemory(Context context)` - Get available memory
- `boolean isFileValid(String filePath)` - Check if file is valid
- `String formatBytes(long bytes)` - Format bytes to human readable
- `String getSystemInfo()` - Get system information

## Model Management

### Supported Formats

- MLC-LLM compiled models
- Quantized models (INT4, INT8)
- Custom model formats

### Model Loading

```java
// From internal storage
String modelPath = context.getFilesDir() + "/models/my_model";
engine.loadModel(modelPath);

// From assets (copy first)
String assetModel = "models/my_model.mlc";
String targetPath = MLCUtils.getModelPath(context, "my_model");
if (MLCUtils.copyAssetToFile(context, assetModel, targetPath)) {
    engine.loadModel(targetPath);
}

// From external storage (with permissions)
String externalModel = Environment.getExternalStorageDirectory() + "/models/my_model";
if (MLCUtils.isFileValid(externalModel)) {
    engine.loadModel(externalModel);
}
```

## Performance Tips

1. **Use ARM64 devices** for best performance
2. **Ensure sufficient RAM** (4GB+ recommended)
3. **Use quantized models** to reduce memory usage
4. **Load models once** and reuse the engine
5. **Release resources** when done

## Troubleshooting

### Common Issues

1. **UnsatisfiedLinkError**: Native libraries not found
   - Check that all .so files are included
   - Verify architecture compatibility

2. **OutOfMemoryError**: Insufficient memory
   - Use smaller or quantized models
   - Increase app heap size

3. **Model loading fails**: Invalid model file
   - Check file path and permissions
   - Verify model format compatibility

### Debug Information

```java
// Get detailed system information
String debugInfo = MLCUtils.getSystemInfo();
Log.d("MLC_DEBUG", debugInfo);

// Check library loading
try {
    MLCEngine engine = new MLCEngine();
    Log.d("MLC_DEBUG", "Engine ready: " + engine.isReady());
} catch (Exception e) {
    Log.e("MLC_DEBUG", "Engine initialization failed", e);
}
```

## License

This library is part of the MLC-LLM project. See the main project for license information.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the MLC-LLM documentation
3. Open an issue on the main MLC-LLM repository
