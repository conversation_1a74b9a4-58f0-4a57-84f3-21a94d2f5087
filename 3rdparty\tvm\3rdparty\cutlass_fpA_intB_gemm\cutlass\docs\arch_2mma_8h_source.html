<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: mma.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">arch/mma.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="arch_2mma_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span>arch {</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">struct </span>OpMultiplyAdd;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">struct </span>OpMultiplyAddSaturate;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">struct </span>OpXorPopc;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="keyword">struct </span>OpClassSimt;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">struct </span>OpClassTensorOp;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="keyword">struct </span>OpClassWmmaTensorOp;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="keyword">typename</span> Shape_,</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="keywordtype">int</span> kThreads_,</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="keyword">typename</span> ElementA,</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <span class="keyword">typename</span> ElementC,</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;  <span class="keyword">typename</span> Operator</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;&gt;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma.html">   92</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  <span class="keyword">typename</span> ElementA,</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="keyword">typename</span> LayoutA,</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <span class="keyword">typename</span> ElementB,</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  <span class="keyword">typename</span> LayoutB,</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <span class="keyword">typename</span> ElementC,</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="keyword">typename</span> LayoutC,</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  <span class="keyword">typename</span> Operator</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;&gt;</div><div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01ElementAb6e65b2cf5ede7f41cb070a767158dee.html">  113</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1arch_1_1Mma.html">Mma</a>&lt;gemm::GemmShape&lt;1, 1, 1&gt;, 1, ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, Operator&gt; {</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div><div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01ElementAb6e65b2cf5ede7f41cb070a767158dee.html#a90803d5c187b85cfc55bf1d6fae6756e">  115</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">Shape</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;1, 1, 1&gt;</a>;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01ElementAb6e65b2cf5ede7f41cb070a767158dee.html#af8276da36c235c482881864f4ee93c58">  118</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01ElementAb6e65b2cf5ede7f41cb070a767158dee.html#af8276da36c235c482881864f4ee93c58">operator()</a>(</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    Array&lt;ElementC, 1&gt; &amp;d,</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    Array&lt;ElementA, 1&gt; <span class="keyword">const</span> &amp;a,</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    Array&lt;ElementB, 1&gt; <span class="keyword">const</span> &amp;b,</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    Array&lt;ElementC, 1&gt; <span class="keyword">const</span> &amp;c</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  ) {</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    d[0] = a[0] * b[0] + c[0];</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  }</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;};</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;} <span class="comment">// namespace arch</span></div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="comment">// Specializations for each compute capability</span></div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="arch_2mma__sm50_8h.html">cutlass/arch/mma_sm50.h</a>&quot;</span></div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="arch_2mma__sm60_8h.html">cutlass/arch/mma_sm60.h</a>&quot;</span></div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="arch_2mma__sm61_8h.html">cutlass/arch/mma_sm61.h</a>&quot;</span></div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="mma__sm70_8h.html">cutlass/arch/mma_sm70.h</a>&quot;</span> </div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="mma__sm75_8h.html">cutlass/arch/mma_sm75.h</a>&quot;</span> </div><div class="ttc" id="mma__sm70_8h_html"><div class="ttname"><a href="mma__sm70_8h.html">mma_sm70.h</a></div><div class="ttdoc">Matrix multiply. </div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="arch_2mma__sm60_8h_html"><div class="ttname"><a href="arch_2mma__sm60_8h.html">mma_sm60.h</a></div><div class="ttdoc">Matrix multiply. </div></div>
<div class="ttc" id="arch_2mma__sm61_8h_html"><div class="ttname"><a href="arch_2mma__sm61_8h.html">mma_sm61.h</a></div><div class="ttdoc">Matrix multiply. </div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_html"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a></div><div class="ttdoc">Matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> arch/mma.h:92</div></div>
<div class="ttc" id="mma__sm75_8h_html"><div class="ttname"><a href="mma__sm75_8h.html">mma_sm75.h</a></div><div class="ttdoc">Matrix multiply for SM75. </div></div>
<div class="ttc" id="arch_2mma__sm50_8h_html"><div class="ttname"><a href="arch_2mma__sm50_8h.html">mma_sm50.h</a></div><div class="ttdoc">Matrix multiply. </div></div>
<div class="ttc" id="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01ElementAb6e65b2cf5ede7f41cb070a767158dee_html_af8276da36c235c482881864f4ee93c58"><div class="ttname"><a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_011_00_011_00_011_01_4_00_011_00_01ElementAb6e65b2cf5ede7f41cb070a767158dee.html#af8276da36c235c482881864f4ee93c58">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 1, 1, 1 &gt;, 1, ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, Operator &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void operator()(Array&lt; ElementC, 1 &gt; &amp;d, Array&lt; ElementA, 1 &gt; const &amp;a, Array&lt; ElementB, 1 &gt; const &amp;b, Array&lt; ElementC, 1 &gt; const &amp;c)</div><div class="ttdef"><b>Definition:</b> arch/mma.h:118</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
