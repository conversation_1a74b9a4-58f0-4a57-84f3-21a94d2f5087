name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  Build:
    strategy:
      matrix:
        os: [ubuntu-latest, macOS-latest]

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v5
      with:
        python-version: '3.10'

    - name: Setup Python
      run: |
        pip install -U pip wheel
        pip install cpplint
    - name: Setup@Ubuntu
      if: startsWith(matrix.os, 'ubuntu')
      run: |
        sudo apt-get install -y doxygen wget graphviz unzip
    - name: Lint
      if: startsWith(matrix.os, 'ubuntu')
      run: |
        pip install -r doc_requirements.txt
        ./tests/scripts/task_lint.sh
    - name: Test
      run: |
        ./tests/scripts/task_build.sh
        ./bin/mock
