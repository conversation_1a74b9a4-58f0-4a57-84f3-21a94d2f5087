//
// MessagePack for C++ deserializing routine
//
// Copyright (C) 2018 <PERSON>OND<PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_NULL_VISITOR_HPP
#define MSGPACK_NULL_VISITOR_HPP

#include "msgpack/null_visitor_decl.hpp"

#include "msgpack/v2/null_visitor.hpp"

#endif // MSGPACK_NULL_VISITOR_HPP
