# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# if defined(MSGPACK_PP_ITERATION_LIMITS)
#    if !defined(MSGPACK_PP_FILENAME_1)
#        error MSGPACK_PP_ERROR:  depth #1 filename is not defined
#    endif
#    define MSGPACK_PP_VALUE MSGPACK_PP_TUPLE_ELEM(2, 0, MSGPACK_PP_ITERATION_LIMITS)
#    include <msgpack/preprocessor/iteration/detail/bounds/lower1.hpp>
#    define MSGPACK_PP_VALUE MSGPACK_PP_TUPLE_ELEM(2, 1, MSGPACK_PP_ITERATION_LIMITS)
#    include <msgpack/preprocessor/iteration/detail/bounds/upper1.hpp>
#    define MSGPACK_PP_ITERATION_FLAGS_1() 0
#    undef MSGPACK_PP_ITERATION_LIMITS
# elif defined(MSGPACK_PP_ITERATION_PARAMS_1)
#    define MSGPACK_PP_VALUE MSGPACK_PP_ARRAY_ELEM(0, MSGPACK_PP_ITERATION_PARAMS_1)
#    include <msgpack/preprocessor/iteration/detail/bounds/lower1.hpp>
#    define MSGPACK_PP_VALUE MSGPACK_PP_ARRAY_ELEM(1, MSGPACK_PP_ITERATION_PARAMS_1)
#    include <msgpack/preprocessor/iteration/detail/bounds/upper1.hpp>
#    define MSGPACK_PP_FILENAME_1 MSGPACK_PP_ARRAY_ELEM(2, MSGPACK_PP_ITERATION_PARAMS_1)
#    if MSGPACK_PP_ARRAY_SIZE(MSGPACK_PP_ITERATION_PARAMS_1) >= 4
#        define MSGPACK_PP_ITERATION_FLAGS_1() MSGPACK_PP_ARRAY_ELEM(3, MSGPACK_PP_ITERATION_PARAMS_1)
#    else
#        define MSGPACK_PP_ITERATION_FLAGS_1() 0
#    endif
# else
#    error MSGPACK_PP_ERROR:  depth #1 iteration boundaries or filename not defined
# endif
#
# undef MSGPACK_PP_ITERATION_DEPTH
# define MSGPACK_PP_ITERATION_DEPTH() 1
#
# define MSGPACK_PP_IS_ITERATING 1
#
# if (MSGPACK_PP_ITERATION_START_1) > (MSGPACK_PP_ITERATION_FINISH_1)
#    include <msgpack/preprocessor/iteration/detail/iter/reverse1.hpp>
# else
#    if MSGPACK_PP_ITERATION_START_1 <= 0 && MSGPACK_PP_ITERATION_FINISH_1 >= 0
#        define MSGPACK_PP_ITERATION_1 0
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 1 && MSGPACK_PP_ITERATION_FINISH_1 >= 1
#        define MSGPACK_PP_ITERATION_1 1
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 2 && MSGPACK_PP_ITERATION_FINISH_1 >= 2
#        define MSGPACK_PP_ITERATION_1 2
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 3 && MSGPACK_PP_ITERATION_FINISH_1 >= 3
#        define MSGPACK_PP_ITERATION_1 3
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 4 && MSGPACK_PP_ITERATION_FINISH_1 >= 4
#        define MSGPACK_PP_ITERATION_1 4
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 5 && MSGPACK_PP_ITERATION_FINISH_1 >= 5
#        define MSGPACK_PP_ITERATION_1 5
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 6 && MSGPACK_PP_ITERATION_FINISH_1 >= 6
#        define MSGPACK_PP_ITERATION_1 6
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 7 && MSGPACK_PP_ITERATION_FINISH_1 >= 7
#        define MSGPACK_PP_ITERATION_1 7
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 8 && MSGPACK_PP_ITERATION_FINISH_1 >= 8
#        define MSGPACK_PP_ITERATION_1 8
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 9 && MSGPACK_PP_ITERATION_FINISH_1 >= 9
#        define MSGPACK_PP_ITERATION_1 9
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 10 && MSGPACK_PP_ITERATION_FINISH_1 >= 10
#        define MSGPACK_PP_ITERATION_1 10
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 11 && MSGPACK_PP_ITERATION_FINISH_1 >= 11
#        define MSGPACK_PP_ITERATION_1 11
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 12 && MSGPACK_PP_ITERATION_FINISH_1 >= 12
#        define MSGPACK_PP_ITERATION_1 12
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 13 && MSGPACK_PP_ITERATION_FINISH_1 >= 13
#        define MSGPACK_PP_ITERATION_1 13
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 14 && MSGPACK_PP_ITERATION_FINISH_1 >= 14
#        define MSGPACK_PP_ITERATION_1 14
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 15 && MSGPACK_PP_ITERATION_FINISH_1 >= 15
#        define MSGPACK_PP_ITERATION_1 15
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 16 && MSGPACK_PP_ITERATION_FINISH_1 >= 16
#        define MSGPACK_PP_ITERATION_1 16
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 17 && MSGPACK_PP_ITERATION_FINISH_1 >= 17
#        define MSGPACK_PP_ITERATION_1 17
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 18 && MSGPACK_PP_ITERATION_FINISH_1 >= 18
#        define MSGPACK_PP_ITERATION_1 18
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 19 && MSGPACK_PP_ITERATION_FINISH_1 >= 19
#        define MSGPACK_PP_ITERATION_1 19
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 20 && MSGPACK_PP_ITERATION_FINISH_1 >= 20
#        define MSGPACK_PP_ITERATION_1 20
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 21 && MSGPACK_PP_ITERATION_FINISH_1 >= 21
#        define MSGPACK_PP_ITERATION_1 21
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 22 && MSGPACK_PP_ITERATION_FINISH_1 >= 22
#        define MSGPACK_PP_ITERATION_1 22
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 23 && MSGPACK_PP_ITERATION_FINISH_1 >= 23
#        define MSGPACK_PP_ITERATION_1 23
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 24 && MSGPACK_PP_ITERATION_FINISH_1 >= 24
#        define MSGPACK_PP_ITERATION_1 24
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 25 && MSGPACK_PP_ITERATION_FINISH_1 >= 25
#        define MSGPACK_PP_ITERATION_1 25
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 26 && MSGPACK_PP_ITERATION_FINISH_1 >= 26
#        define MSGPACK_PP_ITERATION_1 26
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 27 && MSGPACK_PP_ITERATION_FINISH_1 >= 27
#        define MSGPACK_PP_ITERATION_1 27
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 28 && MSGPACK_PP_ITERATION_FINISH_1 >= 28
#        define MSGPACK_PP_ITERATION_1 28
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 29 && MSGPACK_PP_ITERATION_FINISH_1 >= 29
#        define MSGPACK_PP_ITERATION_1 29
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 30 && MSGPACK_PP_ITERATION_FINISH_1 >= 30
#        define MSGPACK_PP_ITERATION_1 30
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 31 && MSGPACK_PP_ITERATION_FINISH_1 >= 31
#        define MSGPACK_PP_ITERATION_1 31
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 32 && MSGPACK_PP_ITERATION_FINISH_1 >= 32
#        define MSGPACK_PP_ITERATION_1 32
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 33 && MSGPACK_PP_ITERATION_FINISH_1 >= 33
#        define MSGPACK_PP_ITERATION_1 33
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 34 && MSGPACK_PP_ITERATION_FINISH_1 >= 34
#        define MSGPACK_PP_ITERATION_1 34
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 35 && MSGPACK_PP_ITERATION_FINISH_1 >= 35
#        define MSGPACK_PP_ITERATION_1 35
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 36 && MSGPACK_PP_ITERATION_FINISH_1 >= 36
#        define MSGPACK_PP_ITERATION_1 36
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 37 && MSGPACK_PP_ITERATION_FINISH_1 >= 37
#        define MSGPACK_PP_ITERATION_1 37
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 38 && MSGPACK_PP_ITERATION_FINISH_1 >= 38
#        define MSGPACK_PP_ITERATION_1 38
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 39 && MSGPACK_PP_ITERATION_FINISH_1 >= 39
#        define MSGPACK_PP_ITERATION_1 39
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 40 && MSGPACK_PP_ITERATION_FINISH_1 >= 40
#        define MSGPACK_PP_ITERATION_1 40
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 41 && MSGPACK_PP_ITERATION_FINISH_1 >= 41
#        define MSGPACK_PP_ITERATION_1 41
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 42 && MSGPACK_PP_ITERATION_FINISH_1 >= 42
#        define MSGPACK_PP_ITERATION_1 42
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 43 && MSGPACK_PP_ITERATION_FINISH_1 >= 43
#        define MSGPACK_PP_ITERATION_1 43
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 44 && MSGPACK_PP_ITERATION_FINISH_1 >= 44
#        define MSGPACK_PP_ITERATION_1 44
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 45 && MSGPACK_PP_ITERATION_FINISH_1 >= 45
#        define MSGPACK_PP_ITERATION_1 45
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 46 && MSGPACK_PP_ITERATION_FINISH_1 >= 46
#        define MSGPACK_PP_ITERATION_1 46
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 47 && MSGPACK_PP_ITERATION_FINISH_1 >= 47
#        define MSGPACK_PP_ITERATION_1 47
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 48 && MSGPACK_PP_ITERATION_FINISH_1 >= 48
#        define MSGPACK_PP_ITERATION_1 48
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 49 && MSGPACK_PP_ITERATION_FINISH_1 >= 49
#        define MSGPACK_PP_ITERATION_1 49
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 50 && MSGPACK_PP_ITERATION_FINISH_1 >= 50
#        define MSGPACK_PP_ITERATION_1 50
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 51 && MSGPACK_PP_ITERATION_FINISH_1 >= 51
#        define MSGPACK_PP_ITERATION_1 51
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 52 && MSGPACK_PP_ITERATION_FINISH_1 >= 52
#        define MSGPACK_PP_ITERATION_1 52
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 53 && MSGPACK_PP_ITERATION_FINISH_1 >= 53
#        define MSGPACK_PP_ITERATION_1 53
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 54 && MSGPACK_PP_ITERATION_FINISH_1 >= 54
#        define MSGPACK_PP_ITERATION_1 54
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 55 && MSGPACK_PP_ITERATION_FINISH_1 >= 55
#        define MSGPACK_PP_ITERATION_1 55
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 56 && MSGPACK_PP_ITERATION_FINISH_1 >= 56
#        define MSGPACK_PP_ITERATION_1 56
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 57 && MSGPACK_PP_ITERATION_FINISH_1 >= 57
#        define MSGPACK_PP_ITERATION_1 57
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 58 && MSGPACK_PP_ITERATION_FINISH_1 >= 58
#        define MSGPACK_PP_ITERATION_1 58
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 59 && MSGPACK_PP_ITERATION_FINISH_1 >= 59
#        define MSGPACK_PP_ITERATION_1 59
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 60 && MSGPACK_PP_ITERATION_FINISH_1 >= 60
#        define MSGPACK_PP_ITERATION_1 60
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 61 && MSGPACK_PP_ITERATION_FINISH_1 >= 61
#        define MSGPACK_PP_ITERATION_1 61
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 62 && MSGPACK_PP_ITERATION_FINISH_1 >= 62
#        define MSGPACK_PP_ITERATION_1 62
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 63 && MSGPACK_PP_ITERATION_FINISH_1 >= 63
#        define MSGPACK_PP_ITERATION_1 63
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 64 && MSGPACK_PP_ITERATION_FINISH_1 >= 64
#        define MSGPACK_PP_ITERATION_1 64
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 65 && MSGPACK_PP_ITERATION_FINISH_1 >= 65
#        define MSGPACK_PP_ITERATION_1 65
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 66 && MSGPACK_PP_ITERATION_FINISH_1 >= 66
#        define MSGPACK_PP_ITERATION_1 66
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 67 && MSGPACK_PP_ITERATION_FINISH_1 >= 67
#        define MSGPACK_PP_ITERATION_1 67
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 68 && MSGPACK_PP_ITERATION_FINISH_1 >= 68
#        define MSGPACK_PP_ITERATION_1 68
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 69 && MSGPACK_PP_ITERATION_FINISH_1 >= 69
#        define MSGPACK_PP_ITERATION_1 69
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 70 && MSGPACK_PP_ITERATION_FINISH_1 >= 70
#        define MSGPACK_PP_ITERATION_1 70
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 71 && MSGPACK_PP_ITERATION_FINISH_1 >= 71
#        define MSGPACK_PP_ITERATION_1 71
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 72 && MSGPACK_PP_ITERATION_FINISH_1 >= 72
#        define MSGPACK_PP_ITERATION_1 72
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 73 && MSGPACK_PP_ITERATION_FINISH_1 >= 73
#        define MSGPACK_PP_ITERATION_1 73
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 74 && MSGPACK_PP_ITERATION_FINISH_1 >= 74
#        define MSGPACK_PP_ITERATION_1 74
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 75 && MSGPACK_PP_ITERATION_FINISH_1 >= 75
#        define MSGPACK_PP_ITERATION_1 75
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 76 && MSGPACK_PP_ITERATION_FINISH_1 >= 76
#        define MSGPACK_PP_ITERATION_1 76
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 77 && MSGPACK_PP_ITERATION_FINISH_1 >= 77
#        define MSGPACK_PP_ITERATION_1 77
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 78 && MSGPACK_PP_ITERATION_FINISH_1 >= 78
#        define MSGPACK_PP_ITERATION_1 78
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 79 && MSGPACK_PP_ITERATION_FINISH_1 >= 79
#        define MSGPACK_PP_ITERATION_1 79
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 80 && MSGPACK_PP_ITERATION_FINISH_1 >= 80
#        define MSGPACK_PP_ITERATION_1 80
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 81 && MSGPACK_PP_ITERATION_FINISH_1 >= 81
#        define MSGPACK_PP_ITERATION_1 81
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 82 && MSGPACK_PP_ITERATION_FINISH_1 >= 82
#        define MSGPACK_PP_ITERATION_1 82
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 83 && MSGPACK_PP_ITERATION_FINISH_1 >= 83
#        define MSGPACK_PP_ITERATION_1 83
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 84 && MSGPACK_PP_ITERATION_FINISH_1 >= 84
#        define MSGPACK_PP_ITERATION_1 84
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 85 && MSGPACK_PP_ITERATION_FINISH_1 >= 85
#        define MSGPACK_PP_ITERATION_1 85
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 86 && MSGPACK_PP_ITERATION_FINISH_1 >= 86
#        define MSGPACK_PP_ITERATION_1 86
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 87 && MSGPACK_PP_ITERATION_FINISH_1 >= 87
#        define MSGPACK_PP_ITERATION_1 87
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 88 && MSGPACK_PP_ITERATION_FINISH_1 >= 88
#        define MSGPACK_PP_ITERATION_1 88
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 89 && MSGPACK_PP_ITERATION_FINISH_1 >= 89
#        define MSGPACK_PP_ITERATION_1 89
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 90 && MSGPACK_PP_ITERATION_FINISH_1 >= 90
#        define MSGPACK_PP_ITERATION_1 90
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 91 && MSGPACK_PP_ITERATION_FINISH_1 >= 91
#        define MSGPACK_PP_ITERATION_1 91
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 92 && MSGPACK_PP_ITERATION_FINISH_1 >= 92
#        define MSGPACK_PP_ITERATION_1 92
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 93 && MSGPACK_PP_ITERATION_FINISH_1 >= 93
#        define MSGPACK_PP_ITERATION_1 93
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 94 && MSGPACK_PP_ITERATION_FINISH_1 >= 94
#        define MSGPACK_PP_ITERATION_1 94
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 95 && MSGPACK_PP_ITERATION_FINISH_1 >= 95
#        define MSGPACK_PP_ITERATION_1 95
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 96 && MSGPACK_PP_ITERATION_FINISH_1 >= 96
#        define MSGPACK_PP_ITERATION_1 96
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 97 && MSGPACK_PP_ITERATION_FINISH_1 >= 97
#        define MSGPACK_PP_ITERATION_1 97
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 98 && MSGPACK_PP_ITERATION_FINISH_1 >= 98
#        define MSGPACK_PP_ITERATION_1 98
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 99 && MSGPACK_PP_ITERATION_FINISH_1 >= 99
#        define MSGPACK_PP_ITERATION_1 99
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 100 && MSGPACK_PP_ITERATION_FINISH_1 >= 100
#        define MSGPACK_PP_ITERATION_1 100
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 101 && MSGPACK_PP_ITERATION_FINISH_1 >= 101
#        define MSGPACK_PP_ITERATION_1 101
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 102 && MSGPACK_PP_ITERATION_FINISH_1 >= 102
#        define MSGPACK_PP_ITERATION_1 102
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 103 && MSGPACK_PP_ITERATION_FINISH_1 >= 103
#        define MSGPACK_PP_ITERATION_1 103
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 104 && MSGPACK_PP_ITERATION_FINISH_1 >= 104
#        define MSGPACK_PP_ITERATION_1 104
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 105 && MSGPACK_PP_ITERATION_FINISH_1 >= 105
#        define MSGPACK_PP_ITERATION_1 105
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 106 && MSGPACK_PP_ITERATION_FINISH_1 >= 106
#        define MSGPACK_PP_ITERATION_1 106
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 107 && MSGPACK_PP_ITERATION_FINISH_1 >= 107
#        define MSGPACK_PP_ITERATION_1 107
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 108 && MSGPACK_PP_ITERATION_FINISH_1 >= 108
#        define MSGPACK_PP_ITERATION_1 108
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 109 && MSGPACK_PP_ITERATION_FINISH_1 >= 109
#        define MSGPACK_PP_ITERATION_1 109
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 110 && MSGPACK_PP_ITERATION_FINISH_1 >= 110
#        define MSGPACK_PP_ITERATION_1 110
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 111 && MSGPACK_PP_ITERATION_FINISH_1 >= 111
#        define MSGPACK_PP_ITERATION_1 111
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 112 && MSGPACK_PP_ITERATION_FINISH_1 >= 112
#        define MSGPACK_PP_ITERATION_1 112
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 113 && MSGPACK_PP_ITERATION_FINISH_1 >= 113
#        define MSGPACK_PP_ITERATION_1 113
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 114 && MSGPACK_PP_ITERATION_FINISH_1 >= 114
#        define MSGPACK_PP_ITERATION_1 114
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 115 && MSGPACK_PP_ITERATION_FINISH_1 >= 115
#        define MSGPACK_PP_ITERATION_1 115
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 116 && MSGPACK_PP_ITERATION_FINISH_1 >= 116
#        define MSGPACK_PP_ITERATION_1 116
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 117 && MSGPACK_PP_ITERATION_FINISH_1 >= 117
#        define MSGPACK_PP_ITERATION_1 117
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 118 && MSGPACK_PP_ITERATION_FINISH_1 >= 118
#        define MSGPACK_PP_ITERATION_1 118
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 119 && MSGPACK_PP_ITERATION_FINISH_1 >= 119
#        define MSGPACK_PP_ITERATION_1 119
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 120 && MSGPACK_PP_ITERATION_FINISH_1 >= 120
#        define MSGPACK_PP_ITERATION_1 120
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 121 && MSGPACK_PP_ITERATION_FINISH_1 >= 121
#        define MSGPACK_PP_ITERATION_1 121
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 122 && MSGPACK_PP_ITERATION_FINISH_1 >= 122
#        define MSGPACK_PP_ITERATION_1 122
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 123 && MSGPACK_PP_ITERATION_FINISH_1 >= 123
#        define MSGPACK_PP_ITERATION_1 123
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 124 && MSGPACK_PP_ITERATION_FINISH_1 >= 124
#        define MSGPACK_PP_ITERATION_1 124
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 125 && MSGPACK_PP_ITERATION_FINISH_1 >= 125
#        define MSGPACK_PP_ITERATION_1 125
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 126 && MSGPACK_PP_ITERATION_FINISH_1 >= 126
#        define MSGPACK_PP_ITERATION_1 126
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 127 && MSGPACK_PP_ITERATION_FINISH_1 >= 127
#        define MSGPACK_PP_ITERATION_1 127
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 128 && MSGPACK_PP_ITERATION_FINISH_1 >= 128
#        define MSGPACK_PP_ITERATION_1 128
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 129 && MSGPACK_PP_ITERATION_FINISH_1 >= 129
#        define MSGPACK_PP_ITERATION_1 129
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 130 && MSGPACK_PP_ITERATION_FINISH_1 >= 130
#        define MSGPACK_PP_ITERATION_1 130
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 131 && MSGPACK_PP_ITERATION_FINISH_1 >= 131
#        define MSGPACK_PP_ITERATION_1 131
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 132 && MSGPACK_PP_ITERATION_FINISH_1 >= 132
#        define MSGPACK_PP_ITERATION_1 132
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 133 && MSGPACK_PP_ITERATION_FINISH_1 >= 133
#        define MSGPACK_PP_ITERATION_1 133
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 134 && MSGPACK_PP_ITERATION_FINISH_1 >= 134
#        define MSGPACK_PP_ITERATION_1 134
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 135 && MSGPACK_PP_ITERATION_FINISH_1 >= 135
#        define MSGPACK_PP_ITERATION_1 135
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 136 && MSGPACK_PP_ITERATION_FINISH_1 >= 136
#        define MSGPACK_PP_ITERATION_1 136
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 137 && MSGPACK_PP_ITERATION_FINISH_1 >= 137
#        define MSGPACK_PP_ITERATION_1 137
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 138 && MSGPACK_PP_ITERATION_FINISH_1 >= 138
#        define MSGPACK_PP_ITERATION_1 138
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 139 && MSGPACK_PP_ITERATION_FINISH_1 >= 139
#        define MSGPACK_PP_ITERATION_1 139
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 140 && MSGPACK_PP_ITERATION_FINISH_1 >= 140
#        define MSGPACK_PP_ITERATION_1 140
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 141 && MSGPACK_PP_ITERATION_FINISH_1 >= 141
#        define MSGPACK_PP_ITERATION_1 141
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 142 && MSGPACK_PP_ITERATION_FINISH_1 >= 142
#        define MSGPACK_PP_ITERATION_1 142
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 143 && MSGPACK_PP_ITERATION_FINISH_1 >= 143
#        define MSGPACK_PP_ITERATION_1 143
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 144 && MSGPACK_PP_ITERATION_FINISH_1 >= 144
#        define MSGPACK_PP_ITERATION_1 144
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 145 && MSGPACK_PP_ITERATION_FINISH_1 >= 145
#        define MSGPACK_PP_ITERATION_1 145
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 146 && MSGPACK_PP_ITERATION_FINISH_1 >= 146
#        define MSGPACK_PP_ITERATION_1 146
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 147 && MSGPACK_PP_ITERATION_FINISH_1 >= 147
#        define MSGPACK_PP_ITERATION_1 147
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 148 && MSGPACK_PP_ITERATION_FINISH_1 >= 148
#        define MSGPACK_PP_ITERATION_1 148
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 149 && MSGPACK_PP_ITERATION_FINISH_1 >= 149
#        define MSGPACK_PP_ITERATION_1 149
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 150 && MSGPACK_PP_ITERATION_FINISH_1 >= 150
#        define MSGPACK_PP_ITERATION_1 150
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 151 && MSGPACK_PP_ITERATION_FINISH_1 >= 151
#        define MSGPACK_PP_ITERATION_1 151
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 152 && MSGPACK_PP_ITERATION_FINISH_1 >= 152
#        define MSGPACK_PP_ITERATION_1 152
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 153 && MSGPACK_PP_ITERATION_FINISH_1 >= 153
#        define MSGPACK_PP_ITERATION_1 153
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 154 && MSGPACK_PP_ITERATION_FINISH_1 >= 154
#        define MSGPACK_PP_ITERATION_1 154
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 155 && MSGPACK_PP_ITERATION_FINISH_1 >= 155
#        define MSGPACK_PP_ITERATION_1 155
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 156 && MSGPACK_PP_ITERATION_FINISH_1 >= 156
#        define MSGPACK_PP_ITERATION_1 156
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 157 && MSGPACK_PP_ITERATION_FINISH_1 >= 157
#        define MSGPACK_PP_ITERATION_1 157
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 158 && MSGPACK_PP_ITERATION_FINISH_1 >= 158
#        define MSGPACK_PP_ITERATION_1 158
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 159 && MSGPACK_PP_ITERATION_FINISH_1 >= 159
#        define MSGPACK_PP_ITERATION_1 159
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 160 && MSGPACK_PP_ITERATION_FINISH_1 >= 160
#        define MSGPACK_PP_ITERATION_1 160
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 161 && MSGPACK_PP_ITERATION_FINISH_1 >= 161
#        define MSGPACK_PP_ITERATION_1 161
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 162 && MSGPACK_PP_ITERATION_FINISH_1 >= 162
#        define MSGPACK_PP_ITERATION_1 162
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 163 && MSGPACK_PP_ITERATION_FINISH_1 >= 163
#        define MSGPACK_PP_ITERATION_1 163
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 164 && MSGPACK_PP_ITERATION_FINISH_1 >= 164
#        define MSGPACK_PP_ITERATION_1 164
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 165 && MSGPACK_PP_ITERATION_FINISH_1 >= 165
#        define MSGPACK_PP_ITERATION_1 165
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 166 && MSGPACK_PP_ITERATION_FINISH_1 >= 166
#        define MSGPACK_PP_ITERATION_1 166
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 167 && MSGPACK_PP_ITERATION_FINISH_1 >= 167
#        define MSGPACK_PP_ITERATION_1 167
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 168 && MSGPACK_PP_ITERATION_FINISH_1 >= 168
#        define MSGPACK_PP_ITERATION_1 168
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 169 && MSGPACK_PP_ITERATION_FINISH_1 >= 169
#        define MSGPACK_PP_ITERATION_1 169
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 170 && MSGPACK_PP_ITERATION_FINISH_1 >= 170
#        define MSGPACK_PP_ITERATION_1 170
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 171 && MSGPACK_PP_ITERATION_FINISH_1 >= 171
#        define MSGPACK_PP_ITERATION_1 171
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 172 && MSGPACK_PP_ITERATION_FINISH_1 >= 172
#        define MSGPACK_PP_ITERATION_1 172
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 173 && MSGPACK_PP_ITERATION_FINISH_1 >= 173
#        define MSGPACK_PP_ITERATION_1 173
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 174 && MSGPACK_PP_ITERATION_FINISH_1 >= 174
#        define MSGPACK_PP_ITERATION_1 174
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 175 && MSGPACK_PP_ITERATION_FINISH_1 >= 175
#        define MSGPACK_PP_ITERATION_1 175
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 176 && MSGPACK_PP_ITERATION_FINISH_1 >= 176
#        define MSGPACK_PP_ITERATION_1 176
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 177 && MSGPACK_PP_ITERATION_FINISH_1 >= 177
#        define MSGPACK_PP_ITERATION_1 177
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 178 && MSGPACK_PP_ITERATION_FINISH_1 >= 178
#        define MSGPACK_PP_ITERATION_1 178
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 179 && MSGPACK_PP_ITERATION_FINISH_1 >= 179
#        define MSGPACK_PP_ITERATION_1 179
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 180 && MSGPACK_PP_ITERATION_FINISH_1 >= 180
#        define MSGPACK_PP_ITERATION_1 180
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 181 && MSGPACK_PP_ITERATION_FINISH_1 >= 181
#        define MSGPACK_PP_ITERATION_1 181
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 182 && MSGPACK_PP_ITERATION_FINISH_1 >= 182
#        define MSGPACK_PP_ITERATION_1 182
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 183 && MSGPACK_PP_ITERATION_FINISH_1 >= 183
#        define MSGPACK_PP_ITERATION_1 183
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 184 && MSGPACK_PP_ITERATION_FINISH_1 >= 184
#        define MSGPACK_PP_ITERATION_1 184
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 185 && MSGPACK_PP_ITERATION_FINISH_1 >= 185
#        define MSGPACK_PP_ITERATION_1 185
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 186 && MSGPACK_PP_ITERATION_FINISH_1 >= 186
#        define MSGPACK_PP_ITERATION_1 186
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 187 && MSGPACK_PP_ITERATION_FINISH_1 >= 187
#        define MSGPACK_PP_ITERATION_1 187
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 188 && MSGPACK_PP_ITERATION_FINISH_1 >= 188
#        define MSGPACK_PP_ITERATION_1 188
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 189 && MSGPACK_PP_ITERATION_FINISH_1 >= 189
#        define MSGPACK_PP_ITERATION_1 189
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 190 && MSGPACK_PP_ITERATION_FINISH_1 >= 190
#        define MSGPACK_PP_ITERATION_1 190
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 191 && MSGPACK_PP_ITERATION_FINISH_1 >= 191
#        define MSGPACK_PP_ITERATION_1 191
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 192 && MSGPACK_PP_ITERATION_FINISH_1 >= 192
#        define MSGPACK_PP_ITERATION_1 192
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 193 && MSGPACK_PP_ITERATION_FINISH_1 >= 193
#        define MSGPACK_PP_ITERATION_1 193
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 194 && MSGPACK_PP_ITERATION_FINISH_1 >= 194
#        define MSGPACK_PP_ITERATION_1 194
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 195 && MSGPACK_PP_ITERATION_FINISH_1 >= 195
#        define MSGPACK_PP_ITERATION_1 195
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 196 && MSGPACK_PP_ITERATION_FINISH_1 >= 196
#        define MSGPACK_PP_ITERATION_1 196
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 197 && MSGPACK_PP_ITERATION_FINISH_1 >= 197
#        define MSGPACK_PP_ITERATION_1 197
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 198 && MSGPACK_PP_ITERATION_FINISH_1 >= 198
#        define MSGPACK_PP_ITERATION_1 198
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 199 && MSGPACK_PP_ITERATION_FINISH_1 >= 199
#        define MSGPACK_PP_ITERATION_1 199
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 200 && MSGPACK_PP_ITERATION_FINISH_1 >= 200
#        define MSGPACK_PP_ITERATION_1 200
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 201 && MSGPACK_PP_ITERATION_FINISH_1 >= 201
#        define MSGPACK_PP_ITERATION_1 201
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 202 && MSGPACK_PP_ITERATION_FINISH_1 >= 202
#        define MSGPACK_PP_ITERATION_1 202
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 203 && MSGPACK_PP_ITERATION_FINISH_1 >= 203
#        define MSGPACK_PP_ITERATION_1 203
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 204 && MSGPACK_PP_ITERATION_FINISH_1 >= 204
#        define MSGPACK_PP_ITERATION_1 204
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 205 && MSGPACK_PP_ITERATION_FINISH_1 >= 205
#        define MSGPACK_PP_ITERATION_1 205
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 206 && MSGPACK_PP_ITERATION_FINISH_1 >= 206
#        define MSGPACK_PP_ITERATION_1 206
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 207 && MSGPACK_PP_ITERATION_FINISH_1 >= 207
#        define MSGPACK_PP_ITERATION_1 207
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 208 && MSGPACK_PP_ITERATION_FINISH_1 >= 208
#        define MSGPACK_PP_ITERATION_1 208
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 209 && MSGPACK_PP_ITERATION_FINISH_1 >= 209
#        define MSGPACK_PP_ITERATION_1 209
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 210 && MSGPACK_PP_ITERATION_FINISH_1 >= 210
#        define MSGPACK_PP_ITERATION_1 210
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 211 && MSGPACK_PP_ITERATION_FINISH_1 >= 211
#        define MSGPACK_PP_ITERATION_1 211
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 212 && MSGPACK_PP_ITERATION_FINISH_1 >= 212
#        define MSGPACK_PP_ITERATION_1 212
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 213 && MSGPACK_PP_ITERATION_FINISH_1 >= 213
#        define MSGPACK_PP_ITERATION_1 213
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 214 && MSGPACK_PP_ITERATION_FINISH_1 >= 214
#        define MSGPACK_PP_ITERATION_1 214
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 215 && MSGPACK_PP_ITERATION_FINISH_1 >= 215
#        define MSGPACK_PP_ITERATION_1 215
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 216 && MSGPACK_PP_ITERATION_FINISH_1 >= 216
#        define MSGPACK_PP_ITERATION_1 216
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 217 && MSGPACK_PP_ITERATION_FINISH_1 >= 217
#        define MSGPACK_PP_ITERATION_1 217
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 218 && MSGPACK_PP_ITERATION_FINISH_1 >= 218
#        define MSGPACK_PP_ITERATION_1 218
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 219 && MSGPACK_PP_ITERATION_FINISH_1 >= 219
#        define MSGPACK_PP_ITERATION_1 219
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 220 && MSGPACK_PP_ITERATION_FINISH_1 >= 220
#        define MSGPACK_PP_ITERATION_1 220
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 221 && MSGPACK_PP_ITERATION_FINISH_1 >= 221
#        define MSGPACK_PP_ITERATION_1 221
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 222 && MSGPACK_PP_ITERATION_FINISH_1 >= 222
#        define MSGPACK_PP_ITERATION_1 222
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 223 && MSGPACK_PP_ITERATION_FINISH_1 >= 223
#        define MSGPACK_PP_ITERATION_1 223
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 224 && MSGPACK_PP_ITERATION_FINISH_1 >= 224
#        define MSGPACK_PP_ITERATION_1 224
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 225 && MSGPACK_PP_ITERATION_FINISH_1 >= 225
#        define MSGPACK_PP_ITERATION_1 225
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 226 && MSGPACK_PP_ITERATION_FINISH_1 >= 226
#        define MSGPACK_PP_ITERATION_1 226
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 227 && MSGPACK_PP_ITERATION_FINISH_1 >= 227
#        define MSGPACK_PP_ITERATION_1 227
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 228 && MSGPACK_PP_ITERATION_FINISH_1 >= 228
#        define MSGPACK_PP_ITERATION_1 228
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 229 && MSGPACK_PP_ITERATION_FINISH_1 >= 229
#        define MSGPACK_PP_ITERATION_1 229
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 230 && MSGPACK_PP_ITERATION_FINISH_1 >= 230
#        define MSGPACK_PP_ITERATION_1 230
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 231 && MSGPACK_PP_ITERATION_FINISH_1 >= 231
#        define MSGPACK_PP_ITERATION_1 231
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 232 && MSGPACK_PP_ITERATION_FINISH_1 >= 232
#        define MSGPACK_PP_ITERATION_1 232
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 233 && MSGPACK_PP_ITERATION_FINISH_1 >= 233
#        define MSGPACK_PP_ITERATION_1 233
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 234 && MSGPACK_PP_ITERATION_FINISH_1 >= 234
#        define MSGPACK_PP_ITERATION_1 234
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 235 && MSGPACK_PP_ITERATION_FINISH_1 >= 235
#        define MSGPACK_PP_ITERATION_1 235
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 236 && MSGPACK_PP_ITERATION_FINISH_1 >= 236
#        define MSGPACK_PP_ITERATION_1 236
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 237 && MSGPACK_PP_ITERATION_FINISH_1 >= 237
#        define MSGPACK_PP_ITERATION_1 237
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 238 && MSGPACK_PP_ITERATION_FINISH_1 >= 238
#        define MSGPACK_PP_ITERATION_1 238
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 239 && MSGPACK_PP_ITERATION_FINISH_1 >= 239
#        define MSGPACK_PP_ITERATION_1 239
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 240 && MSGPACK_PP_ITERATION_FINISH_1 >= 240
#        define MSGPACK_PP_ITERATION_1 240
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 241 && MSGPACK_PP_ITERATION_FINISH_1 >= 241
#        define MSGPACK_PP_ITERATION_1 241
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 242 && MSGPACK_PP_ITERATION_FINISH_1 >= 242
#        define MSGPACK_PP_ITERATION_1 242
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 243 && MSGPACK_PP_ITERATION_FINISH_1 >= 243
#        define MSGPACK_PP_ITERATION_1 243
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 244 && MSGPACK_PP_ITERATION_FINISH_1 >= 244
#        define MSGPACK_PP_ITERATION_1 244
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 245 && MSGPACK_PP_ITERATION_FINISH_1 >= 245
#        define MSGPACK_PP_ITERATION_1 245
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 246 && MSGPACK_PP_ITERATION_FINISH_1 >= 246
#        define MSGPACK_PP_ITERATION_1 246
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 247 && MSGPACK_PP_ITERATION_FINISH_1 >= 247
#        define MSGPACK_PP_ITERATION_1 247
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 248 && MSGPACK_PP_ITERATION_FINISH_1 >= 248
#        define MSGPACK_PP_ITERATION_1 248
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 249 && MSGPACK_PP_ITERATION_FINISH_1 >= 249
#        define MSGPACK_PP_ITERATION_1 249
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 250 && MSGPACK_PP_ITERATION_FINISH_1 >= 250
#        define MSGPACK_PP_ITERATION_1 250
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 251 && MSGPACK_PP_ITERATION_FINISH_1 >= 251
#        define MSGPACK_PP_ITERATION_1 251
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 252 && MSGPACK_PP_ITERATION_FINISH_1 >= 252
#        define MSGPACK_PP_ITERATION_1 252
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 253 && MSGPACK_PP_ITERATION_FINISH_1 >= 253
#        define MSGPACK_PP_ITERATION_1 253
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 254 && MSGPACK_PP_ITERATION_FINISH_1 >= 254
#        define MSGPACK_PP_ITERATION_1 254
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 255 && MSGPACK_PP_ITERATION_FINISH_1 >= 255
#        define MSGPACK_PP_ITERATION_1 255
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
#    if MSGPACK_PP_ITERATION_START_1 <= 256 && MSGPACK_PP_ITERATION_FINISH_1 >= 256
#        define MSGPACK_PP_ITERATION_1 256
#        include MSGPACK_PP_FILENAME_1
#        undef MSGPACK_PP_ITERATION_1
#    endif
# endif
#
# undef MSGPACK_PP_IS_ITERATING
#
# undef MSGPACK_PP_ITERATION_DEPTH
# define MSGPACK_PP_ITERATION_DEPTH() 0
#
# undef MSGPACK_PP_ITERATION_START_1
# undef MSGPACK_PP_ITERATION_FINISH_1
# undef MSGPACK_PP_FILENAME_1
#
# undef MSGPACK_PP_ITERATION_FLAGS_1
# undef MSGPACK_PP_ITERATION_PARAMS_1
