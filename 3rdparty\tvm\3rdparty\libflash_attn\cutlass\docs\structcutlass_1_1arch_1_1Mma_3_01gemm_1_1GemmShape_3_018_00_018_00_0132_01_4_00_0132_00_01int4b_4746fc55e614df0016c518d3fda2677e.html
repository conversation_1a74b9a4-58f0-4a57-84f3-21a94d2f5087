<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 32 &gt;, 32, int4b_t, layout::RowMajor, uint4b_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAdd &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1arch.html">arch</a></li><li class="navelem"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html">Mma&lt; gemm::GemmShape&lt; 8, 8, 32 &gt;, 32, int4b_t, layout::RowMajor, uint4b_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAdd &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_3c87ec4ca9f646f0bf0bead0e5cf262c.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 32 &gt;, 32, int4b_t, layout::RowMajor, uint4b_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAdd &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Matrix multiply-add operation: S32 = S4 * U4 + S32.  
</p>

<p><code>#include &lt;<a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a261b8c82bfd2590e2e99ad34ae4d6b4a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a261b8c82bfd2590e2e99ad34ae4d6b4a">Shape</a> = <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;</td></tr>
<tr class="separator:a261b8c82bfd2590e2e99ad34ae4d6b4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa45a569adfa09653037b5d1bf07bf6d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#aaa45a569adfa09653037b5d1bf07bf6d">ElementA</a> = <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a></td></tr>
<tr class="separator:aaa45a569adfa09653037b5d1bf07bf6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b2bd84cca60d0445b2ca537ffba656f"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a3b2bd84cca60d0445b2ca537ffba656f">LayoutA</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a3b2bd84cca60d0445b2ca537ffba656f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd536cf5cc0d0da6289a4dd3cf980d53"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#acd536cf5cc0d0da6289a4dd3cf980d53">FragmentA</a> = Array&lt; <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, 8 &gt;</td></tr>
<tr class="separator:acd536cf5cc0d0da6289a4dd3cf980d53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb4896ccb36071f799c0ef596c89e784"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#afb4896ccb36071f799c0ef596c89e784">ElementB</a> = <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a></td></tr>
<tr class="separator:afb4896ccb36071f799c0ef596c89e784"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35d3ac13044ce4cdefafb1dd859253b0"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a35d3ac13044ce4cdefafb1dd859253b0">LayoutB</a> = <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td></tr>
<tr class="separator:a35d3ac13044ce4cdefafb1dd859253b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22c4bc1ed799179c65b7b5a3794579ab"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a22c4bc1ed799179c65b7b5a3794579ab">FragmentB</a> = Array&lt; <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, 8 &gt;</td></tr>
<tr class="separator:a22c4bc1ed799179c65b7b5a3794579ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad65d8c17f86c93ae832ca8aafe643caf"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#ad65d8c17f86c93ae832ca8aafe643caf">ElementC</a> = int</td></tr>
<tr class="separator:ad65d8c17f86c93ae832ca8aafe643caf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ef6b7ff2696d4cd47fcfbaeb24adc22"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a5ef6b7ff2696d4cd47fcfbaeb24adc22">LayoutC</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a5ef6b7ff2696d4cd47fcfbaeb24adc22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f965468f743c33c92de38d80f4dfc25"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a8f965468f743c33c92de38d80f4dfc25">FragmentC</a> = Array&lt; int, 2 &gt;</td></tr>
<tr class="separator:a8f965468f743c33c92de38d80f4dfc25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ad193beaf808eb0cd1e058bd8f8aa90"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a1ad193beaf808eb0cd1e058bd8f8aa90">Operator</a> = OpMultiplyAdd</td></tr>
<tr class="separator:a1ad193beaf808eb0cd1e058bd8f8aa90"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a0d76a6b029d10c796d2923f000eeea29"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a0d76a6b029d10c796d2923f000eeea29">operator()</a> (<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a8f965468f743c33c92de38d80f4dfc25">FragmentC</a> &amp;d, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#acd536cf5cc0d0da6289a4dd3cf980d53">FragmentA</a> const &amp;a, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a22c4bc1ed799179c65b7b5a3794579ab">FragmentB</a> const &amp;b, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a8f965468f743c33c92de38d80f4dfc25">FragmentC</a> const &amp;c) const </td></tr>
<tr class="memdesc:a0d76a6b029d10c796d2923f000eeea29"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes multiply-add.  <a href="#a0d76a6b029d10c796d2923f000eeea29">More...</a><br /></td></tr>
<tr class="separator:a0d76a6b029d10c796d2923f000eeea29"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="aaa45a569adfa09653037b5d1bf07bf6d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#aaa45a569adfa09653037b5d1bf07bf6d">ElementA</a> =  <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afb4896ccb36071f799c0ef596c89e784"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#afb4896ccb36071f799c0ef596c89e784">ElementB</a> =  <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad65d8c17f86c93ae832ca8aafe643caf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#ad65d8c17f86c93ae832ca8aafe643caf">ElementC</a> =  int</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acd536cf5cc0d0da6289a4dd3cf980d53"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#acd536cf5cc0d0da6289a4dd3cf980d53">FragmentA</a> =  Array&lt;<a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, 8&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a22c4bc1ed799179c65b7b5a3794579ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a22c4bc1ed799179c65b7b5a3794579ab">FragmentB</a> =  Array&lt;<a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, 8&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8f965468f743c33c92de38d80f4dfc25"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a8f965468f743c33c92de38d80f4dfc25">FragmentC</a> =  Array&lt;int, 2&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3b2bd84cca60d0445b2ca537ffba656f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a3b2bd84cca60d0445b2ca537ffba656f">LayoutA</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a35d3ac13044ce4cdefafb1dd859253b0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a35d3ac13044ce4cdefafb1dd859253b0">LayoutB</a> =  <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5ef6b7ff2696d4cd47fcfbaeb24adc22"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a5ef6b7ff2696d4cd47fcfbaeb24adc22">LayoutC</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1ad193beaf808eb0cd1e058bd8f8aa90"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a1ad193beaf808eb0cd1e058bd8f8aa90">Operator</a> =  OpMultiplyAdd</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a261b8c82bfd2590e2e99ad34ae4d6b4a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a261b8c82bfd2590e2e99ad34ae4d6b4a">Shape</a> =  <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt;8,8,32&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a0d76a6b029d10c796d2923f000eeea29"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a8f965468f743c33c92de38d80f4dfc25">FragmentC</a> &amp;&#160;</td>
          <td class="paramname"><em>d</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#acd536cf5cc0d0da6289a4dd3cf980d53">FragmentA</a> const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a22c4bc1ed799179c65b7b5a3794579ab">FragmentB</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_4746fc55e614df0016c518d3fda2677e.html#a8f965468f743c33c92de38d80f4dfc25">FragmentC</a> const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
