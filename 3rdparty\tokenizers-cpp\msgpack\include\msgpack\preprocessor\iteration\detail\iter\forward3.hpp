# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# if defined(MSGPACK_PP_ITERATION_LIMITS)
#    if !defined(MSGPACK_PP_FILENAME_3)
#        error MSGPACK_PP_ERROR:  depth #3 filename is not defined
#    endif
#    define MSGPACK_PP_VALUE MSGPACK_PP_TUPLE_ELEM(2, 0, MSGPACK_PP_ITERATION_LIMITS)
#    include <msgpack/preprocessor/iteration/detail/bounds/lower3.hpp>
#    define MSGPACK_PP_VALUE MSGPACK_PP_TUPLE_ELEM(2, 1, MSGPACK_PP_ITERATION_LIMITS)
#    include <msgpack/preprocessor/iteration/detail/bounds/upper3.hpp>
#    define MSGPACK_PP_ITERATION_FLAGS_3() 0
#    undef MSGPACK_PP_ITERATION_LIMITS
# elif defined(MSGPACK_PP_ITERATION_PARAMS_3)
#    define MSGPACK_PP_VALUE MSGPACK_PP_ARRAY_ELEM(0, MSGPACK_PP_ITERATION_PARAMS_3)
#    include <msgpack/preprocessor/iteration/detail/bounds/lower3.hpp>
#    define MSGPACK_PP_VALUE MSGPACK_PP_ARRAY_ELEM(1, MSGPACK_PP_ITERATION_PARAMS_3)
#    include <msgpack/preprocessor/iteration/detail/bounds/upper3.hpp>
#    define MSGPACK_PP_FILENAME_3 MSGPACK_PP_ARRAY_ELEM(2, MSGPACK_PP_ITERATION_PARAMS_3)
#    if MSGPACK_PP_ARRAY_SIZE(MSGPACK_PP_ITERATION_PARAMS_3) >= 4
#        define MSGPACK_PP_ITERATION_FLAGS_3() MSGPACK_PP_ARRAY_ELEM(3, MSGPACK_PP_ITERATION_PARAMS_3)
#    else
#        define MSGPACK_PP_ITERATION_FLAGS_3() 0
#    endif
# else
#    error MSGPACK_PP_ERROR:  depth #3 iteration boundaries or filename not defined
# endif
#
# undef MSGPACK_PP_ITERATION_DEPTH
# define MSGPACK_PP_ITERATION_DEPTH() 3
#
# if (MSGPACK_PP_ITERATION_START_3) > (MSGPACK_PP_ITERATION_FINISH_3)
#    include <msgpack/preprocessor/iteration/detail/iter/reverse3.hpp>
# else
#    if MSGPACK_PP_ITERATION_START_3 <= 0 && MSGPACK_PP_ITERATION_FINISH_3 >= 0
#        define MSGPACK_PP_ITERATION_3 0
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 1 && MSGPACK_PP_ITERATION_FINISH_3 >= 1
#        define MSGPACK_PP_ITERATION_3 1
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 2 && MSGPACK_PP_ITERATION_FINISH_3 >= 2
#        define MSGPACK_PP_ITERATION_3 2
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 3 && MSGPACK_PP_ITERATION_FINISH_3 >= 3
#        define MSGPACK_PP_ITERATION_3 3
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 4 && MSGPACK_PP_ITERATION_FINISH_3 >= 4
#        define MSGPACK_PP_ITERATION_3 4
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 5 && MSGPACK_PP_ITERATION_FINISH_3 >= 5
#        define MSGPACK_PP_ITERATION_3 5
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 6 && MSGPACK_PP_ITERATION_FINISH_3 >= 6
#        define MSGPACK_PP_ITERATION_3 6
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 7 && MSGPACK_PP_ITERATION_FINISH_3 >= 7
#        define MSGPACK_PP_ITERATION_3 7
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 8 && MSGPACK_PP_ITERATION_FINISH_3 >= 8
#        define MSGPACK_PP_ITERATION_3 8
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 9 && MSGPACK_PP_ITERATION_FINISH_3 >= 9
#        define MSGPACK_PP_ITERATION_3 9
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 10 && MSGPACK_PP_ITERATION_FINISH_3 >= 10
#        define MSGPACK_PP_ITERATION_3 10
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 11 && MSGPACK_PP_ITERATION_FINISH_3 >= 11
#        define MSGPACK_PP_ITERATION_3 11
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 12 && MSGPACK_PP_ITERATION_FINISH_3 >= 12
#        define MSGPACK_PP_ITERATION_3 12
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 13 && MSGPACK_PP_ITERATION_FINISH_3 >= 13
#        define MSGPACK_PP_ITERATION_3 13
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 14 && MSGPACK_PP_ITERATION_FINISH_3 >= 14
#        define MSGPACK_PP_ITERATION_3 14
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 15 && MSGPACK_PP_ITERATION_FINISH_3 >= 15
#        define MSGPACK_PP_ITERATION_3 15
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 16 && MSGPACK_PP_ITERATION_FINISH_3 >= 16
#        define MSGPACK_PP_ITERATION_3 16
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 17 && MSGPACK_PP_ITERATION_FINISH_3 >= 17
#        define MSGPACK_PP_ITERATION_3 17
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 18 && MSGPACK_PP_ITERATION_FINISH_3 >= 18
#        define MSGPACK_PP_ITERATION_3 18
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 19 && MSGPACK_PP_ITERATION_FINISH_3 >= 19
#        define MSGPACK_PP_ITERATION_3 19
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 20 && MSGPACK_PP_ITERATION_FINISH_3 >= 20
#        define MSGPACK_PP_ITERATION_3 20
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 21 && MSGPACK_PP_ITERATION_FINISH_3 >= 21
#        define MSGPACK_PP_ITERATION_3 21
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 22 && MSGPACK_PP_ITERATION_FINISH_3 >= 22
#        define MSGPACK_PP_ITERATION_3 22
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 23 && MSGPACK_PP_ITERATION_FINISH_3 >= 23
#        define MSGPACK_PP_ITERATION_3 23
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 24 && MSGPACK_PP_ITERATION_FINISH_3 >= 24
#        define MSGPACK_PP_ITERATION_3 24
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 25 && MSGPACK_PP_ITERATION_FINISH_3 >= 25
#        define MSGPACK_PP_ITERATION_3 25
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 26 && MSGPACK_PP_ITERATION_FINISH_3 >= 26
#        define MSGPACK_PP_ITERATION_3 26
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 27 && MSGPACK_PP_ITERATION_FINISH_3 >= 27
#        define MSGPACK_PP_ITERATION_3 27
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 28 && MSGPACK_PP_ITERATION_FINISH_3 >= 28
#        define MSGPACK_PP_ITERATION_3 28
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 29 && MSGPACK_PP_ITERATION_FINISH_3 >= 29
#        define MSGPACK_PP_ITERATION_3 29
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 30 && MSGPACK_PP_ITERATION_FINISH_3 >= 30
#        define MSGPACK_PP_ITERATION_3 30
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 31 && MSGPACK_PP_ITERATION_FINISH_3 >= 31
#        define MSGPACK_PP_ITERATION_3 31
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 32 && MSGPACK_PP_ITERATION_FINISH_3 >= 32
#        define MSGPACK_PP_ITERATION_3 32
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 33 && MSGPACK_PP_ITERATION_FINISH_3 >= 33
#        define MSGPACK_PP_ITERATION_3 33
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 34 && MSGPACK_PP_ITERATION_FINISH_3 >= 34
#        define MSGPACK_PP_ITERATION_3 34
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 35 && MSGPACK_PP_ITERATION_FINISH_3 >= 35
#        define MSGPACK_PP_ITERATION_3 35
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 36 && MSGPACK_PP_ITERATION_FINISH_3 >= 36
#        define MSGPACK_PP_ITERATION_3 36
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 37 && MSGPACK_PP_ITERATION_FINISH_3 >= 37
#        define MSGPACK_PP_ITERATION_3 37
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 38 && MSGPACK_PP_ITERATION_FINISH_3 >= 38
#        define MSGPACK_PP_ITERATION_3 38
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 39 && MSGPACK_PP_ITERATION_FINISH_3 >= 39
#        define MSGPACK_PP_ITERATION_3 39
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 40 && MSGPACK_PP_ITERATION_FINISH_3 >= 40
#        define MSGPACK_PP_ITERATION_3 40
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 41 && MSGPACK_PP_ITERATION_FINISH_3 >= 41
#        define MSGPACK_PP_ITERATION_3 41
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 42 && MSGPACK_PP_ITERATION_FINISH_3 >= 42
#        define MSGPACK_PP_ITERATION_3 42
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 43 && MSGPACK_PP_ITERATION_FINISH_3 >= 43
#        define MSGPACK_PP_ITERATION_3 43
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 44 && MSGPACK_PP_ITERATION_FINISH_3 >= 44
#        define MSGPACK_PP_ITERATION_3 44
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 45 && MSGPACK_PP_ITERATION_FINISH_3 >= 45
#        define MSGPACK_PP_ITERATION_3 45
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 46 && MSGPACK_PP_ITERATION_FINISH_3 >= 46
#        define MSGPACK_PP_ITERATION_3 46
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 47 && MSGPACK_PP_ITERATION_FINISH_3 >= 47
#        define MSGPACK_PP_ITERATION_3 47
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 48 && MSGPACK_PP_ITERATION_FINISH_3 >= 48
#        define MSGPACK_PP_ITERATION_3 48
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 49 && MSGPACK_PP_ITERATION_FINISH_3 >= 49
#        define MSGPACK_PP_ITERATION_3 49
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 50 && MSGPACK_PP_ITERATION_FINISH_3 >= 50
#        define MSGPACK_PP_ITERATION_3 50
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 51 && MSGPACK_PP_ITERATION_FINISH_3 >= 51
#        define MSGPACK_PP_ITERATION_3 51
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 52 && MSGPACK_PP_ITERATION_FINISH_3 >= 52
#        define MSGPACK_PP_ITERATION_3 52
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 53 && MSGPACK_PP_ITERATION_FINISH_3 >= 53
#        define MSGPACK_PP_ITERATION_3 53
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 54 && MSGPACK_PP_ITERATION_FINISH_3 >= 54
#        define MSGPACK_PP_ITERATION_3 54
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 55 && MSGPACK_PP_ITERATION_FINISH_3 >= 55
#        define MSGPACK_PP_ITERATION_3 55
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 56 && MSGPACK_PP_ITERATION_FINISH_3 >= 56
#        define MSGPACK_PP_ITERATION_3 56
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 57 && MSGPACK_PP_ITERATION_FINISH_3 >= 57
#        define MSGPACK_PP_ITERATION_3 57
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 58 && MSGPACK_PP_ITERATION_FINISH_3 >= 58
#        define MSGPACK_PP_ITERATION_3 58
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 59 && MSGPACK_PP_ITERATION_FINISH_3 >= 59
#        define MSGPACK_PP_ITERATION_3 59
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 60 && MSGPACK_PP_ITERATION_FINISH_3 >= 60
#        define MSGPACK_PP_ITERATION_3 60
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 61 && MSGPACK_PP_ITERATION_FINISH_3 >= 61
#        define MSGPACK_PP_ITERATION_3 61
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 62 && MSGPACK_PP_ITERATION_FINISH_3 >= 62
#        define MSGPACK_PP_ITERATION_3 62
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 63 && MSGPACK_PP_ITERATION_FINISH_3 >= 63
#        define MSGPACK_PP_ITERATION_3 63
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 64 && MSGPACK_PP_ITERATION_FINISH_3 >= 64
#        define MSGPACK_PP_ITERATION_3 64
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 65 && MSGPACK_PP_ITERATION_FINISH_3 >= 65
#        define MSGPACK_PP_ITERATION_3 65
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 66 && MSGPACK_PP_ITERATION_FINISH_3 >= 66
#        define MSGPACK_PP_ITERATION_3 66
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 67 && MSGPACK_PP_ITERATION_FINISH_3 >= 67
#        define MSGPACK_PP_ITERATION_3 67
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 68 && MSGPACK_PP_ITERATION_FINISH_3 >= 68
#        define MSGPACK_PP_ITERATION_3 68
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 69 && MSGPACK_PP_ITERATION_FINISH_3 >= 69
#        define MSGPACK_PP_ITERATION_3 69
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 70 && MSGPACK_PP_ITERATION_FINISH_3 >= 70
#        define MSGPACK_PP_ITERATION_3 70
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 71 && MSGPACK_PP_ITERATION_FINISH_3 >= 71
#        define MSGPACK_PP_ITERATION_3 71
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 72 && MSGPACK_PP_ITERATION_FINISH_3 >= 72
#        define MSGPACK_PP_ITERATION_3 72
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 73 && MSGPACK_PP_ITERATION_FINISH_3 >= 73
#        define MSGPACK_PP_ITERATION_3 73
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 74 && MSGPACK_PP_ITERATION_FINISH_3 >= 74
#        define MSGPACK_PP_ITERATION_3 74
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 75 && MSGPACK_PP_ITERATION_FINISH_3 >= 75
#        define MSGPACK_PP_ITERATION_3 75
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 76 && MSGPACK_PP_ITERATION_FINISH_3 >= 76
#        define MSGPACK_PP_ITERATION_3 76
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 77 && MSGPACK_PP_ITERATION_FINISH_3 >= 77
#        define MSGPACK_PP_ITERATION_3 77
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 78 && MSGPACK_PP_ITERATION_FINISH_3 >= 78
#        define MSGPACK_PP_ITERATION_3 78
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 79 && MSGPACK_PP_ITERATION_FINISH_3 >= 79
#        define MSGPACK_PP_ITERATION_3 79
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 80 && MSGPACK_PP_ITERATION_FINISH_3 >= 80
#        define MSGPACK_PP_ITERATION_3 80
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 81 && MSGPACK_PP_ITERATION_FINISH_3 >= 81
#        define MSGPACK_PP_ITERATION_3 81
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 82 && MSGPACK_PP_ITERATION_FINISH_3 >= 82
#        define MSGPACK_PP_ITERATION_3 82
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 83 && MSGPACK_PP_ITERATION_FINISH_3 >= 83
#        define MSGPACK_PP_ITERATION_3 83
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 84 && MSGPACK_PP_ITERATION_FINISH_3 >= 84
#        define MSGPACK_PP_ITERATION_3 84
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 85 && MSGPACK_PP_ITERATION_FINISH_3 >= 85
#        define MSGPACK_PP_ITERATION_3 85
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 86 && MSGPACK_PP_ITERATION_FINISH_3 >= 86
#        define MSGPACK_PP_ITERATION_3 86
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 87 && MSGPACK_PP_ITERATION_FINISH_3 >= 87
#        define MSGPACK_PP_ITERATION_3 87
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 88 && MSGPACK_PP_ITERATION_FINISH_3 >= 88
#        define MSGPACK_PP_ITERATION_3 88
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 89 && MSGPACK_PP_ITERATION_FINISH_3 >= 89
#        define MSGPACK_PP_ITERATION_3 89
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 90 && MSGPACK_PP_ITERATION_FINISH_3 >= 90
#        define MSGPACK_PP_ITERATION_3 90
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 91 && MSGPACK_PP_ITERATION_FINISH_3 >= 91
#        define MSGPACK_PP_ITERATION_3 91
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 92 && MSGPACK_PP_ITERATION_FINISH_3 >= 92
#        define MSGPACK_PP_ITERATION_3 92
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 93 && MSGPACK_PP_ITERATION_FINISH_3 >= 93
#        define MSGPACK_PP_ITERATION_3 93
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 94 && MSGPACK_PP_ITERATION_FINISH_3 >= 94
#        define MSGPACK_PP_ITERATION_3 94
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 95 && MSGPACK_PP_ITERATION_FINISH_3 >= 95
#        define MSGPACK_PP_ITERATION_3 95
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 96 && MSGPACK_PP_ITERATION_FINISH_3 >= 96
#        define MSGPACK_PP_ITERATION_3 96
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 97 && MSGPACK_PP_ITERATION_FINISH_3 >= 97
#        define MSGPACK_PP_ITERATION_3 97
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 98 && MSGPACK_PP_ITERATION_FINISH_3 >= 98
#        define MSGPACK_PP_ITERATION_3 98
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 99 && MSGPACK_PP_ITERATION_FINISH_3 >= 99
#        define MSGPACK_PP_ITERATION_3 99
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 100 && MSGPACK_PP_ITERATION_FINISH_3 >= 100
#        define MSGPACK_PP_ITERATION_3 100
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 101 && MSGPACK_PP_ITERATION_FINISH_3 >= 101
#        define MSGPACK_PP_ITERATION_3 101
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 102 && MSGPACK_PP_ITERATION_FINISH_3 >= 102
#        define MSGPACK_PP_ITERATION_3 102
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 103 && MSGPACK_PP_ITERATION_FINISH_3 >= 103
#        define MSGPACK_PP_ITERATION_3 103
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 104 && MSGPACK_PP_ITERATION_FINISH_3 >= 104
#        define MSGPACK_PP_ITERATION_3 104
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 105 && MSGPACK_PP_ITERATION_FINISH_3 >= 105
#        define MSGPACK_PP_ITERATION_3 105
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 106 && MSGPACK_PP_ITERATION_FINISH_3 >= 106
#        define MSGPACK_PP_ITERATION_3 106
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 107 && MSGPACK_PP_ITERATION_FINISH_3 >= 107
#        define MSGPACK_PP_ITERATION_3 107
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 108 && MSGPACK_PP_ITERATION_FINISH_3 >= 108
#        define MSGPACK_PP_ITERATION_3 108
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 109 && MSGPACK_PP_ITERATION_FINISH_3 >= 109
#        define MSGPACK_PP_ITERATION_3 109
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 110 && MSGPACK_PP_ITERATION_FINISH_3 >= 110
#        define MSGPACK_PP_ITERATION_3 110
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 111 && MSGPACK_PP_ITERATION_FINISH_3 >= 111
#        define MSGPACK_PP_ITERATION_3 111
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 112 && MSGPACK_PP_ITERATION_FINISH_3 >= 112
#        define MSGPACK_PP_ITERATION_3 112
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 113 && MSGPACK_PP_ITERATION_FINISH_3 >= 113
#        define MSGPACK_PP_ITERATION_3 113
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 114 && MSGPACK_PP_ITERATION_FINISH_3 >= 114
#        define MSGPACK_PP_ITERATION_3 114
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 115 && MSGPACK_PP_ITERATION_FINISH_3 >= 115
#        define MSGPACK_PP_ITERATION_3 115
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 116 && MSGPACK_PP_ITERATION_FINISH_3 >= 116
#        define MSGPACK_PP_ITERATION_3 116
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 117 && MSGPACK_PP_ITERATION_FINISH_3 >= 117
#        define MSGPACK_PP_ITERATION_3 117
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 118 && MSGPACK_PP_ITERATION_FINISH_3 >= 118
#        define MSGPACK_PP_ITERATION_3 118
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 119 && MSGPACK_PP_ITERATION_FINISH_3 >= 119
#        define MSGPACK_PP_ITERATION_3 119
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 120 && MSGPACK_PP_ITERATION_FINISH_3 >= 120
#        define MSGPACK_PP_ITERATION_3 120
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 121 && MSGPACK_PP_ITERATION_FINISH_3 >= 121
#        define MSGPACK_PP_ITERATION_3 121
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 122 && MSGPACK_PP_ITERATION_FINISH_3 >= 122
#        define MSGPACK_PP_ITERATION_3 122
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 123 && MSGPACK_PP_ITERATION_FINISH_3 >= 123
#        define MSGPACK_PP_ITERATION_3 123
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 124 && MSGPACK_PP_ITERATION_FINISH_3 >= 124
#        define MSGPACK_PP_ITERATION_3 124
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 125 && MSGPACK_PP_ITERATION_FINISH_3 >= 125
#        define MSGPACK_PP_ITERATION_3 125
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 126 && MSGPACK_PP_ITERATION_FINISH_3 >= 126
#        define MSGPACK_PP_ITERATION_3 126
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 127 && MSGPACK_PP_ITERATION_FINISH_3 >= 127
#        define MSGPACK_PP_ITERATION_3 127
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 128 && MSGPACK_PP_ITERATION_FINISH_3 >= 128
#        define MSGPACK_PP_ITERATION_3 128
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 129 && MSGPACK_PP_ITERATION_FINISH_3 >= 129
#        define MSGPACK_PP_ITERATION_3 129
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 130 && MSGPACK_PP_ITERATION_FINISH_3 >= 130
#        define MSGPACK_PP_ITERATION_3 130
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 131 && MSGPACK_PP_ITERATION_FINISH_3 >= 131
#        define MSGPACK_PP_ITERATION_3 131
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 132 && MSGPACK_PP_ITERATION_FINISH_3 >= 132
#        define MSGPACK_PP_ITERATION_3 132
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 133 && MSGPACK_PP_ITERATION_FINISH_3 >= 133
#        define MSGPACK_PP_ITERATION_3 133
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 134 && MSGPACK_PP_ITERATION_FINISH_3 >= 134
#        define MSGPACK_PP_ITERATION_3 134
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 135 && MSGPACK_PP_ITERATION_FINISH_3 >= 135
#        define MSGPACK_PP_ITERATION_3 135
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 136 && MSGPACK_PP_ITERATION_FINISH_3 >= 136
#        define MSGPACK_PP_ITERATION_3 136
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 137 && MSGPACK_PP_ITERATION_FINISH_3 >= 137
#        define MSGPACK_PP_ITERATION_3 137
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 138 && MSGPACK_PP_ITERATION_FINISH_3 >= 138
#        define MSGPACK_PP_ITERATION_3 138
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 139 && MSGPACK_PP_ITERATION_FINISH_3 >= 139
#        define MSGPACK_PP_ITERATION_3 139
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 140 && MSGPACK_PP_ITERATION_FINISH_3 >= 140
#        define MSGPACK_PP_ITERATION_3 140
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 141 && MSGPACK_PP_ITERATION_FINISH_3 >= 141
#        define MSGPACK_PP_ITERATION_3 141
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 142 && MSGPACK_PP_ITERATION_FINISH_3 >= 142
#        define MSGPACK_PP_ITERATION_3 142
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 143 && MSGPACK_PP_ITERATION_FINISH_3 >= 143
#        define MSGPACK_PP_ITERATION_3 143
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 144 && MSGPACK_PP_ITERATION_FINISH_3 >= 144
#        define MSGPACK_PP_ITERATION_3 144
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 145 && MSGPACK_PP_ITERATION_FINISH_3 >= 145
#        define MSGPACK_PP_ITERATION_3 145
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 146 && MSGPACK_PP_ITERATION_FINISH_3 >= 146
#        define MSGPACK_PP_ITERATION_3 146
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 147 && MSGPACK_PP_ITERATION_FINISH_3 >= 147
#        define MSGPACK_PP_ITERATION_3 147
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 148 && MSGPACK_PP_ITERATION_FINISH_3 >= 148
#        define MSGPACK_PP_ITERATION_3 148
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 149 && MSGPACK_PP_ITERATION_FINISH_3 >= 149
#        define MSGPACK_PP_ITERATION_3 149
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 150 && MSGPACK_PP_ITERATION_FINISH_3 >= 150
#        define MSGPACK_PP_ITERATION_3 150
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 151 && MSGPACK_PP_ITERATION_FINISH_3 >= 151
#        define MSGPACK_PP_ITERATION_3 151
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 152 && MSGPACK_PP_ITERATION_FINISH_3 >= 152
#        define MSGPACK_PP_ITERATION_3 152
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 153 && MSGPACK_PP_ITERATION_FINISH_3 >= 153
#        define MSGPACK_PP_ITERATION_3 153
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 154 && MSGPACK_PP_ITERATION_FINISH_3 >= 154
#        define MSGPACK_PP_ITERATION_3 154
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 155 && MSGPACK_PP_ITERATION_FINISH_3 >= 155
#        define MSGPACK_PP_ITERATION_3 155
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 156 && MSGPACK_PP_ITERATION_FINISH_3 >= 156
#        define MSGPACK_PP_ITERATION_3 156
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 157 && MSGPACK_PP_ITERATION_FINISH_3 >= 157
#        define MSGPACK_PP_ITERATION_3 157
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 158 && MSGPACK_PP_ITERATION_FINISH_3 >= 158
#        define MSGPACK_PP_ITERATION_3 158
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 159 && MSGPACK_PP_ITERATION_FINISH_3 >= 159
#        define MSGPACK_PP_ITERATION_3 159
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 160 && MSGPACK_PP_ITERATION_FINISH_3 >= 160
#        define MSGPACK_PP_ITERATION_3 160
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 161 && MSGPACK_PP_ITERATION_FINISH_3 >= 161
#        define MSGPACK_PP_ITERATION_3 161
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 162 && MSGPACK_PP_ITERATION_FINISH_3 >= 162
#        define MSGPACK_PP_ITERATION_3 162
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 163 && MSGPACK_PP_ITERATION_FINISH_3 >= 163
#        define MSGPACK_PP_ITERATION_3 163
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 164 && MSGPACK_PP_ITERATION_FINISH_3 >= 164
#        define MSGPACK_PP_ITERATION_3 164
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 165 && MSGPACK_PP_ITERATION_FINISH_3 >= 165
#        define MSGPACK_PP_ITERATION_3 165
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 166 && MSGPACK_PP_ITERATION_FINISH_3 >= 166
#        define MSGPACK_PP_ITERATION_3 166
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 167 && MSGPACK_PP_ITERATION_FINISH_3 >= 167
#        define MSGPACK_PP_ITERATION_3 167
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 168 && MSGPACK_PP_ITERATION_FINISH_3 >= 168
#        define MSGPACK_PP_ITERATION_3 168
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 169 && MSGPACK_PP_ITERATION_FINISH_3 >= 169
#        define MSGPACK_PP_ITERATION_3 169
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 170 && MSGPACK_PP_ITERATION_FINISH_3 >= 170
#        define MSGPACK_PP_ITERATION_3 170
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 171 && MSGPACK_PP_ITERATION_FINISH_3 >= 171
#        define MSGPACK_PP_ITERATION_3 171
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 172 && MSGPACK_PP_ITERATION_FINISH_3 >= 172
#        define MSGPACK_PP_ITERATION_3 172
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 173 && MSGPACK_PP_ITERATION_FINISH_3 >= 173
#        define MSGPACK_PP_ITERATION_3 173
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 174 && MSGPACK_PP_ITERATION_FINISH_3 >= 174
#        define MSGPACK_PP_ITERATION_3 174
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 175 && MSGPACK_PP_ITERATION_FINISH_3 >= 175
#        define MSGPACK_PP_ITERATION_3 175
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 176 && MSGPACK_PP_ITERATION_FINISH_3 >= 176
#        define MSGPACK_PP_ITERATION_3 176
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 177 && MSGPACK_PP_ITERATION_FINISH_3 >= 177
#        define MSGPACK_PP_ITERATION_3 177
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 178 && MSGPACK_PP_ITERATION_FINISH_3 >= 178
#        define MSGPACK_PP_ITERATION_3 178
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 179 && MSGPACK_PP_ITERATION_FINISH_3 >= 179
#        define MSGPACK_PP_ITERATION_3 179
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 180 && MSGPACK_PP_ITERATION_FINISH_3 >= 180
#        define MSGPACK_PP_ITERATION_3 180
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 181 && MSGPACK_PP_ITERATION_FINISH_3 >= 181
#        define MSGPACK_PP_ITERATION_3 181
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 182 && MSGPACK_PP_ITERATION_FINISH_3 >= 182
#        define MSGPACK_PP_ITERATION_3 182
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 183 && MSGPACK_PP_ITERATION_FINISH_3 >= 183
#        define MSGPACK_PP_ITERATION_3 183
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 184 && MSGPACK_PP_ITERATION_FINISH_3 >= 184
#        define MSGPACK_PP_ITERATION_3 184
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 185 && MSGPACK_PP_ITERATION_FINISH_3 >= 185
#        define MSGPACK_PP_ITERATION_3 185
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 186 && MSGPACK_PP_ITERATION_FINISH_3 >= 186
#        define MSGPACK_PP_ITERATION_3 186
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 187 && MSGPACK_PP_ITERATION_FINISH_3 >= 187
#        define MSGPACK_PP_ITERATION_3 187
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 188 && MSGPACK_PP_ITERATION_FINISH_3 >= 188
#        define MSGPACK_PP_ITERATION_3 188
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 189 && MSGPACK_PP_ITERATION_FINISH_3 >= 189
#        define MSGPACK_PP_ITERATION_3 189
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 190 && MSGPACK_PP_ITERATION_FINISH_3 >= 190
#        define MSGPACK_PP_ITERATION_3 190
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 191 && MSGPACK_PP_ITERATION_FINISH_3 >= 191
#        define MSGPACK_PP_ITERATION_3 191
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 192 && MSGPACK_PP_ITERATION_FINISH_3 >= 192
#        define MSGPACK_PP_ITERATION_3 192
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 193 && MSGPACK_PP_ITERATION_FINISH_3 >= 193
#        define MSGPACK_PP_ITERATION_3 193
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 194 && MSGPACK_PP_ITERATION_FINISH_3 >= 194
#        define MSGPACK_PP_ITERATION_3 194
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 195 && MSGPACK_PP_ITERATION_FINISH_3 >= 195
#        define MSGPACK_PP_ITERATION_3 195
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 196 && MSGPACK_PP_ITERATION_FINISH_3 >= 196
#        define MSGPACK_PP_ITERATION_3 196
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 197 && MSGPACK_PP_ITERATION_FINISH_3 >= 197
#        define MSGPACK_PP_ITERATION_3 197
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 198 && MSGPACK_PP_ITERATION_FINISH_3 >= 198
#        define MSGPACK_PP_ITERATION_3 198
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 199 && MSGPACK_PP_ITERATION_FINISH_3 >= 199
#        define MSGPACK_PP_ITERATION_3 199
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 200 && MSGPACK_PP_ITERATION_FINISH_3 >= 200
#        define MSGPACK_PP_ITERATION_3 200
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 201 && MSGPACK_PP_ITERATION_FINISH_3 >= 201
#        define MSGPACK_PP_ITERATION_3 201
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 202 && MSGPACK_PP_ITERATION_FINISH_3 >= 202
#        define MSGPACK_PP_ITERATION_3 202
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 203 && MSGPACK_PP_ITERATION_FINISH_3 >= 203
#        define MSGPACK_PP_ITERATION_3 203
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 204 && MSGPACK_PP_ITERATION_FINISH_3 >= 204
#        define MSGPACK_PP_ITERATION_3 204
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 205 && MSGPACK_PP_ITERATION_FINISH_3 >= 205
#        define MSGPACK_PP_ITERATION_3 205
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 206 && MSGPACK_PP_ITERATION_FINISH_3 >= 206
#        define MSGPACK_PP_ITERATION_3 206
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 207 && MSGPACK_PP_ITERATION_FINISH_3 >= 207
#        define MSGPACK_PP_ITERATION_3 207
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 208 && MSGPACK_PP_ITERATION_FINISH_3 >= 208
#        define MSGPACK_PP_ITERATION_3 208
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 209 && MSGPACK_PP_ITERATION_FINISH_3 >= 209
#        define MSGPACK_PP_ITERATION_3 209
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 210 && MSGPACK_PP_ITERATION_FINISH_3 >= 210
#        define MSGPACK_PP_ITERATION_3 210
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 211 && MSGPACK_PP_ITERATION_FINISH_3 >= 211
#        define MSGPACK_PP_ITERATION_3 211
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 212 && MSGPACK_PP_ITERATION_FINISH_3 >= 212
#        define MSGPACK_PP_ITERATION_3 212
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 213 && MSGPACK_PP_ITERATION_FINISH_3 >= 213
#        define MSGPACK_PP_ITERATION_3 213
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 214 && MSGPACK_PP_ITERATION_FINISH_3 >= 214
#        define MSGPACK_PP_ITERATION_3 214
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 215 && MSGPACK_PP_ITERATION_FINISH_3 >= 215
#        define MSGPACK_PP_ITERATION_3 215
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 216 && MSGPACK_PP_ITERATION_FINISH_3 >= 216
#        define MSGPACK_PP_ITERATION_3 216
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 217 && MSGPACK_PP_ITERATION_FINISH_3 >= 217
#        define MSGPACK_PP_ITERATION_3 217
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 218 && MSGPACK_PP_ITERATION_FINISH_3 >= 218
#        define MSGPACK_PP_ITERATION_3 218
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 219 && MSGPACK_PP_ITERATION_FINISH_3 >= 219
#        define MSGPACK_PP_ITERATION_3 219
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 220 && MSGPACK_PP_ITERATION_FINISH_3 >= 220
#        define MSGPACK_PP_ITERATION_3 220
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 221 && MSGPACK_PP_ITERATION_FINISH_3 >= 221
#        define MSGPACK_PP_ITERATION_3 221
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 222 && MSGPACK_PP_ITERATION_FINISH_3 >= 222
#        define MSGPACK_PP_ITERATION_3 222
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 223 && MSGPACK_PP_ITERATION_FINISH_3 >= 223
#        define MSGPACK_PP_ITERATION_3 223
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 224 && MSGPACK_PP_ITERATION_FINISH_3 >= 224
#        define MSGPACK_PP_ITERATION_3 224
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 225 && MSGPACK_PP_ITERATION_FINISH_3 >= 225
#        define MSGPACK_PP_ITERATION_3 225
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 226 && MSGPACK_PP_ITERATION_FINISH_3 >= 226
#        define MSGPACK_PP_ITERATION_3 226
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 227 && MSGPACK_PP_ITERATION_FINISH_3 >= 227
#        define MSGPACK_PP_ITERATION_3 227
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 228 && MSGPACK_PP_ITERATION_FINISH_3 >= 228
#        define MSGPACK_PP_ITERATION_3 228
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 229 && MSGPACK_PP_ITERATION_FINISH_3 >= 229
#        define MSGPACK_PP_ITERATION_3 229
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 230 && MSGPACK_PP_ITERATION_FINISH_3 >= 230
#        define MSGPACK_PP_ITERATION_3 230
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 231 && MSGPACK_PP_ITERATION_FINISH_3 >= 231
#        define MSGPACK_PP_ITERATION_3 231
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 232 && MSGPACK_PP_ITERATION_FINISH_3 >= 232
#        define MSGPACK_PP_ITERATION_3 232
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 233 && MSGPACK_PP_ITERATION_FINISH_3 >= 233
#        define MSGPACK_PP_ITERATION_3 233
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 234 && MSGPACK_PP_ITERATION_FINISH_3 >= 234
#        define MSGPACK_PP_ITERATION_3 234
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 235 && MSGPACK_PP_ITERATION_FINISH_3 >= 235
#        define MSGPACK_PP_ITERATION_3 235
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 236 && MSGPACK_PP_ITERATION_FINISH_3 >= 236
#        define MSGPACK_PP_ITERATION_3 236
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 237 && MSGPACK_PP_ITERATION_FINISH_3 >= 237
#        define MSGPACK_PP_ITERATION_3 237
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 238 && MSGPACK_PP_ITERATION_FINISH_3 >= 238
#        define MSGPACK_PP_ITERATION_3 238
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 239 && MSGPACK_PP_ITERATION_FINISH_3 >= 239
#        define MSGPACK_PP_ITERATION_3 239
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 240 && MSGPACK_PP_ITERATION_FINISH_3 >= 240
#        define MSGPACK_PP_ITERATION_3 240
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 241 && MSGPACK_PP_ITERATION_FINISH_3 >= 241
#        define MSGPACK_PP_ITERATION_3 241
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 242 && MSGPACK_PP_ITERATION_FINISH_3 >= 242
#        define MSGPACK_PP_ITERATION_3 242
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 243 && MSGPACK_PP_ITERATION_FINISH_3 >= 243
#        define MSGPACK_PP_ITERATION_3 243
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 244 && MSGPACK_PP_ITERATION_FINISH_3 >= 244
#        define MSGPACK_PP_ITERATION_3 244
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 245 && MSGPACK_PP_ITERATION_FINISH_3 >= 245
#        define MSGPACK_PP_ITERATION_3 245
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 246 && MSGPACK_PP_ITERATION_FINISH_3 >= 246
#        define MSGPACK_PP_ITERATION_3 246
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 247 && MSGPACK_PP_ITERATION_FINISH_3 >= 247
#        define MSGPACK_PP_ITERATION_3 247
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 248 && MSGPACK_PP_ITERATION_FINISH_3 >= 248
#        define MSGPACK_PP_ITERATION_3 248
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 249 && MSGPACK_PP_ITERATION_FINISH_3 >= 249
#        define MSGPACK_PP_ITERATION_3 249
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 250 && MSGPACK_PP_ITERATION_FINISH_3 >= 250
#        define MSGPACK_PP_ITERATION_3 250
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 251 && MSGPACK_PP_ITERATION_FINISH_3 >= 251
#        define MSGPACK_PP_ITERATION_3 251
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 252 && MSGPACK_PP_ITERATION_FINISH_3 >= 252
#        define MSGPACK_PP_ITERATION_3 252
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 253 && MSGPACK_PP_ITERATION_FINISH_3 >= 253
#        define MSGPACK_PP_ITERATION_3 253
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 254 && MSGPACK_PP_ITERATION_FINISH_3 >= 254
#        define MSGPACK_PP_ITERATION_3 254
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 255 && MSGPACK_PP_ITERATION_FINISH_3 >= 255
#        define MSGPACK_PP_ITERATION_3 255
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
#    if MSGPACK_PP_ITERATION_START_3 <= 256 && MSGPACK_PP_ITERATION_FINISH_3 >= 256
#        define MSGPACK_PP_ITERATION_3 256
#        include MSGPACK_PP_FILENAME_3
#        undef MSGPACK_PP_ITERATION_3
#    endif
# endif
#
# undef MSGPACK_PP_ITERATION_DEPTH
# define MSGPACK_PP_ITERATION_DEPTH() 2
#
# undef MSGPACK_PP_ITERATION_START_3
# undef MSGPACK_PP_ITERATION_FINISH_3
# undef MSGPACK_PP_FILENAME_3
#
# undef MSGPACK_PP_ITERATION_FLAGS_3
# undef MSGPACK_PP_ITERATION_PARAMS_3
