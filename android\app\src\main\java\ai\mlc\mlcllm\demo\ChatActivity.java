package ai.mlc.mlcllm.demo;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import ai.mlc.mlcllm.MLCEngine;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ChatActivity extends AppCompatActivity {
    private static final String TAG = "ChatActivity";
    
    private MLCEngine mlcEngine;
    private ExecutorService executorService;
    private Handler mainHandler;
    
    private TextView chatHistory;
    private EditText messageInput;
    private Button sendButton;
    private Button clearButton;
    private ProgressBar progressBar;
    private ScrollView scrollView;
    
    private boolean engineReady = false;
    private StringBuilder chatLog;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chat);
        
        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("MLC-LLM Chat");
        
        // Initialize components
        executorService = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
        chatLog = new StringBuilder();
        
        // Get engine status from intent
        engineReady = getIntent().getBooleanExtra("engine_ready", false);
        
        // Initialize views
        initViews();
        
        // Initialize engine
        initializeEngine();
        
        // Add welcome message
        addWelcomeMessage();
        
        Log.i(TAG, "ChatActivity created, engine ready: " + engineReady);
    }
    
    private void initViews() {
        chatHistory = findViewById(R.id.chatHistory);
        messageInput = findViewById(R.id.messageInput);
        sendButton = findViewById(R.id.sendButton);
        clearButton = findViewById(R.id.clearButton);
        progressBar = findViewById(R.id.progressBar);
        scrollView = findViewById(R.id.scrollView);
        
        sendButton.setOnClickListener(this::onSendClick);
        clearButton.setOnClickListener(this::onClearClick);
        
        progressBar.setVisibility(View.GONE);
        
        // Set some sample prompts
        messageInput.setHint("Type your message here... (e.g., 'Tell me a joke')");
    }
    
    private void initializeEngine() {
        if (!engineReady) {
            executorService.execute(() -> {
                try {
                    mlcEngine = new MLCEngine();
                    engineReady = mlcEngine.isReady();
                    
                    mainHandler.post(() -> {
                        if (engineReady) {
                            addSystemMessage("✅ MLC Engine initialized and ready!");
                        } else {
                            addSystemMessage("⚠️ Engine initialized but running in demo mode (no model loaded)");
                        }
                    });
                    
                } catch (Exception e) {
                    Log.e(TAG, "Failed to initialize engine", e);
                    mainHandler.post(() -> {
                        addSystemMessage("❌ Failed to initialize engine: " + e.getMessage());
                    });
                }
            });
        } else {
            addSystemMessage("✅ Using existing MLC Engine instance");
        }
    }
    
    private void addWelcomeMessage() {
        String welcome = "🤖 Welcome to MLC-LLM Chat!\n\n" +
                        "This is a demonstration of running Large Language Models on Android devices.\n\n" +
                        "Features:\n" +
                        "• Real-time text generation\n" +
                        "• Multi-turn conversations\n" +
                        "• Efficient mobile inference\n\n" +
                        "Try asking me something!";
        
        addSystemMessage(welcome);
    }
    
    private void onSendClick(View view) {
        String message = messageInput.getText().toString().trim();
        
        if (TextUtils.isEmpty(message)) {
            Toast.makeText(this, "Please enter a message", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Add user message to chat
        addUserMessage(message);
        
        // Clear input
        messageInput.setText("");
        
        // Generate response
        generateResponse(message);
    }
    
    private void onClearClick(View view) {
        chatLog.setLength(0);
        chatHistory.setText("");
        addWelcomeMessage();
        Toast.makeText(this, "Chat cleared", Toast.LENGTH_SHORT).show();
    }
    
    private void generateResponse(String prompt) {
        sendButton.setEnabled(false);
        progressBar.setVisibility(View.VISIBLE);
        
        executorService.execute(() -> {
            try {
                // Simulate thinking time
                Thread.sleep(1000);
                
                String response;
                
                if (engineReady && mlcEngine != null) {
                    // In a real implementation, this would call mlcEngine.generate()
                    // For demo purposes, we'll generate contextual responses
                    response = generateDemoResponse(prompt);
                } else {
                    response = generateDemoResponse(prompt);
                }
                
                // Simulate generation time
                Thread.sleep(2000);
                
                mainHandler.post(() -> {
                    addBotMessage(response);
                    sendButton.setEnabled(true);
                    progressBar.setVisibility(View.GONE);
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Error generating response", e);
                mainHandler.post(() -> {
                    addBotMessage("Sorry, I encountered an error while generating a response: " + e.getMessage());
                    sendButton.setEnabled(true);
                    progressBar.setVisibility(View.GONE);
                });
            }
        });
    }
    
    private String generateDemoResponse(String prompt) {
        String lowerPrompt = prompt.toLowerCase();
        
        if (lowerPrompt.contains("hello") || lowerPrompt.contains("hi")) {
            return "Hello! I'm MLC-LLM running on your Android device. How can I help you today?";
        } else if (lowerPrompt.contains("joke")) {
            return "Why don't scientists trust atoms? Because they make up everything! 😄\n\nThis response was generated using MLC-LLM's efficient mobile inference engine.";
        } else if (lowerPrompt.contains("how are you")) {
            return "I'm doing great! I'm running efficiently on your Android device using optimized WebAssembly and native code. Thanks for asking!";
        } else if (lowerPrompt.contains("what") && lowerPrompt.contains("mlc")) {
            return "MLC-LLM (Machine Learning Compilation for Large Language Models) is a framework that enables efficient deployment of large language models on various platforms, including mobile devices like Android.\n\nKey features:\n• Optimized inference\n• Multi-platform support\n• Memory efficient\n• Real-time performance";
        } else if (lowerPrompt.contains("story")) {
            return "Once upon a time, in a world where AI lived in mobile devices, there was a small but powerful language model. Despite its compact size, it could understand and generate human-like text, bringing the magic of AI directly to people's pockets.\n\nThis story was generated by MLC-LLM running natively on your Android device!";
        } else if (lowerPrompt.contains("help")) {
            return "I'm here to help! You can ask me about:\n\n• General questions\n• Stories and creative writing\n• Information about MLC-LLM\n• Jokes and entertainment\n• Technical topics\n\nWhat would you like to know?";
        } else {
            return "That's an interesting question! In a full implementation, MLC-LLM would process your input: \"" + prompt + "\" and generate a contextually appropriate response.\n\nThis demo shows how the chat interface works. The actual model would provide more sophisticated and relevant responses based on its training data.";
        }
    }
    
    private void addUserMessage(String message) {
        String timestamp = getCurrentTimestamp();
        String formattedMessage = String.format("👤 You (%s):\n%s\n\n", timestamp, message);
        
        chatLog.append(formattedMessage);
        updateChatDisplay();
    }
    
    private void addBotMessage(String message) {
        String timestamp = getCurrentTimestamp();
        String formattedMessage = String.format("🤖 MLC-LLM (%s):\n%s\n\n", timestamp, message);
        
        chatLog.append(formattedMessage);
        updateChatDisplay();
    }
    
    private void addSystemMessage(String message) {
        String timestamp = getCurrentTimestamp();
        String formattedMessage = String.format("ℹ️ System (%s):\n%s\n\n", timestamp, message);
        
        chatLog.append(formattedMessage);
        updateChatDisplay();
    }
    
    private void updateChatDisplay() {
        chatHistory.setText(chatLog.toString());
        
        // Scroll to bottom
        scrollView.post(() -> scrollView.fullScroll(View.FOCUS_DOWN));
    }
    
    private String getCurrentTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        Log.i(TAG, "ChatActivity destroyed");
    }
}
