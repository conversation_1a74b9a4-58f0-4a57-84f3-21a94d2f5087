# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_ITERATION_SELF_HPP
# define MSGPACK_PREPROCESSOR_ITERATION_SELF_HPP
#
# /* MSGPACK_PP_INCLUDE_SELF */
#
# define MSGPACK_PP_INCLUDE_SELF() <msgpack/preprocessor/iteration/detail/self.hpp>
#
# endif
