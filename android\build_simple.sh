#!/bin/bash

echo "🚀 Building MLC-LLM Android APK..."
echo "================================"

# Set environment variables
export ANDROID_HOME="/c/Android/Sdk"
export JAVA_HOME="/c/Program Files/Microsoft/jdk-17.0.15.6-hotspot"

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "❌ Java not found! Please install Java JDK 8+"
    echo "Download from: https://adoptium.net/"
    exit 1
fi

echo "✅ Java found"
java -version

# Make gradlew executable
chmod +x gradlew.bat

echo ""
echo "🔨 Starting Gradle build..."
echo ""

# Clean first
echo "🧹 Cleaning project..."
./gradlew.bat clean
if [ $? -ne 0 ]; then
    echo "⚠️ Clean failed, continuing anyway..."
fi

# Build debug APK
echo "🔨 Building debug APK..."
./gradlew.bat assembleDebug
if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    echo ""
    echo "💡 Possible solutions:"
    echo "1. Install Android Studio"
    echo "2. Set ANDROID_HOME environment variable"
    echo "3. Install Android SDK"
    echo "4. Check internet connection for dependencies"
    exit 1
fi

# Check if APK was created
if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
    echo ""
    echo "🎉 APK built successfully!"
    echo "📁 Location: app/build/outputs/apk/debug/app-debug.apk"
    
    # Get file size
    size=$(stat -c%s "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null || stat -f%z "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null || echo "unknown")
    echo "📊 Size: $size bytes"
    
    # Copy to root directory
    cp "app/build/outputs/apk/debug/app-debug.apk" "../mlc-llm-demo.apk"
    if [ $? -eq 0 ]; then
        echo "✅ APK copied to: mlc-llm-demo.apk"
    fi
    
    echo ""
    echo "📱 Installation Instructions:"
    echo "1. Enable 'Unknown Sources' in Android Settings"
    echo "2. Transfer APK to your Android device"
    echo "3. Tap the APK file to install"
    echo "4. Launch 'MLC-LLM Demo' app"
    
else
    echo "❌ APK file not found!"
    echo "Expected: app/build/outputs/apk/debug/app-debug.apk"
fi

echo ""
echo "Build process completed."
