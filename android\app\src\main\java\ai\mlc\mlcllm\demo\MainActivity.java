package ai.mlc.mlcllm.demo;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import ai.mlc.mlcllm.MLCEngine;
import ai.mlc.mlcllm.MLCUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    
    private MLCEngine mlcEngine;
    private ExecutorService executorService;
    private Handler mainHandler;
    
    private TextView statusText;
    private TextView systemInfoText;
    private Button initEngineButton;
    private Button chatButton;
    private Button settingsButton;
    private ProgressBar progressBar;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        
        // Initialize components
        executorService = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
        
        // Initialize views
        initViews();
        
        // Display system information
        displaySystemInfo();
        
        Log.i(TAG, "MainActivity created");
    }
    
    private void initViews() {
        statusText = findViewById(R.id.statusText);
        systemInfoText = findViewById(R.id.systemInfoText);
        initEngineButton = findViewById(R.id.initEngineButton);
        chatButton = findViewById(R.id.chatButton);
        settingsButton = findViewById(R.id.settingsButton);
        progressBar = findViewById(R.id.progressBar);
        
        initEngineButton.setOnClickListener(this::onInitEngineClick);
        chatButton.setOnClickListener(this::onChatClick);
        settingsButton.setOnClickListener(this::onSettingsClick);
        
        // Initially disable chat button
        chatButton.setEnabled(false);
        progressBar.setVisibility(View.GONE);
    }
    
    private void displaySystemInfo() {
        String systemInfo = MLCUtils.getSystemInfo();
        systemInfo += "\nAvailable Memory: " + MLCUtils.formatBytes(MLCUtils.getAvailableMemory(this));
        systemInfo += "\nMLC-LLM Version: 1.0.0";
        
        systemInfoText.setText(systemInfo);
        Log.d(TAG, "System info displayed");
    }
    
    private void onInitEngineClick(View view) {
        initEngineButton.setEnabled(false);
        progressBar.setVisibility(View.VISIBLE);
        statusText.setText("Initializing MLC Engine...");
        statusText.setTextColor(getColor(android.R.color.holo_orange_dark));
        
        executorService.execute(() -> {
            try {
                Log.i(TAG, "Starting engine initialization");
                
                // Simulate initialization time
                Thread.sleep(2000);
                
                mlcEngine = new MLCEngine();
                
                // Simulate model loading
                Thread.sleep(3000);
                
                mainHandler.post(() -> {
                    progressBar.setVisibility(View.GONE);
                    
                    if (mlcEngine != null && mlcEngine.isReady()) {
                        statusText.setText("✅ MLC Engine initialized successfully!\nReady for text generation.");
                        statusText.setTextColor(getColor(android.R.color.holo_green_dark));
                        chatButton.setEnabled(true);
                        initEngineButton.setText("Engine Ready");
                        Toast.makeText(this, "Engine ready!", Toast.LENGTH_SHORT).show();
                    } else {
                        statusText.setText("⚠️ Engine initialized but no model loaded.\nYou can still test the interface.");
                        statusText.setTextColor(getColor(android.R.color.holo_orange_dark));
                        chatButton.setEnabled(true);
                        initEngineButton.setText("Retry");
                        initEngineButton.setEnabled(true);
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Engine initialization failed", e);
                mainHandler.post(() -> {
                    progressBar.setVisibility(View.GONE);
                    statusText.setText("❌ Failed to initialize engine: " + e.getMessage());
                    statusText.setTextColor(getColor(android.R.color.holo_red_dark));
                    initEngineButton.setText("Retry");
                    initEngineButton.setEnabled(true);
                    Toast.makeText(this, "Initialization failed", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void onChatClick(View view) {
        Intent intent = new Intent(this, ChatActivity.class);
        
        // Pass engine status to chat activity
        intent.putExtra("engine_ready", mlcEngine != null && mlcEngine.isReady());
        
        startActivity(intent);
    }
    
    private void onSettingsClick(View view) {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == R.id.action_about) {
            showAboutDialog();
            return true;
        } else if (id == R.id.action_refresh) {
            displaySystemInfo();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    private void showAboutDialog() {
        androidx.appcompat.app.AlertDialog.Builder builder = 
            new androidx.appcompat.app.AlertDialog.Builder(this);
        
        builder.setTitle("About MLC-LLM Demo")
               .setMessage("MLC-LLM Android Demo Application\n\n" +
                          "Version: 1.0.0\n" +
                          "Build: 2025-06-22\n\n" +
                          "This app demonstrates running Large Language Models " +
                          "on Android devices using MLC-LLM technology.\n\n" +
                          "Features:\n" +
                          "• Multi-architecture support\n" +
                          "• Efficient memory management\n" +
                          "• Real-time text generation\n" +
                          "• Native performance")
               .setPositiveButton("OK", null)
               .show();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        if (mlcEngine != null) {
            mlcEngine.release();
            Log.i(TAG, "MLC Engine released");
        }
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        Log.i(TAG, "MainActivity destroyed");
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // Update status when returning from other activities
        if (mlcEngine != null && mlcEngine.isReady()) {
            statusText.setText("✅ MLC Engine is ready");
            statusText.setTextColor(getColor(android.R.color.holo_green_dark));
        }
    }
}
