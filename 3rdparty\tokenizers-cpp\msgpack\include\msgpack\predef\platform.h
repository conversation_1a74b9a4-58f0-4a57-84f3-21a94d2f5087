/*
Copyright <PERSON> 2013-2015
Copyright (c) Microsoft Corporation 2014
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(MSGPACK_PREDEF_PLATFORM_H) || defined(MSGPACK_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef MSGPACK_PREDEF_PLATFORM_H
#define MSGPACK_PREDEF_PLATFORM_H
#endif

#include <msgpack/predef/platform/cloudabi.h>
#include <msgpack/predef/platform/mingw.h>
#include <msgpack/predef/platform/mingw32.h>
#include <msgpack/predef/platform/mingw64.h>
#include <msgpack/predef/platform/windows_uwp.h>
#include <msgpack/predef/platform/windows_desktop.h>
#include <msgpack/predef/platform/windows_phone.h>
#include <msgpack/predef/platform/windows_server.h>
#include <msgpack/predef/platform/windows_store.h>
#include <msgpack/predef/platform/windows_system.h>
#include <msgpack/predef/platform/windows_runtime.h> // deprecated
#include <msgpack/predef/platform/ios.h>
/*#include <msgpack/predef/platform/.h>*/

#endif
