# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_ARITHMETIC_HPP
# define MSGPACK_PREPROCESSOR_ARITHMETIC_HPP
#
# include <msgpack/preprocessor/arithmetic/add.hpp>
# include <msgpack/preprocessor/arithmetic/dec.hpp>
# include <msgpack/preprocessor/arithmetic/div.hpp>
# include <msgpack/preprocessor/arithmetic/inc.hpp>
# include <msgpack/preprocessor/arithmetic/mod.hpp>
# include <msgpack/preprocessor/arithmetic/mul.hpp>
# include <msgpack/preprocessor/arithmetic/sub.hpp>
#
# endif
