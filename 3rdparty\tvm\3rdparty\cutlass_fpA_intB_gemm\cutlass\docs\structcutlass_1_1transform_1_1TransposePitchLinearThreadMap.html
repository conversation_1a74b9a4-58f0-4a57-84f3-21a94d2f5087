<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::TransposePitchLinearThreadMap&lt; ThreadMap_, WarpThreadArrangement_ &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">TransposePitchLinearThreadMap</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::TransposePitchLinearThreadMap&lt; ThreadMap_, WarpThreadArrangement_ &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap_1_1Detail.html">Detail</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal details made public to facilitate introspection Iterations along each dimension (concept: PitchLinearShape)  <a href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap_1_1Detail.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a43ccdad16d5fca090703703b9a6fc171"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a43ccdad16d5fca090703703b9a6fc171">ThreadMap</a> = ThreadMap_</td></tr>
<tr class="memdesc:a43ccdad16d5fca090703703b9a6fc171"><td class="mdescLeft">&#160;</td><td class="mdescRight">Underlying ThreadMap.  <a href="#a43ccdad16d5fca090703703b9a6fc171">More...</a><br /></td></tr>
<tr class="separator:a43ccdad16d5fca090703703b9a6fc171"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ac42991f38b20fa0cc2d72ea69d131d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a0ac42991f38b20fa0cc2d72ea69d131d">TensorCoord</a> = typename ThreadMap::TensorCoord</td></tr>
<tr class="memdesc:a0ac42991f38b20fa0cc2d72ea69d131d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor coordinate.  <a href="#a0ac42991f38b20fa0cc2d72ea69d131d">More...</a><br /></td></tr>
<tr class="separator:a0ac42991f38b20fa0cc2d72ea69d131d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60339dda02fad5f1e659d91b0264a860"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a60339dda02fad5f1e659d91b0264a860">Shape</a> = typename ThreadMap::Shape</td></tr>
<tr class="memdesc:a60339dda02fad5f1e659d91b0264a860"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile shape.  <a href="#a60339dda02fad5f1e659d91b0264a860">More...</a><br /></td></tr>
<tr class="separator:a60339dda02fad5f1e659d91b0264a860"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae080af127f99904180ffd3e7ef6db05d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#ae080af127f99904180ffd3e7ef6db05d">ThreadAccessShape</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a9c5c6785a5d4bb21d93b3a3e18692235">kElementsPerAccess</a>, 1 &gt;</td></tr>
<tr class="memdesc:ae080af127f99904180ffd3e7ef6db05d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shape of access by each thread.  <a href="#ae080af127f99904180ffd3e7ef6db05d">More...</a><br /></td></tr>
<tr class="separator:ae080af127f99904180ffd3e7ef6db05d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaff78ad7b55c3043f65db8072489da43"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#aaff78ad7b55c3043f65db8072489da43">Iterations</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; ThreadMap::Iterations::kStrided, ThreadMap::Iterations::kContiguous &gt;</td></tr>
<tr class="separator:aaff78ad7b55c3043f65db8072489da43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfa87c73e85a088159450b488e0311d6"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#adfa87c73e85a088159450b488e0311d6">Delta</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Detail::WarpThreadArrangement::kContiguous *<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a9c5c6785a5d4bb21d93b3a3e18692235">kElementsPerAccess</a>, Detail::WarpThreadArrangement::kStrided &gt;</td></tr>
<tr class="memdesc:adfa87c73e85a088159450b488e0311d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Delta betweeen accesses (units of elements, concept: PitchLinearShape)  <a href="#adfa87c73e85a088159450b488e0311d6">More...</a><br /></td></tr>
<tr class="separator:adfa87c73e85a088159450b488e0311d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a1efbb1ee0b34e0d258fc74c201d9ee02"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a0ac42991f38b20fa0cc2d72ea69d131d">TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a1efbb1ee0b34e0d258fc74c201d9ee02">initial_offset</a> (int thread_id)</td></tr>
<tr class="separator:a1efbb1ee0b34e0d258fc74c201d9ee02"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:acec5d21914f954b2dc12d8db0273e9ee"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#acec5d21914f954b2dc12d8db0273e9ee">kThreads</a> = ThreadMap::kThreads</td></tr>
<tr class="memdesc:acec5d21914f954b2dc12d8db0273e9ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads total.  <a href="#acec5d21914f954b2dc12d8db0273e9ee">More...</a><br /></td></tr>
<tr class="separator:acec5d21914f954b2dc12d8db0273e9ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c5c6785a5d4bb21d93b3a3e18692235"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a9c5c6785a5d4bb21d93b3a3e18692235">kElementsPerAccess</a> = ThreadMap::kElementsPerAccess</td></tr>
<tr class="memdesc:a9c5c6785a5d4bb21d93b3a3e18692235"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract vector length from Layout.  <a href="#a9c5c6785a5d4bb21d93b3a3e18692235">More...</a><br /></td></tr>
<tr class="separator:a9c5c6785a5d4bb21d93b3a3e18692235"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename ThreadMap_, typename WarpThreadArrangement_&gt;<br />
struct cutlass::transform::TransposePitchLinearThreadMap&lt; ThreadMap_, WarpThreadArrangement_ &gt;</h3>

<p>Transpose the existing ThreadMap. For example, interleaved layout is like congruous in the global memory and crosswise in the shared memory. We need to transpose the coordinates between two. </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="adfa87c73e85a088159450b488e0311d6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#adfa87c73e85a088159450b488e0311d6">Delta</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;Detail::WarpThreadArrangement::kContiguous * <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a9c5c6785a5d4bb21d93b3a3e18692235">kElementsPerAccess</a>, Detail::WarpThreadArrangement::kStrided&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aaff78ad7b55c3043f65db8072489da43"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#aaff78ad7b55c3043f65db8072489da43">Iterations</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;ThreadMap::Iterations::kStrided, ThreadMap::Iterations::kContiguous&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a60339dda02fad5f1e659d91b0264a860"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a60339dda02fad5f1e659d91b0264a860">Shape</a> =  typename ThreadMap::Shape</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0ac42991f38b20fa0cc2d72ea69d131d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a0ac42991f38b20fa0cc2d72ea69d131d">TensorCoord</a> =  typename ThreadMap::TensorCoord</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae080af127f99904180ffd3e7ef6db05d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#ae080af127f99904180ffd3e7ef6db05d">ThreadAccessShape</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a9c5c6785a5d4bb21d93b3a3e18692235">kElementsPerAccess</a>, 1&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a43ccdad16d5fca090703703b9a6fc171"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a43ccdad16d5fca090703703b9a6fc171">ThreadMap</a> =  ThreadMap_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a1efbb1ee0b34e0d258fc74c201d9ee02"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html#a0ac42991f38b20fa0cc2d72ea69d131d">TensorCoord</a> <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::initial_offset </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>thread_id</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Maps thread ID to a coordinate offset within the tensor's logical coordinate space Note this is slightly different from the one of <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">PitchLinearWarpRakedThreadMap</a>. </p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a9c5c6785a5d4bb21d93b3a3e18692235"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::kElementsPerAccess = ThreadMap::kElementsPerAccess</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acec5d21914f954b2dc12d8db0273e9ee"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ , typename WarpThreadArrangement_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap.html">cutlass::transform::TransposePitchLinearThreadMap</a>&lt; ThreadMap_, WarpThreadArrangement_ &gt;::kThreads = ThreadMap::kThreads</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
