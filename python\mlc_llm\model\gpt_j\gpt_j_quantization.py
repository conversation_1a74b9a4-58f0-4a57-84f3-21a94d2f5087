"""This file specifies how MLC's GPTJ parameters are quantized using group quantization
or other formats."""

from typing import <PERSON>ple

from tvm.relax.frontend import nn

from mlc_llm.loader import QuantizeMapping
from mlc_llm.quantization import FTQuantize, GroupQuantize, NoQuantize

from .gpt_j_model import GPT<PERSON><PERSON>onfig, GPTJForCausalLM


def group_quant(
    model_config: GPTJConfig,
    quantization: GroupQuantize,
) -> <PERSON><PERSON>[nn.Modu<PERSON>, QuantizeMapping]:
    """Quantize a gptjLM-architecture model using group quantization."""
    model: nn.Module = GPTJForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def ft_quant(
    model_config: GPTJConfig,
    quantization: FTQuantize,
) -> <PERSON><PERSON>[nn.<PERSON><PERSON><PERSON>, QuantizeMapping]:
    """Quantize a GPTNeoX-architecture model using FasterTransformer quantization."""
    model: nn.Module = GPTJForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    model = quantization.quantize_model(
        model,
        quant_map,
        "",
    )
    return model, quant_map


def no_quant(
    model_config: GPTJConfig,
    quantization: NoQuantize,
) -> Tuple[nn.Module, QuantizeMapping]:
    """Quantize a gptjLM model without quantization."""
    model: nn.Module = GPTJForCausalLM(model_config)
    model.to(quantization.model_dtype)
    quant_map = QuantizeMapping({}, {})
    return model, quant_map
