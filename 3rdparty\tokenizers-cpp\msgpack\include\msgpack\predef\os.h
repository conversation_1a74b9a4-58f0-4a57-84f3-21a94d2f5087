/*
Copyright <PERSON> 2008-2015
Copyright Franz Detro 2014
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(MSGPACK_PREDEF_OS_H) || defined(MSGPACK_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef MSGPACK_PREDEF_OS_H
#define MSGPACK_PREDEF_OS_H
#endif

#include <msgpack/predef/os/aix.h>
#include <msgpack/predef/os/amigaos.h>
#include <msgpack/predef/os/android.h>
#include <msgpack/predef/os/beos.h>
#include <msgpack/predef/os/bsd.h>
#include <msgpack/predef/os/cygwin.h>
#include <msgpack/predef/os/haiku.h>
#include <msgpack/predef/os/hpux.h>
#include <msgpack/predef/os/irix.h>
#include <msgpack/predef/os/ios.h>
#include <msgpack/predef/os/linux.h>
#include <msgpack/predef/os/macos.h>
#include <msgpack/predef/os/os400.h>
#include <msgpack/predef/os/qnxnto.h>
#include <msgpack/predef/os/solaris.h>
#include <msgpack/predef/os/unix.h>
#include <msgpack/predef/os/vms.h>
#include <msgpack/predef/os/windows.h>

#endif
