<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1Distribution.html">Distribution</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">cutlass::Distribution Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">delta</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a40f0b9d0f92199f8a49c931d34dd8c8a">Distribution</a>()</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5">Gaussian</a> enum value</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">gaussian</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6">Identity</a> enum value</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a676b1d8b87691b4218f6ed308e6adfc1">int_scale</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa2ff14122c59a823654b84764f68e597b">Invalid</a> enum value</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">Kind</a> enum name</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a07cb089b346ef06e198f6043128264fb">kind</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a824641fd3addfa360999614970adfac0">max</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a776df53c7ad1b7de983c9f9d17d7438c">mean</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a846430e3a21ed25c779fc6e714bc1bcc">min</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc">Sequential</a> enum value</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#ab86d975567ef141ff82067b1f41cd3ee">sequential</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#ad594b5ec1d577e8ef03d4d808a8220b1">set_gaussian</a>(double _mean, double _stddev, int _int_scale=0)</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#aad2cf02af3d520544d89843cc4295858">set_identity</a>()</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a7fb9689c8ae17d5c72c7d0376fa93767">set_sequential</a>(double start, double delta, int _int_scale=0)</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a5ef87d3af6af0a815a56e74645f32991">set_uniform</a>(double _min, double _max, int _int_scale=0)</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">start</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#aee3bd32372426422bb02b335704965aa">stddev</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6">Uniform</a> enum value</td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">uniform</a></td><td class="entry"><a class="el" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
