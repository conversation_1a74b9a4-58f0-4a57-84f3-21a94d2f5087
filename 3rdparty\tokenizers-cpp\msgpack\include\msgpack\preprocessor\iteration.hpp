# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_ITERATION_HPP
# define MSGPACK_PREPROCESSOR_ITERATION_HPP
#
# include <msgpack/preprocessor/iteration/iterate.hpp>
# include <msgpack/preprocessor/iteration/local.hpp>
# include <msgpack/preprocessor/iteration/self.hpp>
#
# endif
