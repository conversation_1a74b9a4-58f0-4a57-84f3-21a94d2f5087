<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:title="MLC-LLM Demo" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- App Title -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🤖 MLC-LLM Android Demo"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/colorPrimary"
                android:gravity="center"
                android:layout_marginBottom="24dp" />

            <!-- Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Engine Status"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/statusText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Ready to initialize MLC Engine..."
                        android:textSize="14sp"
                        android:textColor="@android:color/darker_gray" />

                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:indeterminate="true" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <Button
                    android:id="@+id/initEngineButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Initialize MLC Engine"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp"
                    android:backgroundTint="@color/colorPrimary" />

                <Button
                    android:id="@+id/chatButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Start Chat"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp"
                    android:backgroundTint="@color/colorAccent" />

                <Button
                    android:id="@+id/settingsButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Settings"
                    android:textSize="16sp"
                    android:backgroundTint="@android:color/darker_gray" />

            </LinearLayout>

            <!-- System Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="System Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/systemInfoText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Loading system information..."
                        android:textSize="12sp"
                        android:textColor="@android:color/darker_gray"
                        android:fontFamily="monospace" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
