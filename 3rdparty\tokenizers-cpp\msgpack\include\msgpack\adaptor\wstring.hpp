//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2018 <PERSON><PERSON><PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_TYPE_WSTRING_HPP
#define MSGPACK_TYPE_WSTRING_HPP

#include "msgpack/v1/adaptor/wstring.hpp"

#endif // MSGPACK_TYPE_WSTRING_HPP
