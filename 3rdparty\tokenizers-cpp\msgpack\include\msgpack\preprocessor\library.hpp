# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002-2011.                             *
#  *     (C) Copyright <PERSON> 2011.                                    *
#  *     Distributed under the Boost Software License, Version 1.0. (See      *
#  *     accompanying file LICENSE_1_0.txt or copy at                         *
#  *     http://www.boost.org/LICENSE_1_0.txt)                                *
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_LIBRARY_HPP
# define MSGPACK_PREPROCESSOR_LIBRARY_HPP
#
# include <msgpack/preprocessor/arithmetic.hpp>
# include <msgpack/preprocessor/array.hpp>
# include <msgpack/preprocessor/cat.hpp>
# include <msgpack/preprocessor/comparison.hpp>
# include <msgpack/preprocessor/config/limits.hpp>
# include <msgpack/preprocessor/control.hpp>
# include <msgpack/preprocessor/debug.hpp>
# include <msgpack/preprocessor/facilities.hpp>
# include <msgpack/preprocessor/iteration.hpp>
# include <msgpack/preprocessor/list.hpp>
# include <msgpack/preprocessor/logical.hpp>
# include <msgpack/preprocessor/punctuation.hpp>
# include <msgpack/preprocessor/repetition.hpp>
# include <msgpack/preprocessor/selection.hpp>
# include <msgpack/preprocessor/seq.hpp>
# include <msgpack/preprocessor/slot.hpp>
# include <msgpack/preprocessor/stringize.hpp>
# include <msgpack/preprocessor/tuple.hpp>
# include <msgpack/preprocessor/variadic.hpp>
# include <msgpack/preprocessor/wstringize.hpp>
#
# endif
