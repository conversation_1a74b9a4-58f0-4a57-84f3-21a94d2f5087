//
// MessagePack for C++ deflate buffer implementation
//
// Copyright (C) 2016 KOND<PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_ZBUFFER_DECL_HPP
#define MSGPACK_ZBUFFER_DECL_HPP

#include "msgpack/v1/zbuffer_decl.hpp"
#include "msgpack/v2/zbuffer_decl.hpp"
#include "msgpack/v3/zbuffer_decl.hpp"

#endif // MSGPACK_ZBUFFER_DECL_HPP
