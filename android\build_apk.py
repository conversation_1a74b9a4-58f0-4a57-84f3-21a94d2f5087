#!/usr/bin/env python3
"""
Build MLC-LLM Android APK
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def setup_gradle_wrapper():
    """Setup Gradle wrapper if not exists"""
    
    print("🔧 Setting up Gradle wrapper...")
    
    gradle_wrapper_dir = Path("android/gradle/wrapper")
    gradle_wrapper_dir.mkdir(parents=True, exist_ok=True)
    
    # Create gradle-wrapper.properties
    wrapper_props = gradle_wrapper_dir / "gradle-wrapper.properties"
    with open(wrapper_props, 'w') as f:
        f.write("""distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\\://services.gradle.org/distributions/gradle-8.0-bin.zip
networkTimeout=10000
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
""")
    
    print(f"✅ Created: {wrapper_props}")
    
    # Create gradlew script
    gradlew_script = Path("android/gradlew")
    with open(gradlew_script, 'w') as f:
        f.write("""#!/bin/sh

# Gradle start up script for UN*X

# Attempt to set APP_HOME
# Resolve links: $0 may be a link
PRG="$0"
# Need this for relative symlinks.
while [ -h "$PRG" ] ; do
    ls=`ls -ld "$PRG"`
    link=`expr "$ls" : '.*-> \\(.*\\)$'`
    if expr "$link" : '/.*' > /dev/null; then
        PRG="$link"
    else
        PRG=`dirname "$PRG"`"/$link"
    fi
done
SAVED="`pwd`"
cd "`dirname \\"$PRG\\"`/" >/dev/null
APP_HOME="`pwd -P`"
cd "$SAVED" >/dev/null

APP_NAME="Gradle"
APP_BASE_NAME=`basename "$0"`

# Use the maximum available, or set MAX_FD != -1 to use that value.
MAX_FD="maximum"

warn ( ) {
    echo "$*"
}

die ( ) {
    echo
    echo "$*"
    echo
    exit 1
}

# OS specific support (must be 'true' or 'false').
cygwin=false
msys=false
darwin=false
case "`uname`" in
  CYGWIN* )
    cygwin=true
    ;;
  Darwin* )
    darwin=true
    ;;
  MINGW* )
    msys=true
    ;;
esac

# For Cygwin, ensure paths are in UNIX format before anything is touched.
if $cygwin ; then
    [ -n "$JAVA_HOME" ] && JAVA_HOME=`cygpath --unix "$JAVA_HOME"`
fi

# Attempt to set ANDROID_HOME
if [ -z "$ANDROID_HOME" ] ; then
    if [ -d "$HOME/Android/Sdk" ] ; then
        export ANDROID_HOME="$HOME/Android/Sdk"
    elif [ -d "/c/Android/Sdk" ] ; then
        export ANDROID_HOME="/c/Android/Sdk"
    fi
fi

# For Cygwin, switch paths to Windows format before running java
if $cygwin ; then
    APP_HOME=`cygpath --path --mixed "$APP_HOME"`
    CLASSPATH=`cygpath --path --mixed "$CLASSPATH"`
    [ -n "$ANDROID_HOME" ] && ANDROID_HOME=`cygpath --path --mixed "$ANDROID_HOME"`
fi

exec gradle "$@"
""")
    
    # Make gradlew executable
    os.chmod(gradlew_script, 0o755)
    print(f"✅ Created: {gradlew_script}")
    
    return True

def build_apk():
    """Build the Android APK"""
    
    print("🚀 Building MLC-LLM Android APK...")
    
    # Change to android directory
    android_dir = Path("android")
    if not android_dir.exists():
        print("❌ Android directory not found!")
        return False
    
    os.chdir(android_dir)
    
    # Set environment variables
    os.environ['ANDROID_HOME'] = 'C:/Android/Sdk'
    os.environ['JAVA_HOME'] = 'C:/Program Files/Java/jdk-17'
    
    try:
        # Clean project
        print("🧹 Cleaning project...")
        if os.name == 'nt':  # Windows
            result = subprocess.run(['gradlew.bat', 'clean'], 
                                  capture_output=True, text=True, timeout=300)
        else:  # Unix-like
            result = subprocess.run(['./gradlew', 'clean'], 
                                  capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            print(f"❌ Clean failed: {result.stderr}")
            return False
        
        print("✅ Project cleaned successfully")
        
        # Build debug APK
        print("🔨 Building debug APK...")
        if os.name == 'nt':  # Windows
            result = subprocess.run(['gradlew.bat', 'assembleDebug'], 
                                  capture_output=True, text=True, timeout=600)
        else:  # Unix-like
            result = subprocess.run(['./gradlew', 'assembleDebug'], 
                                  capture_output=True, text=True, timeout=600)
        
        if result.returncode != 0:
            print(f"❌ Build failed: {result.stderr}")
            return False
        
        print("✅ Debug APK built successfully")
        
        # Build release APK
        print("🔨 Building release APK...")
        if os.name == 'nt':  # Windows
            result = subprocess.run(['gradlew.bat', 'assembleRelease'], 
                                  capture_output=True, text=True, timeout=600)
        else:  # Unix-like
            result = subprocess.run(['./gradlew', 'assembleRelease'], 
                                  capture_output=True, text=True, timeout=600)
        
        if result.returncode != 0:
            print(f"⚠️ Release build failed, but debug APK is available")
            print(f"Error: {result.stderr}")
        else:
            print("✅ Release APK built successfully")
        
        # Find and copy APK files
        apk_files = []
        
        # Look for debug APK
        debug_apk = Path("app/build/outputs/apk/debug/app-debug.apk")
        if debug_apk.exists():
            apk_files.append(("Debug APK", debug_apk))
        
        # Look for release APK
        release_apk = Path("app/build/outputs/apk/release/app-release.apk")
        if release_apk.exists():
            apk_files.append(("Release APK", release_apk))
        
        if not apk_files:
            print("❌ No APK files found!")
            return False
        
        # Copy APK files to root directory
        print("\n📦 APK Files Generated:")
        for apk_type, apk_path in apk_files:
            file_size = apk_path.stat().st_size
            size_mb = file_size / (1024 * 1024)
            
            # Copy to root with descriptive name
            target_name = f"mlc-llm-demo-{apk_type.lower().replace(' ', '-')}.apk"
            target_path = Path("..") / target_name
            shutil.copy2(apk_path, target_path)
            
            print(f"✅ {apk_type}: {target_path}")
            print(f"   Size: {size_mb:.1f} MB ({file_size:,} bytes)")
            print(f"   Original: {apk_path}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ Build timed out!")
        return False
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def main():
    """Main build function"""
    
    print("🚀 MLC-LLM Android APK Builder")
    print("=" * 50)
    
    try:
        # Setup Gradle wrapper
        if not setup_gradle_wrapper():
            raise Exception("Failed to setup Gradle wrapper")
        
        # Build APK
        if not build_apk():
            raise Exception("Failed to build APK")
        
        print("\n" + "=" * 50)
        print("🎉 APK Build Completed Successfully!")
        print("\n📱 Installation Instructions:")
        print("1. Enable 'Unknown Sources' in Android Settings")
        print("2. Transfer APK file to your Android device")
        print("3. Tap the APK file to install")
        print("4. Launch 'MLC-LLM Demo' app")
        
        print("\n🔧 APK Features:")
        print("• Complete MLC-LLM Android demo")
        print("• Multi-architecture support")
        print("• Interactive chat interface")
        print("• System information display")
        print("• Settings and configuration")
        
        return True
        
    except Exception as e:
        print(f"\n❌ APK build failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
