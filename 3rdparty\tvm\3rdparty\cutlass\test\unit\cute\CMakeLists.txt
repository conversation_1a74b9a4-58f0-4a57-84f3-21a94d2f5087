# Copyright (c) 2023 - 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

add_subdirectory(core)
add_subdirectory(volta)
add_subdirectory(turing)
add_subdirectory(ampere)
add_subdirectory(hopper)
add_subdirectory(layout)
add_subdirectory(msvc_compilation)

add_custom_target(
  cutlass_test_unit_cute
  DEPENDS
  cutlass_test_unit_cute_layout
  cutlass_test_unit_cute_core
  cutlass_test_unit_cute_volta
  cutlass_test_unit_cute_turing
  cutlass_test_unit_cute_ampere
  cutlass_test_unit_cute_hopper
  cutlass_test_unit_cute_msvc_compilation
  )

add_custom_target(
  test_unit_cute
  DEPENDS
  test_unit_cute_layout
  test_unit_cute_core
  test_unit_cute_volta
  test_unit_cute_ampere
  test_unit_cute_turing
  test_unit_cute_hopper
  test_unit_cute_msvc_compilation
  )
