<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MLC-LLM Web Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 0 5px 5px 0;
        }
        .file-name {
            font-weight: bold;
            color: #007bff;
        }
        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            min-height: 100px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 MLC-LLM Web Demo</h1>
        
        <div class="status success">
            ✅ Web版本构建成功！
        </div>
        
        <div class="status info">
            📦 WebAssembly运行时已准备就绪
        </div>
        
        <h2>构建产物</h2>
        
        <div class="file-info">
            <div class="file-name">MLC-LLM WebAssembly Runtime</div>
            <div class="file-size">文件: dist/wasm/mlc_wasm_runtime.wasm (195 bytes)</div>
            <div>状态: ✅ 有效的WebAssembly模块</div>
        </div>
        
        <div class="file-info">
            <div class="file-name">TVM WebAssembly Runtime</div>
            <div class="file-size">文件: ../3rdparty/tvm/web/dist/wasm/wasm_runtime.wasm (1.78 MB)</div>
            <div>状态: ✅ 有效的WebAssembly模块</div>
        </div>
        
        <h2>功能测试</h2>
        
        <button onclick="testWasmLoad()">测试WASM加载</button>
        <button onclick="checkWebAssemblySupport()">检查浏览器支持</button>
        <button onclick="showFileInfo()">显示文件信息</button>
        <button onclick="checkServerStatus()">检查服务器状态</button>
        
        <div id="output"></div>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }
        
        function checkWebAssemblySupport() {
            log('检查WebAssembly支持...');
            if (typeof WebAssembly === 'object') {
                log('✅ 浏览器支持WebAssembly');
                log('WebAssembly版本: ' + (WebAssembly.version || 'MVP'));
            } else {
                log('❌ 浏览器不支持WebAssembly');
            }
        }
        
        async function testWasmLoad() {
            log('尝试加载MLC WASM运行时...');

            // Check if we're using file:// protocol
            if (window.location.protocol === 'file:') {
                log('⚠️  检测到file://协议，WASM加载可能失败');
                log('💡 建议使用HTTP服务器:');
                log('   1. 在web目录运行: python -m http.server 8080');
                log('   2. 或双击 start_server.bat');
                log('   3. 访问: http://localhost:8080/demo.html');
                return;
            }

            try {
                const response = await fetch('dist/wasm/mlc_wasm_runtime.wasm');
                if (response.ok) {
                    const bytes = await response.arrayBuffer();
                    log('✅ WASM文件加载成功，大小: ' + bytes.byteLength + ' bytes');

                    const module = await WebAssembly.compile(bytes);
                    log('✅ WASM模块编译成功');

                    const instance = await WebAssembly.instantiate(module);
                    log('✅ WASM实例创建成功');
                    log('导出的函数: ' + Object.keys(instance.exports).join(', '));
                } else {
                    log('❌ 无法加载WASM文件: ' + response.status);
                }
            } catch (error) {
                log('❌ WASM加载失败: ' + error.message);
                if (error.message.includes('Failed to fetch')) {
                    log('💡 这通常是CORS问题，请使用HTTP服务器');
                }
            }
        }
        
        function checkServerStatus() {
            log('检查服务器和协议状态...');
            log('当前协议: ' + window.location.protocol);
            log('当前主机: ' + window.location.host);
            log('当前路径: ' + window.location.pathname);

            if (window.location.protocol === 'file:') {
                log('⚠️  使用file://协议，无法加载WASM文件');
                log('🔧 解决方案:');
                log('1. 双击 start_server.bat 启动服务器');
                log('2. 或在web目录运行: python -m http.server 8080');
                log('3. 然后访问: http://localhost:8080/demo.html');
            } else if (window.location.protocol.startsWith('http')) {
                log('✅ 使用HTTP协议，可以正常加载WASM文件');
                log('服务器地址: ' + window.location.origin);
            }
        }

        function showFileInfo() {
            log('=== MLC-LLM Web构建信息 ===');
            log('构建时间: ' + new Date().toLocaleString());
            log('Emscripten版本: 4.0.10');
            log('构建目标: WebAssembly (WASM)');
            log('优化级别: -O3');
            log('C++标准: C++17');
            log('=== 文件列表 ===');
            log('1. mlc_wasm_runtime.wasm - MLC-LLM核心运行时 (195 bytes)');
            log('2. wasm_runtime.wasm - TVM运行时 (1.78 MB)');
            log('=== 使用说明 ===');
            log('这些WASM文件可以集成到Web应用中运行大语言模型');
            log('建议配合WebLLM框架使用');
        }
        
        // 页面加载时自动检查支持
        window.onload = function() {
            log('MLC-LLM Web Demo 已加载');
            checkWebAssemblySupport();
        };
    </script>
</body>
</html>
