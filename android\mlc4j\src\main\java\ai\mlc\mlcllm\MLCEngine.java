package ai.mlc.mlcllm;

import android.content.Context;
import android.util.Log;

/**
 * MLC-LLM Engine for Android
 * Main interface for running large language models on Android devices
 *
 * Features:
 * - Multi-architecture support (ARM64, ARM32, x86_64)
 * - Efficient memory management
 * - Tokenization and detokenization
 * - Model loading and inference
 * - Error handling and logging
 */
public class MLCEngine {
    private static final String TAG = "MLCEngine";
    private static final String VERSION = "1.0.0";

    static {
        try {
            System.loadLibrary("mlc_llm_full");
            System.loadLibrary("tvm4j_runtime_packed");
            System.loadLibrary("tokenizers_cpp");
            Log.i(TAG, "MLC-LLM native libraries loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native libraries", e);
        }
    }

    private long nativeHandle;
    private boolean isInitialized = false;
    private String currentModel = null;

    public MLCEngine() {
        nativeHandle = createEngine();
        isInitialized = (nativeHandle != 0);
        Log.i(TAG, "MLCEngine initialized: " + isInitialized);
    }

    // Native method declarations
    public native long createEngine();
    public native void destroyEngine(long handle);
    public native String generate(long handle, String prompt, int maxTokens, float temperature);
    public native boolean loadModel(long handle, String modelPath);
    public native int getVocabSize(long handle);
    public native String[] tokenize(long handle, String text);
    public native String detokenize(long handle, int[] tokens);
    public native String getModelInfo(long handle);
    public native boolean isModelLoaded(long handle);

    // Public API methods
    public String generate(String prompt, int maxTokens) {
        return generate(prompt, maxTokens, 0.7f);
    }

    public String generate(String prompt, int maxTokens, float temperature) {
        if (!isInitialized) {
            Log.e(TAG, "Engine not initialized");
            return "";
        }

        if (currentModel == null) {
            Log.e(TAG, "No model loaded");
            return "";
        }

        Log.d(TAG, "Generating text with prompt: " + prompt.substring(0, Math.min(50, prompt.length())));
        return generate(nativeHandle, prompt, maxTokens, temperature);
    }

    public boolean loadModel(String modelPath) {
        if (!isInitialized) {
            Log.e(TAG, "Engine not initialized");
            return false;
        }

        Log.i(TAG, "Loading model from: " + modelPath);
        boolean result = loadModel(nativeHandle, modelPath);

        if (result) {
            currentModel = modelPath;
            Log.i(TAG, "Model loaded successfully");
        } else {
            Log.e(TAG, "Failed to load model");
        }

        return result;
    }

    public int getVocabSize() {
        if (!isInitialized) return 0;
        return getVocabSize(nativeHandle);
    }

    public String[] tokenize(String text) {
        if (!isInitialized) return new String[0];
        return tokenize(nativeHandle, text);
    }

    public String detokenize(int[] tokens) {
        if (!isInitialized) return "";
        return detokenize(nativeHandle, tokens);
    }

    public String getModelInfo() {
        if (!isInitialized) return "";
        return getModelInfo(nativeHandle);
    }

    public boolean isModelLoaded() {
        if (!isInitialized) return false;
        return isModelLoaded(nativeHandle);
    }

    public boolean isReady() {
        return isInitialized && currentModel != null;
    }

    public String getCurrentModel() {
        return currentModel;
    }

    public String getVersion() {
        return VERSION;
    }

    @Override
    protected void finalize() throws Throwable {
        release();
        super.finalize();
    }

    public void release() {
        if (nativeHandle != 0) {
            Log.i(TAG, "Releasing MLCEngine resources");
            destroyEngine(nativeHandle);
            nativeHandle = 0;
            isInitialized = false;
            currentModel = null;
        }
    }
}
