package ai.mlc.mlcllm;

/**
 * MLC-LLM Engine for Android
 * Main interface for running large language models on Android devices
 */
public class MLCEngine {
    static {
        System.loadLibrary("mlc_llm");
        System.loadLibrary("tvm4j_runtime_packed");
        System.loadLibrary("tokenizers_cpp");
    }
    
    private long nativeHandle;
    
    public MLCEngine() {
        nativeHandle = createEngine();
    }
    
    public native long createEngine();
    public native void destroyEngine(long handle);
    public native String generate(long handle, String prompt, int maxTokens);
    public native boolean loadModel(long handle, String modelPath);
    
    public String generate(String prompt, int maxTokens) {
        return generate(nativeHandle, prompt, maxTokens);
    }
    
    public boolean loadModel(String modelPath) {
        return loadModel(nativeHandle, modelPath);
    }
    
    @Override
    protected void finalize() throws Throwable {
        if (nativeHandle != 0) {
            destroyEngine(nativeHandle);
            nativeHandle = 0;
        }
        super.finalize();
    }
}
