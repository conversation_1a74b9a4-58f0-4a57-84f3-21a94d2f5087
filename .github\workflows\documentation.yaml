name: Build Docs

on:
  push:
    branches:
      - main

jobs:
  test_linux:
    name: Deploy Docs
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Configuring build Environment
      run: |
        sudo apt-get update
        python -m pip install -U pip wheel

    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.0'

    - name: Installing dependencies
      run: |
        python -m pip install -r docs/requirements.txt
        gem install jekyll jekyll-remote-theme

    - name: Deploying on GitHub Pages
      if: github.ref == 'refs/heads/main'
      run: |
        git remote set-url origin https://x-access-token:${{ secrets.MLC_GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY
        git config --global user.email "mlc-gh-actions-bot@nomail"
        git config --global user.name "mlc-gh-actions-bot"
        ./scripts/gh_deploy_site.sh
