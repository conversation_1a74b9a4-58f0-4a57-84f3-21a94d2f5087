<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::Array&lt; T, N, true &gt; Class Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html">Array&lt; T, N, true &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::Array&lt; T, N, true &gt; Class Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Statically sized array for any data type.  
</p>

<p><code>#include &lt;<a class="el" href="array_8h_source.html">array.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html">const_iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional constant iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html">const_reverse_iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional constant iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1const__reverse__iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html">iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html">reverse_iterator</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bidirectional iterator over elements.  <a href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4_1_1reverse__iterator.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a70e53f314dc7b7bb6050486d18c14b31"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a70e53f314dc7b7bb6050486d18c14b31">Storage</a> = T</td></tr>
<tr class="memdesc:a70e53f314dc7b7bb6050486d18c14b31"><td class="mdescLeft">&#160;</td><td class="mdescRight">Storage type.  <a href="#a70e53f314dc7b7bb6050486d18c14b31">More...</a><br /></td></tr>
<tr class="separator:a70e53f314dc7b7bb6050486d18c14b31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bf5b693d01e004852c642400d0e9b89"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7bf5b693d01e004852c642400d0e9b89">Element</a> = T</td></tr>
<tr class="memdesc:a7bf5b693d01e004852c642400d0e9b89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element type.  <a href="#a7bf5b693d01e004852c642400d0e9b89">More...</a><br /></td></tr>
<tr class="separator:a7bf5b693d01e004852c642400d0e9b89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9109f9dc42faa978ac2f846b98b29eb9"><td class="memItemLeft" align="right" valign="top">typedef T&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a></td></tr>
<tr class="separator:a9109f9dc42faa978ac2f846b98b29eb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0117378d6f0eda984b974ca760ae984"><td class="memItemLeft" align="right" valign="top">typedef size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a></td></tr>
<tr class="separator:ad0117378d6f0eda984b974ca760ae984"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ffe7541c2cadd34bc6e65ad351772ce"><td class="memItemLeft" align="right" valign="top">typedef ptrdiff_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7ffe7541c2cadd34bc6e65ad351772ce">difference_type</a></td></tr>
<tr class="separator:a7ffe7541c2cadd34bc6e65ad351772ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5827968c9c3deca639f5981ad895fe67"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a></td></tr>
<tr class="separator:a5827968c9c3deca639f5981ad895fe67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad64094119b89bb538cd1c1ea979c7954"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a></td></tr>
<tr class="separator:ad64094119b89bb538cd1c1ea979c7954"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a949beb7b21ad69d3a3bc394235dd8ec0"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">pointer</a></td></tr>
<tr class="separator:a949beb7b21ad69d3a3bc394235dd8ec0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a319dba33ebc8556e58f699f32c6a391b"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">const_pointer</a></td></tr>
<tr class="separator:a319dba33ebc8556e58f699f32c6a391b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a168315948a76d6ae9d7491ad0e1ca302"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a168315948a76d6ae9d7491ad0e1ca302">Array</a> ()</td></tr>
<tr class="separator:a168315948a76d6ae9d7491ad0e1ca302"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22af701f6f542b29198c759b653d3fb0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a22af701f6f542b29198c759b653d3fb0">Array</a> (Array const &amp;x)</td></tr>
<tr class="separator:a22af701f6f542b29198c759b653d3fb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae67b1d98a446384fc75a1c92474e719d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae67b1d98a446384fc75a1c92474e719d">clear</a> ()</td></tr>
<tr class="memdesc:ae67b1d98a446384fc75a1c92474e719d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Efficient clear method.  <a href="#ae67b1d98a446384fc75a1c92474e719d">More...</a><br /></td></tr>
<tr class="separator:ae67b1d98a446384fc75a1c92474e719d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aedd3e189bcbbb69ecd98978bcbbc3f1f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aedd3e189bcbbb69ecd98978bcbbc3f1f">at</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a> pos)</td></tr>
<tr class="separator:aedd3e189bcbbb69ecd98978bcbbc3f1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab504833fe30934eeb6e71e235e7942f1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab504833fe30934eeb6e71e235e7942f1">at</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a> pos) const </td></tr>
<tr class="separator:ab504833fe30934eeb6e71e235e7942f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0fea9a8e9f9def4c0059bba750a95167"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0fea9a8e9f9def4c0059bba750a95167">operator[]</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a> pos)</td></tr>
<tr class="separator:a0fea9a8e9f9def4c0059bba750a95167"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9812d796007116dbd8b20117976deb48"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9812d796007116dbd8b20117976deb48">operator[]</a> (<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a> pos) const </td></tr>
<tr class="separator:a9812d796007116dbd8b20117976deb48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5adbb5bb00cca5e538cd1215d1de08a4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5adbb5bb00cca5e538cd1215d1de08a4">front</a> ()</td></tr>
<tr class="separator:a5adbb5bb00cca5e538cd1215d1de08a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a692495c5f7a7d098e60b9292a07e4f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0a692495c5f7a7d098e60b9292a07e4f">front</a> () const </td></tr>
<tr class="separator:a0a692495c5f7a7d098e60b9292a07e4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa193b8e73b93639f84224d1fea46330d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aa193b8e73b93639f84224d1fea46330d">back</a> ()</td></tr>
<tr class="separator:aa193b8e73b93639f84224d1fea46330d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c81a715431cf5a772c2273362df97fd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6c81a715431cf5a772c2273362df97fd">back</a> () const </td></tr>
<tr class="separator:a6c81a715431cf5a772c2273362df97fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af47ab51582aa1e4c811a9e111b594556"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#af47ab51582aa1e4c811a9e111b594556">data</a> ()</td></tr>
<tr class="separator:af47ab51582aa1e4c811a9e111b594556"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d3d2637b7051145a2048cff1b55c0bf"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">const_pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3d3d2637b7051145a2048cff1b55c0bf">data</a> () const </td></tr>
<tr class="separator:a3d3d2637b7051145a2048cff1b55c0bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4e76ed2b36a4deda6ef36b00fdda363"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ae4e76ed2b36a4deda6ef36b00fdda363">raw_data</a> ()</td></tr>
<tr class="separator:ae4e76ed2b36a4deda6ef36b00fdda363"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90aaac40587e3ae5622030e999995f40"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">const_pointer</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a90aaac40587e3ae5622030e999995f40">raw_data</a> () const </td></tr>
<tr class="separator:a90aaac40587e3ae5622030e999995f40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d1028cb678773f861add6b47f13de78"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5d1028cb678773f861add6b47f13de78">empty</a> () const </td></tr>
<tr class="separator:a5d1028cb678773f861add6b47f13de78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac01c21b1956b645165150cfd0d0b0277"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ac01c21b1956b645165150cfd0d0b0277">size</a> () const </td></tr>
<tr class="separator:ac01c21b1956b645165150cfd0d0b0277"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3391b79db2b9f3bac9576c9bc7af0402"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3391b79db2b9f3bac9576c9bc7af0402">max_size</a> () const </td></tr>
<tr class="separator:a3391b79db2b9f3bac9576c9bc7af0402"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b3f29a6d79dd9cd55de367c96ecfc5c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a0b3f29a6d79dd9cd55de367c96ecfc5c">fill</a> (T const &amp;value)</td></tr>
<tr class="separator:a0b3f29a6d79dd9cd55de367c96ecfc5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf5a84cce457d31be7d30c57ab52f64c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#acf5a84cce457d31be7d30c57ab52f64c">begin</a> ()</td></tr>
<tr class="separator:acf5a84cce457d31be7d30c57ab52f64c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a815d434e9da9715a115896b3f6e64608"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a815d434e9da9715a115896b3f6e64608">cbegin</a> () const </td></tr>
<tr class="separator:a815d434e9da9715a115896b3f6e64608"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e5c83ff2ad6bbfeb5ba0e3c04e3843a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a8e5c83ff2ad6bbfeb5ba0e3c04e3843a">end</a> ()</td></tr>
<tr class="separator:a8e5c83ff2ad6bbfeb5ba0e3c04e3843a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27e663ee5e22d4af436588a500a6cc0c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a27e663ee5e22d4af436588a500a6cc0c">cend</a> () const </td></tr>
<tr class="separator:a27e663ee5e22d4af436588a500a6cc0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8ec17a6d004cb6ffd4450c0686cd924"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad8ec17a6d004cb6ffd4450c0686cd924">rbegin</a> ()</td></tr>
<tr class="separator:ad8ec17a6d004cb6ffd4450c0686cd924"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1813941489bef9563cc0bc3f647b2ca"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ab1813941489bef9563cc0bc3f647b2ca">crbegin</a> () const </td></tr>
<tr class="separator:ab1813941489bef9563cc0bc3f647b2ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6081f288dfc7b60da8d00913be8e83db"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a6081f288dfc7b60da8d00913be8e83db">rend</a> ()</td></tr>
<tr class="separator:a6081f288dfc7b60da8d00913be8e83db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76e1b5d728b155f9d967a43c0cc3b0dd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a76e1b5d728b155f9d967a43c0cc3b0dd">crend</a> () const </td></tr>
<tr class="separator:a76e1b5d728b155f9d967a43c0cc3b0dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:aff4b09f36ec3f8861ebd2db338a298b2"><td class="memItemLeft" align="right" valign="top">static size_t const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#aff4b09f36ec3f8861ebd2db338a298b2">kStorageElements</a> = N</td></tr>
<tr class="memdesc:aff4b09f36ec3f8861ebd2db338a298b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of storage elements.  <a href="#aff4b09f36ec3f8861ebd2db338a298b2">More...</a><br /></td></tr>
<tr class="separator:aff4b09f36ec3f8861ebd2db338a298b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59927c40660b5f39218f5867d4158e5e"><td class="memItemLeft" align="right" valign="top">static size_t const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a59927c40660b5f39218f5867d4158e5e">kElements</a> = N</td></tr>
<tr class="memdesc:a59927c40660b5f39218f5867d4158e5e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of logical elements.  <a href="#a59927c40660b5f39218f5867d4158e5e">More...</a><br /></td></tr>
<tr class="separator:a59927c40660b5f39218f5867d4158e5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a319dba33ebc8556e58f699f32c6a391b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a> const* cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">const_pointer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad64094119b89bb538cd1c1ea979c7954"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a> const&amp; cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7ffe7541c2cadd34bc6e65ad351772ce"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ptrdiff_t cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7ffe7541c2cadd34bc6e65ad351772ce">difference_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7bf5b693d01e004852c642400d0e9b89"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a7bf5b693d01e004852c642400d0e9b89">Element</a> =  T</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a949beb7b21ad69d3a3bc394235dd8ec0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a>* cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">pointer</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5827968c9c3deca639f5981ad895fe67"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a>&amp; cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad0117378d6f0eda984b974ca760ae984"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef size_t cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a70e53f314dc7b7bb6050486d18c14b31"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a70e53f314dc7b7bb6050486d18c14b31">Storage</a> =  T</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9109f9dc42faa978ac2f846b98b29eb9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef T cutlass::Array&lt; T, N, true &gt;::<a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a9109f9dc42faa978ac2f846b98b29eb9">value_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a168315948a76d6ae9d7491ad0e1ca302"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::Array&lt; T, N, true &gt;::Array </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a22af701f6f542b29198c759b653d3fb0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::Array&lt; T, N, true &gt;::Array </td>
          <td>(</td>
          <td class="paramtype">Array&lt; T, N, true &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="aedd3e189bcbbb69ecd98978bcbbc3f1f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a> cutlass::Array&lt; T, N, true &gt;::at </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab504833fe30934eeb6e71e235e7942f1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a> cutlass::Array&lt; T, N, true &gt;::at </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa193b8e73b93639f84224d1fea46330d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a> cutlass::Array&lt; T, N, true &gt;::back </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6c81a715431cf5a772c2273362df97fd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a> cutlass::Array&lt; T, N, true &gt;::back </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acf5a84cce457d31be7d30c57ab52f64c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator cutlass::Array&lt; T, N, true &gt;::begin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a815d434e9da9715a115896b3f6e64608"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator cutlass::Array&lt; T, N, true &gt;::cbegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a27e663ee5e22d4af436588a500a6cc0c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_iterator cutlass::Array&lt; T, N, true &gt;::cend </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae67b1d98a446384fc75a1c92474e719d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void cutlass::Array&lt; T, N, true &gt;::clear </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab1813941489bef9563cc0bc3f647b2ca"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator cutlass::Array&lt; T, N, true &gt;::crbegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a76e1b5d728b155f9d967a43c0cc3b0dd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> const_reverse_iterator cutlass::Array&lt; T, N, true &gt;::crend </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af47ab51582aa1e4c811a9e111b594556"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">pointer</a> cutlass::Array&lt; T, N, true &gt;::data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3d3d2637b7051145a2048cff1b55c0bf"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">const_pointer</a> cutlass::Array&lt; T, N, true &gt;::data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5d1028cb678773f861add6b47f13de78"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> bool cutlass::Array&lt; T, N, true &gt;::empty </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8e5c83ff2ad6bbfeb5ba0e3c04e3843a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> iterator cutlass::Array&lt; T, N, true &gt;::end </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0b3f29a6d79dd9cd55de367c96ecfc5c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void cutlass::Array&lt; T, N, true &gt;::fill </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5adbb5bb00cca5e538cd1215d1de08a4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a> cutlass::Array&lt; T, N, true &gt;::front </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0a692495c5f7a7d098e60b9292a07e4f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a> cutlass::Array&lt; T, N, true &gt;::front </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3391b79db2b9f3bac9576c9bc7af0402"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a> cutlass::Array&lt; T, N, true &gt;::max_size </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0fea9a8e9f9def4c0059bba750a95167"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a5827968c9c3deca639f5981ad895fe67">reference</a> cutlass::Array&lt; T, N, true &gt;::operator[] </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9812d796007116dbd8b20117976deb48"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad64094119b89bb538cd1c1ea979c7954">const_reference</a> cutlass::Array&lt; T, N, true &gt;::operator[] </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a>&#160;</td>
          <td class="paramname"><em>pos</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae4e76ed2b36a4deda6ef36b00fdda363"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a949beb7b21ad69d3a3bc394235dd8ec0">pointer</a> cutlass::Array&lt; T, N, true &gt;::raw_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a90aaac40587e3ae5622030e999995f40"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a319dba33ebc8556e58f699f32c6a391b">const_pointer</a> cutlass::Array&lt; T, N, true &gt;::raw_data </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad8ec17a6d004cb6ffd4450c0686cd924"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator cutlass::Array&lt; T, N, true &gt;::rbegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6081f288dfc7b60da8d00913be8e83db"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> reverse_iterator cutlass::Array&lt; T, N, true &gt;::rend </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac01c21b1956b645165150cfd0d0b0277"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#ad0117378d6f0eda984b974ca760ae984">size_type</a> cutlass::Array&lt; T, N, true &gt;::size </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a59927c40660b5f39218f5867d4158e5e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t const cutlass::Array&lt; T, N, true &gt;::kElements = N</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aff4b09f36ec3f8861ebd2db338a298b2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T , int N&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t const cutlass::Array&lt; T, N, true &gt;::kStorageElements = N</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="array_8h_source.html">array.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
