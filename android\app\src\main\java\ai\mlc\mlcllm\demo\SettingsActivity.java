package ai.mlc.mlcllm.demo;

import android.os.Bundle;
import android.view.MenuItem;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import ai.mlc.mlcllm.MLCUtils;

public class SettingsActivity extends AppCompatActivity {
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);
        
        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Settings");
        
        // Display detailed system information
        TextView systemInfoText = findViewById(R.id.systemInfoText);
        String detailedInfo = getDetailedSystemInfo();
        systemInfoText.setText(detailedInfo);
    }
    
    private String getDetailedSystemInfo() {
        StringBuilder info = new StringBuilder();
        
        info.append("=== MLC-LLM Demo Settings ===\n\n");
        
        info.append("📱 Device Information:\n");
        info.append(MLCUtils.getSystemInfo());
        info.append("\n");
        
        info.append("💾 Memory Information:\n");
        long availableMemory = MLCUtils.getAvailableMemory(this);
        info.append("Available Memory: ").append(MLCUtils.formatBytes(availableMemory)).append("\n");
        info.append("Recommended for LLM: 4GB+\n");
        info.append("Status: ").append(availableMemory > 4L * 1024 * 1024 * 1024 ? "✅ Sufficient" : "⚠️ Limited").append("\n\n");
        
        info.append("🏗️ Architecture Support:\n");
        String[] supportedAbis = MLCUtils.getSupportedArchitectures();
        for (String abi : supportedAbis) {
            info.append("• ").append(abi);
            if (abi.equals("arm64-v8a")) {
                info.append(" (Recommended)");
            }
            info.append("\n");
        }
        info.append("\n");
        
        info.append("⚙️ MLC-LLM Configuration:\n");
        info.append("Version: 1.0.0\n");
        info.append("Build Date: 2025-06-22\n");
        info.append("Native Libraries: ✅ Loaded\n");
        info.append("WebAssembly Support: ✅ Available\n");
        info.append("Optimization Level: Release (-O3)\n");
        info.append("C++ Standard: C++17\n\n");
        
        info.append("🔧 Performance Settings:\n");
        info.append("Thread Pool: Auto-detected\n");
        info.append("Memory Pool: Dynamic\n");
        info.append("Quantization: INT4/INT8 Support\n");
        info.append("Acceleration: Native ARM/x86\n\n");
        
        info.append("📋 Supported Features:\n");
        info.append("• Text Generation ✅\n");
        info.append("• Multi-turn Chat ✅\n");
        info.append("• Tokenization ✅\n");
        info.append("• Model Loading ✅\n");
        info.append("• Memory Management ✅\n");
        info.append("• Error Handling ✅\n\n");
        
        info.append("🚀 Optimization Tips:\n");
        info.append("• Use ARM64 devices for best performance\n");
        info.append("• Ensure 4GB+ RAM for large models\n");
        info.append("• Close other apps to free memory\n");
        info.append("• Use quantized models for efficiency\n");
        info.append("• Keep device cool during inference\n\n");
        
        info.append("📞 Support:\n");
        info.append("• Documentation: MLC-LLM GitHub\n");
        info.append("• Issues: Report on GitHub\n");
        info.append("• Community: MLC-LLM Discord\n");
        
        return info.toString();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
