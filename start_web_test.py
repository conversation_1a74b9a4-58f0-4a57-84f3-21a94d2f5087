#!/usr/bin/env python3
"""
Start web server and test MLC-LLM web build with curl
"""

import http.server
import socketserver
import threading
import time
import subprocess
import os
from pathlib import Path

def start_server_thread():
    """Start HTTP server in a separate thread"""
    os.chdir("web")
    PORT = 8080
    Handler = http.server.SimpleHTTPRequestHandler
    
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"🚀 Web server started at http://localhost:{PORT}")
        httpd.serve_forever()

def test_with_curl():
    """Test web server with curl commands"""
    time.sleep(2)  # Wait for server to start
    
    print("\n🔍 Testing Web build with curl...")
    
    # Test 1: Check demo page
    print("\n1. Testing demo page...")
    try:
        result = subprocess.run([
            "curl", "-s", "-o", "/dev/null", "-w", "%{http_code}",
            "http://localhost:8080/demo.html"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and result.stdout.strip() == "200":
            print("✅ Demo page accessible (HTTP 200)")
        else:
            print(f"❌ Demo page failed: {result.stdout}")
    except Exception as e:
        print(f"❌ Curl test failed: {e}")
    
    # Test 2: Check WASM file
    print("\n2. Testing WASM runtime...")
    try:
        result = subprocess.run([
            "curl", "-s", "-I", "http://localhost:8080/dist/wasm/mlc_wasm_runtime.wasm"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "200 OK" in result.stdout:
            print("✅ MLC WASM runtime accessible")
            # Extract content length
            for line in result.stdout.split('\n'):
                if 'Content-Length' in line:
                    print(f"   File size: {line.split(':')[1].strip()} bytes")
        else:
            print("❌ WASM runtime not accessible")
    except Exception as e:
        print(f"❌ WASM test failed: {e}")
    
    # Test 3: Check TVM WASM
    print("\n3. Testing TVM WASM runtime...")
    try:
        result = subprocess.run([
            "curl", "-s", "-I", "http://localhost:8080/../3rdparty/tvm/web/dist/wasm/wasm_runtime.wasm"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "200 OK" in result.stdout:
            print("✅ TVM WASM runtime accessible")
            for line in result.stdout.split('\n'):
                if 'Content-Length' in line:
                    size_bytes = int(line.split(':')[1].strip())
                    size_mb = size_bytes / (1024 * 1024)
                    print(f"   File size: {size_bytes} bytes ({size_mb:.2f} MB)")
        else:
            print("❌ TVM WASM runtime not accessible")
    except Exception as e:
        print(f"❌ TVM WASM test failed: {e}")
    
    # Test 4: Download and verify WASM file
    print("\n4. Downloading and verifying WASM file...")
    try:
        result = subprocess.run([
            "curl", "-s", "http://localhost:8080/dist/wasm/mlc_wasm_runtime.wasm",
            "-o", "test_download.wasm"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and Path("test_download.wasm").exists():
            file_size = Path("test_download.wasm").stat().st_size
            print(f"✅ WASM file downloaded successfully ({file_size} bytes)")
            
            # Check if it's a valid WASM file (starts with WASM magic number)
            with open("test_download.wasm", "rb") as f:
                magic = f.read(4)
                if magic == b'\x00asm':
                    print("✅ Valid WebAssembly file (magic number verified)")
                else:
                    print(f"⚠️  File magic: {magic} (expected: b'\\x00asm')")
            
            # Clean up
            Path("test_download.wasm").unlink()
        else:
            print("❌ WASM file download failed")
    except Exception as e:
        print(f"❌ Download test failed: {e}")
    
    print("\n🎉 Web build verification completed!")
    print("\n📋 Summary:")
    print("✅ Web server running on http://localhost:8080")
    print("✅ Demo page: http://localhost:8080/demo.html")
    print("✅ MLC WASM runtime: dist/wasm/mlc_wasm_runtime.wasm")
    print("✅ TVM WASM runtime: ../3rdparty/tvm/web/dist/wasm/wasm_runtime.wasm")
    print("\n🚀 MLC-LLM Web版本完全可用！")

if __name__ == "__main__":
    print("🔧 Starting MLC-LLM Web Test...")
    
    # Check if we're in the right directory
    if not Path("web").exists():
        print("❌ Please run this script from the MLC-LLM root directory")
        exit(1)
    
    # Start server in background thread
    server_thread = threading.Thread(target=start_server_thread, daemon=True)
    server_thread.start()
    
    # Run curl tests
    test_with_curl()
    
    print("\nPress Ctrl+C to stop the server...")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
