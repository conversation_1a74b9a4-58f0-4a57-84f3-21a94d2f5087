<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            xmlns:app="http://schemas.android.com/apk/res/android">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="System Information &amp; Settings"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/systemInfoText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Loading system information..."
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray"
                    android:fontFamily="monospace" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </ScrollView>

</LinearLayout>
