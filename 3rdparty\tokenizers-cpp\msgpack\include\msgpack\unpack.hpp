//
// MessagePack for C++ deserializing routine
//
// Copyright (C) 2008-2016 FURUHAS<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_UNPACK_HPP
#define MSGPACK_UNPACK_HPP

#include "msgpack/unpack_decl.hpp"

#include "msgpack/v1/unpack.hpp"
#include "msgpack/v2/unpack.hpp"
#include "msgpack/v3/unpack.hpp"

#endif // MSGPACK_UNPACK_HPP
