name: Configure
on: [push, pull_request]
jobs:
  ci-configure:
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Ubuntu GCC
            os: ubuntu-latest
            compiler: gcc
            configure-args: --warn

          # Test out of source builds
          - name: Ubuntu GCC OSB
            os: ubuntu-latest
            compiler: gcc
            configure-args: --warn
            build-dir: ../build
            src-dir: ../zlib

          - name: Ubuntu GCC ARM SF
            os: ubuntu-latest
            compiler: arm-linux-gnueabi-gcc
            configure-args: --warn
            chost: arm-linux-gnueabi
            packages: qemu qemu-user gcc-arm-linux-gnueabi libc-dev-armel-cross
            qemu-run: qemu-arm -L /usr/arm-linux-gnueabi

          - name: Ubuntu GCC ARM HF
            os: ubuntu-latest
            compiler: arm-linux-gnueabihf-gcc
            configure-args: --warn
            chost: arm-linux-gnueabihf
            packages: qemu qemu-user gcc-arm-linux-gnueabihf libc-dev-armhf-cross
            qemu-run: qemu-arm -L /usr/arm-linux-gnueabihf

          - name: Ubuntu GCC AARCH64
            os: ubuntu-latest
            compiler: aarch64-linux-gnu-gcc
            configure-args: --warn
            chost: aarch64-linux-gnu
            packages: qemu qemu-user gcc-aarch64-linux-gnu libc-dev-arm64-cross
            qemu-run: qemu-aarch64 -L /usr/aarch64-linux-gnu

          - name: Ubuntu GCC PPC
            os: ubuntu-latest
            compiler: powerpc-linux-gnu-gcc
            configure-args: --warn --static
            chost: powerpc-linux-gnu
            packages: qemu qemu-user gcc-powerpc-linux-gnu libc-dev-powerpc-cross
            qemu-run: qemu-ppc -L /usr/powerpc-linux-gnu
            cflags: -static
            ldflags: -static

          - name: Ubuntu GCC PPC64
            os: ubuntu-latest
            compiler: powerpc64-linux-gnu-gcc
            configure-args: --warn --static
            chost: powerpc-linux-gnu
            packages: qemu qemu-user gcc-powerpc64-linux-gnu libc-dev-ppc64-cross
            qemu-run: qemu-ppc64 -L /usr/powerpc64-linux-gnu
            cflags: -static
            ldflags: -static

          - name: Ubuntu GCC PPC64LE
            os: ubuntu-latest
            compiler: powerpc64le-linux-gnu-gcc
            configure-args: --warn
            chost: powerpc64le-linux-gnu
            packages: qemu qemu-user gcc-powerpc64le-linux-gnu libc-dev-ppc64el-cross
            qemu-run: qemu-ppc64le -L /usr/powerpc64le-linux-gnu

          - name: Ubuntu GCC S390X
            os: ubuntu-latest
            compiler: s390x-linux-gnu-gcc
            configure-args: --warn --static
            chost: s390x-linux-gnu
            packages: qemu qemu-user gcc-s390x-linux-gnu libc-dev-s390x-cross
            qemu-run: qemu-s390x -L /usr/s390x-linux-gnu
            cflags: -static
            ldflags: -static

          - name: macOS GCC
            os: macos-latest
            compiler: gcc-12
            configure-args: --warn

          - name: macOS Clang
            os: macos-latest
            compiler: clang
            configure-args: --warn

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install packages (Ubuntu)
      if: runner.os == 'Linux' && matrix.packages
      run: |
        sudo apt-get update
        sudo apt-get install -y ${{ matrix.packages }}

    - name: Generate project files
      run: |
        [ -d ${{ matrix.build-dir || '.' }} ] || mkdir ${{ matrix.build-dir || '.' }}
        cd ${{ matrix.build-dir || '.' }}
        ${{ matrix.src-dir || '.' }}/configure ${{ matrix.configure-args }}
      env:
        CC: ${{ matrix.compiler }}
        CFLAGS: ${{ matrix.cflags }} -Werror
        LDFLAGS: ${{ matrix.ldflags }}
        CHOST: ${{ matrix.chost }}

    - name: Compile source code
      run: make -j2
      working-directory: ${{ matrix.build-dir }}

    - name: Run test cases
      run: |
        make test
        make cover
      working-directory: ${{ matrix.build-dir }}
      env:
        QEMU_RUN: ${{ matrix.qemu-run }}

    - name: Upload build errors
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: ${{ matrix.name }} (configure)
        path: |
          ${{ matrix.build-dir || '.' }}/configure.log
        retention-days: 7
