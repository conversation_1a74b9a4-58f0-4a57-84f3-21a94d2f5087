# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_LIST_DETAIL_EDG_FOLD_RIGHT_HPP
# define MSGPACK_PREPROCESSOR_LIST_DETAIL_EDG_FOLD_RIGHT_HPP
#
# include <msgpack/preprocessor/control/iif.hpp>
# include <msgpack/preprocessor/list/adt.hpp>
# include <msgpack/preprocessor/tuple/eat.hpp>
#
# define MSGPACK_PP_LIST_FOLD_RIGHT_1(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_1_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_2(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_2_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_3(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_3_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_4(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_4_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_5(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_5_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_6(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_6_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_7(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_7_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_8(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_8_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_9(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_9_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_10(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_10_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_11(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_11_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_12(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_12_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_13(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_13_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_14(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_14_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_15(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_15_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_16(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_16_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_17(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_17_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_18(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_18_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_19(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_19_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_20(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_20_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_21(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_21_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_22(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_22_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_23(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_23_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_24(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_24_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_25(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_25_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_26(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_26_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_27(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_27_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_28(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_28_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_29(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_29_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_30(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_30_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_31(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_31_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_32(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_32_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_33(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_33_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_34(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_34_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_35(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_35_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_36(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_36_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_37(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_37_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_38(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_38_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_39(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_39_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_40(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_40_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_41(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_41_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_42(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_42_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_43(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_43_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_44(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_44_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_45(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_45_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_46(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_46_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_47(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_47_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_48(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_48_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_49(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_49_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_50(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_50_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_51(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_51_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_52(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_52_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_53(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_53_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_54(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_54_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_55(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_55_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_56(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_56_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_57(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_57_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_58(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_58_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_59(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_59_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_60(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_60_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_61(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_61_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_62(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_62_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_63(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_63_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_64(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_64_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_65(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_65_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_66(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_66_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_67(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_67_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_68(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_68_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_69(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_69_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_70(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_70_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_71(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_71_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_72(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_72_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_73(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_73_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_74(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_74_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_75(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_75_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_76(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_76_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_77(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_77_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_78(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_78_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_79(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_79_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_80(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_80_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_81(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_81_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_82(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_82_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_83(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_83_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_84(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_84_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_85(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_85_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_86(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_86_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_87(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_87_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_88(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_88_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_89(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_89_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_90(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_90_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_91(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_91_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_92(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_92_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_93(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_93_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_94(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_94_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_95(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_95_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_96(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_96_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_97(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_97_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_98(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_98_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_99(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_99_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_100(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_100_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_101(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_101_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_102(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_102_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_103(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_103_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_104(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_104_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_105(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_105_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_106(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_106_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_107(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_107_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_108(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_108_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_109(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_109_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_110(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_110_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_111(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_111_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_112(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_112_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_113(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_113_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_114(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_114_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_115(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_115_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_116(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_116_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_117(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_117_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_118(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_118_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_119(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_119_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_120(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_120_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_121(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_121_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_122(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_122_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_123(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_123_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_124(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_124_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_125(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_125_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_126(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_126_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_127(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_127_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_128(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_128_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_129(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_129_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_130(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_130_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_131(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_131_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_132(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_132_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_133(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_133_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_134(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_134_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_135(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_135_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_136(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_136_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_137(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_137_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_138(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_138_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_139(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_139_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_140(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_140_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_141(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_141_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_142(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_142_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_143(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_143_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_144(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_144_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_145(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_145_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_146(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_146_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_147(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_147_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_148(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_148_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_149(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_149_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_150(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_150_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_151(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_151_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_152(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_152_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_153(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_153_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_154(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_154_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_155(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_155_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_156(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_156_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_157(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_157_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_158(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_158_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_159(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_159_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_160(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_160_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_161(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_161_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_162(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_162_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_163(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_163_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_164(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_164_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_165(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_165_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_166(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_166_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_167(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_167_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_168(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_168_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_169(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_169_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_170(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_170_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_171(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_171_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_172(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_172_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_173(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_173_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_174(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_174_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_175(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_175_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_176(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_176_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_177(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_177_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_178(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_178_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_179(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_179_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_180(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_180_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_181(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_181_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_182(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_182_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_183(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_183_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_184(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_184_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_185(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_185_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_186(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_186_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_187(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_187_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_188(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_188_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_189(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_189_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_190(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_190_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_191(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_191_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_192(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_192_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_193(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_193_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_194(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_194_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_195(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_195_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_196(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_196_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_197(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_197_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_198(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_198_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_199(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_199_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_200(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_200_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_201(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_201_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_202(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_202_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_203(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_203_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_204(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_204_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_205(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_205_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_206(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_206_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_207(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_207_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_208(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_208_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_209(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_209_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_210(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_210_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_211(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_211_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_212(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_212_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_213(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_213_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_214(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_214_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_215(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_215_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_216(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_216_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_217(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_217_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_218(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_218_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_219(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_219_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_220(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_220_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_221(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_221_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_222(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_222_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_223(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_223_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_224(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_224_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_225(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_225_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_226(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_226_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_227(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_227_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_228(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_228_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_229(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_229_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_230(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_230_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_231(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_231_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_232(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_232_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_233(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_233_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_234(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_234_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_235(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_235_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_236(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_236_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_237(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_237_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_238(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_238_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_239(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_239_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_240(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_240_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_241(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_241_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_242(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_242_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_243(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_243_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_244(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_244_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_245(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_245_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_246(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_246_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_247(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_247_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_248(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_248_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_249(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_249_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_250(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_250_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_251(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_251_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_252(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_252_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_253(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_253_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_254(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_254_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_255(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_255_D(o, s, l)
# define MSGPACK_PP_LIST_FOLD_RIGHT_256(o, s, l) MSGPACK_PP_LIST_FOLD_RIGHT_256_D(o, s, l)
#
# define MSGPACK_PP_LIST_FOLD_RIGHT_1_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(2, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_2, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_2_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(3, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_3, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_3_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(4, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_4, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_4_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(5, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_5, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_5_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(6, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_6, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_6_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(7, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_7, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_7_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(8, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_8, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_8_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(9, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_9, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_9_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(10, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_10, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_10_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(11, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_11, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_11_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(12, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_12, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_12_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(13, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_13, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_13_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(14, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_14, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_14_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(15, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_15, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_15_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(16, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_16, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_16_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(17, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_17, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_17_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(18, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_18, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_18_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(19, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_19, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_19_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(20, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_20, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_20_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(21, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_21, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_21_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(22, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_22, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_22_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(23, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_23, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_23_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(24, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_24, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_24_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(25, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_25, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_25_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(26, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_26, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_26_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(27, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_27, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_27_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(28, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_28, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_28_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(29, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_29, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_29_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(30, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_30, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_30_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(31, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_31, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_31_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(32, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_32, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_32_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(33, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_33, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_33_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(34, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_34, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_34_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(35, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_35, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_35_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(36, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_36, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_36_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(37, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_37, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_37_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(38, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_38, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_38_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(39, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_39, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_39_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(40, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_40, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_40_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(41, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_41, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_41_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(42, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_42, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_42_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(43, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_43, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_43_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(44, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_44, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_44_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(45, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_45, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_45_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(46, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_46, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_46_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(47, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_47, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_47_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(48, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_48, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_48_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(49, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_49, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_49_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(50, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_50, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_50_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(51, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_51, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_51_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(52, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_52, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_52_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(53, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_53, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_53_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(54, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_54, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_54_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(55, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_55, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_55_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(56, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_56, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_56_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(57, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_57, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_57_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(58, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_58, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_58_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(59, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_59, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_59_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(60, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_60, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_60_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(61, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_61, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_61_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(62, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_62, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_62_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(63, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_63, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_63_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(64, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_64, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_64_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(65, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_65, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_65_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(66, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_66, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_66_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(67, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_67, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_67_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(68, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_68, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_68_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(69, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_69, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_69_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(70, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_70, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_70_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(71, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_71, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_71_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(72, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_72, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_72_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(73, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_73, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_73_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(74, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_74, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_74_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(75, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_75, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_75_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(76, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_76, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_76_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(77, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_77, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_77_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(78, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_78, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_78_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(79, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_79, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_79_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(80, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_80, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_80_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(81, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_81, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_81_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(82, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_82, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_82_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(83, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_83, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_83_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(84, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_84, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_84_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(85, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_85, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_85_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(86, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_86, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_86_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(87, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_87, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_87_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(88, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_88, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_88_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(89, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_89, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_89_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(90, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_90, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_90_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(91, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_91, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_91_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(92, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_92, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_92_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(93, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_93, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_93_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(94, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_94, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_94_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(95, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_95, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_95_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(96, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_96, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_96_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(97, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_97, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_97_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(98, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_98, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_98_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(99, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_99, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_99_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(100, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_100, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_100_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(101, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_101, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_101_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(102, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_102, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_102_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(103, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_103, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_103_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(104, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_104, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_104_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(105, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_105, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_105_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(106, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_106, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_106_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(107, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_107, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_107_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(108, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_108, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_108_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(109, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_109, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_109_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(110, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_110, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_110_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(111, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_111, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_111_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(112, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_112, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_112_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(113, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_113, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_113_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(114, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_114, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_114_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(115, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_115, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_115_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(116, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_116, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_116_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(117, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_117, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_117_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(118, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_118, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_118_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(119, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_119, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_119_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(120, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_120, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_120_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(121, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_121, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_121_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(122, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_122, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_122_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(123, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_123, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_123_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(124, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_124, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_124_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(125, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_125, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_125_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(126, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_126, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_126_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(127, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_127, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_127_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(128, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_128, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_128_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(129, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_129, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_129_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(130, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_130, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_130_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(131, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_131, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_131_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(132, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_132, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_132_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(133, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_133, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_133_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(134, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_134, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_134_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(135, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_135, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_135_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(136, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_136, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_136_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(137, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_137, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_137_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(138, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_138, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_138_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(139, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_139, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_139_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(140, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_140, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_140_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(141, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_141, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_141_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(142, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_142, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_142_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(143, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_143, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_143_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(144, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_144, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_144_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(145, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_145, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_145_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(146, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_146, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_146_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(147, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_147, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_147_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(148, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_148, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_148_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(149, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_149, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_149_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(150, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_150, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_150_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(151, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_151, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_151_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(152, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_152, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_152_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(153, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_153, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_153_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(154, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_154, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_154_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(155, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_155, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_155_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(156, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_156, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_156_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(157, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_157, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_157_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(158, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_158, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_158_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(159, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_159, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_159_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(160, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_160, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_160_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(161, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_161, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_161_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(162, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_162, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_162_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(163, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_163, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_163_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(164, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_164, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_164_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(165, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_165, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_165_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(166, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_166, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_166_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(167, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_167, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_167_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(168, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_168, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_168_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(169, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_169, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_169_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(170, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_170, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_170_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(171, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_171, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_171_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(172, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_172, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_172_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(173, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_173, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_173_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(174, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_174, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_174_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(175, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_175, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_175_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(176, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_176, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_176_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(177, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_177, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_177_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(178, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_178, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_178_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(179, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_179, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_179_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(180, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_180, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_180_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(181, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_181, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_181_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(182, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_182, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_182_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(183, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_183, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_183_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(184, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_184, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_184_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(185, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_185, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_185_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(186, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_186, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_186_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(187, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_187, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_187_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(188, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_188, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_188_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(189, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_189, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_189_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(190, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_190, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_190_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(191, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_191, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_191_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(192, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_192, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_192_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(193, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_193, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_193_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(194, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_194, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_194_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(195, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_195, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_195_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(196, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_196, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_196_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(197, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_197, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_197_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(198, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_198, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_198_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(199, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_199, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_199_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(200, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_200, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_200_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(201, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_201, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_201_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(202, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_202, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_202_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(203, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_203, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_203_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(204, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_204, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_204_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(205, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_205, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_205_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(206, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_206, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_206_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(207, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_207, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_207_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(208, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_208, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_208_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(209, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_209, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_209_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(210, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_210, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_210_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(211, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_211, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_211_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(212, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_212, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_212_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(213, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_213, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_213_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(214, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_214, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_214_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(215, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_215, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_215_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(216, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_216, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_216_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(217, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_217, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_217_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(218, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_218, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_218_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(219, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_219, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_219_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(220, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_220, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_220_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(221, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_221, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_221_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(222, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_222, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_222_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(223, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_223, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_223_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(224, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_224, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_224_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(225, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_225, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_225_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(226, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_226, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_226_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(227, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_227, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_227_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(228, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_228, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_228_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(229, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_229, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_229_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(230, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_230, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_230_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(231, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_231, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_231_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(232, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_232, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_232_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(233, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_233, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_233_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(234, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_234, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_234_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(235, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_235, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_235_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(236, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_236, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_236_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(237, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_237, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_237_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(238, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_238, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_238_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(239, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_239, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_239_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(240, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_240, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_240_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(241, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_241, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_241_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(242, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_242, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_242_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(243, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_243, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_243_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(244, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_244, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_244_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(245, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_245, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_245_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(246, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_246, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_246_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(247, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_247, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_247_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(248, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_248, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_248_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(249, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_249, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_249_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(250, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_250, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_250_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(251, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_251, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_251_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(252, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_252, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_252_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(253, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_253, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_253_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(254, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_254, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_254_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(255, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_255, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_255_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(256, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_256, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
# define MSGPACK_PP_LIST_FOLD_RIGHT_256_D(o, s, l) MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), o, s MSGPACK_PP_TUPLE_EAT_3)(257, MSGPACK_PP_IIF(MSGPACK_PP_LIST_IS_CONS(l), MSGPACK_PP_LIST_FOLD_RIGHT_257, MSGPACK_PP_NIL MSGPACK_PP_TUPLE_EAT_3)(o, s, MSGPACK_PP_LIST_REST(l)), MSGPACK_PP_LIST_FIRST(l))
#
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_NIL 1
#
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_1(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_2(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_3(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_4(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_5(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_6(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_7(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_8(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_9(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_10(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_11(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_12(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_13(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_14(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_15(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_16(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_17(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_18(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_19(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_20(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_21(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_22(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_23(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_24(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_25(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_26(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_27(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_28(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_29(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_30(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_31(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_32(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_33(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_34(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_35(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_36(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_37(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_38(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_39(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_40(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_41(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_42(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_43(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_44(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_45(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_46(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_47(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_48(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_49(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_50(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_51(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_52(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_53(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_54(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_55(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_56(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_57(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_58(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_59(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_60(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_61(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_62(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_63(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_64(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_65(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_66(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_67(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_68(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_69(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_70(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_71(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_72(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_73(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_74(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_75(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_76(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_77(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_78(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_79(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_80(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_81(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_82(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_83(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_84(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_85(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_86(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_87(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_88(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_89(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_90(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_91(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_92(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_93(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_94(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_95(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_96(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_97(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_98(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_99(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_100(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_101(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_102(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_103(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_104(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_105(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_106(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_107(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_108(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_109(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_110(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_111(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_112(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_113(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_114(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_115(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_116(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_117(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_118(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_119(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_120(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_121(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_122(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_123(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_124(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_125(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_126(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_127(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_128(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_129(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_130(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_131(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_132(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_133(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_134(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_135(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_136(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_137(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_138(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_139(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_140(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_141(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_142(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_143(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_144(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_145(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_146(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_147(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_148(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_149(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_150(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_151(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_152(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_153(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_154(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_155(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_156(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_157(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_158(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_159(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_160(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_161(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_162(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_163(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_164(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_165(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_166(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_167(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_168(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_169(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_170(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_171(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_172(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_173(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_174(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_175(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_176(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_177(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_178(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_179(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_180(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_181(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_182(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_183(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_184(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_185(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_186(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_187(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_188(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_189(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_190(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_191(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_192(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_193(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_194(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_195(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_196(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_197(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_198(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_199(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_200(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_201(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_202(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_203(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_204(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_205(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_206(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_207(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_208(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_209(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_210(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_211(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_212(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_213(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_214(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_215(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_216(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_217(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_218(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_219(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_220(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_221(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_222(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_223(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_224(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_225(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_226(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_227(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_228(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_229(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_230(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_231(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_232(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_233(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_234(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_235(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_236(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_237(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_238(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_239(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_240(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_241(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_242(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_243(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_244(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_245(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_246(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_247(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_248(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_249(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_250(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_251(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_252(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_253(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_254(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_255(o, s, l) 0
# define MSGPACK_PP_LIST_FOLD_RIGHT_CHECK_MSGPACK_PP_LIST_FOLD_RIGHT_256(o, s, l) 0
#
# endif
