<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::MatrixCoord Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1MatrixCoord-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::MatrixCoord Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="matrix__coord_8h_source.html">matrix_coord.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for cutlass::MatrixCoord:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1MatrixCoord__inherit__graph.png" border="0" usemap="#cutlass_1_1MatrixCoord_inherit__map" alt="Inheritance graph"/></div>
<map name="cutlass_1_1MatrixCoord_inherit__map" id="cutlass_1_1MatrixCoord_inherit__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<div class="dynheader">
Collaboration diagram for cutlass::MatrixCoord:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1MatrixCoord__coll__graph.png" border="0" usemap="#cutlass_1_1MatrixCoord_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1MatrixCoord_coll__map" id="cutlass_1_1MatrixCoord_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a25bb16898763baea29b73d8cf13f70d8"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> = int</td></tr>
<tr class="memdesc:a25bb16898763baea29b73d8cf13f70d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Integer-valued index.  <a href="#a25bb16898763baea29b73d8cf13f70d8">More...</a><br /></td></tr>
<tr class="separator:a25bb16898763baea29b73d8cf13f70d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0ac39fff446bb681b94de36e3f3650a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> = <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 2, <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> &gt;</td></tr>
<tr class="memdesc:ab0ac39fff446bb681b94de36e3f3650a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Base type is a <a class="el" href="structcutlass_1_1Coord.html" title="Statically-sized array specifying Coords within a tensor. ">Coord</a> of rank=2.  <a href="#ab0ac39fff446bb681b94de36e3f3650a">More...</a><br /></td></tr>
<tr class="separator:ab0ac39fff446bb681b94de36e3f3650a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_types_structcutlass_1_1Coord"><td colspan="2" onclick="javascript:toggleInherit('pub_types_structcutlass_1_1Coord')"><img src="closed.png" alt="-"/>&#160;Public Types inherited from <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 2, int &gt;</a></td></tr>
<tr class="memitem:a7a89e5661ef391dd9f4fe81f0c982b75 inherit pub_types_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> = int</td></tr>
<tr class="memdesc:a7a89e5661ef391dd9f4fe81f0c982b75 inherit pub_types_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Index type used to store elements.  <a href="#a7a89e5661ef391dd9f4fe81f0c982b75">More...</a><br /></td></tr>
<tr class="separator:a7a89e5661ef391dd9f4fe81f0c982b75 inherit pub_types_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab61db7c2bfacaf0b7ce465e70d48c44f inherit pub_types_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> = int64_t</td></tr>
<tr class="memdesc:ab61db7c2bfacaf0b7ce465e70d48c44f inherit pub_types_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type used to represent linear offsets.  <a href="#ab61db7c2bfacaf0b7ce465e70d48c44f">More...</a><br /></td></tr>
<tr class="separator:ab61db7c2bfacaf0b7ce465e70d48c44f inherit pub_types_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a36a8a680a466b55325eb0c0cb9fc29c6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a36a8a680a466b55325eb0c0cb9fc29c6">MatrixCoord</a> ()</td></tr>
<tr class="memdesc:a36a8a680a466b55325eb0c0cb9fc29c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default ctor.  <a href="#a36a8a680a466b55325eb0c0cb9fc29c6">More...</a><br /></td></tr>
<tr class="separator:a36a8a680a466b55325eb0c0cb9fc29c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64bddbf8238dc937a01a140722f7f39c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a64bddbf8238dc937a01a140722f7f39c">MatrixCoord</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 2, <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> &gt; const &amp;coord)</td></tr>
<tr class="memdesc:a64bddbf8238dc937a01a140722f7f39c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs from Coord&lt;2&gt;  <a href="#a64bddbf8238dc937a01a140722f7f39c">More...</a><br /></td></tr>
<tr class="separator:a64bddbf8238dc937a01a140722f7f39c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac77b18e67be18cfdfe1935939e7f2017"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#ac77b18e67be18cfdfe1935939e7f2017">MatrixCoord</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>, <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>)</td></tr>
<tr class="memdesc:ac77b18e67be18cfdfe1935939e7f2017"><td class="mdescLeft">&#160;</td><td class="mdescRight">Helper to construct from a row and column.  <a href="#ac77b18e67be18cfdfe1935939e7f2017">More...</a><br /></td></tr>
<tr class="separator:ac77b18e67be18cfdfe1935939e7f2017"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0580610f28427e376b24b71f67602d03"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a> () const </td></tr>
<tr class="memdesc:a0580610f28427e376b24b71f67602d03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the row of the coordinate.  <a href="#a0580610f28427e376b24b71f67602d03">More...</a><br /></td></tr>
<tr class="separator:a0580610f28427e376b24b71f67602d03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67f3102e51abad1205e8a3450e7a6c7e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a67f3102e51abad1205e8a3450e7a6c7e">row</a> ()</td></tr>
<tr class="memdesc:a67f3102e51abad1205e8a3450e7a6c7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the row of the coordinate.  <a href="#a67f3102e51abad1205e8a3450e7a6c7e">More...</a><br /></td></tr>
<tr class="separator:a67f3102e51abad1205e8a3450e7a6c7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbdcc5ca5b91f11f29046667b0bfde7b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a> () const </td></tr>
<tr class="memdesc:afbdcc5ca5b91f11f29046667b0bfde7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the column of the coordinate.  <a href="#afbdcc5ca5b91f11f29046667b0bfde7b">More...</a><br /></td></tr>
<tr class="separator:afbdcc5ca5b91f11f29046667b0bfde7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a093f5e568a81c6464dbf4aef996c32ba"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a093f5e568a81c6464dbf4aef996c32ba">column</a> ()</td></tr>
<tr class="memdesc:a093f5e568a81c6464dbf4aef996c32ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the column of the coordinate.  <a href="#a093f5e568a81c6464dbf4aef996c32ba">More...</a><br /></td></tr>
<tr class="separator:a093f5e568a81c6464dbf4aef996c32ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc89138032ae6b0bc29edb932959eed4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#afc89138032ae6b0bc29edb932959eed4">operator+</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:afc89138032ae6b0bc29edb932959eed4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise addition.  <a href="#afc89138032ae6b0bc29edb932959eed4">More...</a><br /></td></tr>
<tr class="separator:afc89138032ae6b0bc29edb932959eed4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7da194fa0200966f24a06dda344c6df"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#af7da194fa0200966f24a06dda344c6df">operator-</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:af7da194fa0200966f24a06dda344c6df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise subtraction.  <a href="#af7da194fa0200966f24a06dda344c6df">More...</a><br /></td></tr>
<tr class="separator:af7da194fa0200966f24a06dda344c6df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d0b67b654f39527b392505b1f1c77a5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a2d0b67b654f39527b392505b1f1c77a5">operator*</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:a2d0b67b654f39527b392505b1f1c77a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise multiplication.  <a href="#a2d0b67b654f39527b392505b1f1c77a5">More...</a><br /></td></tr>
<tr class="separator:a2d0b67b654f39527b392505b1f1c77a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2542660b34b184b5b8f9d0ad3dedc40a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a2542660b34b184b5b8f9d0ad3dedc40a">operator/</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b) const </td></tr>
<tr class="memdesc:a2542660b34b184b5b8f9d0ad3dedc40a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise division.  <a href="#a2542660b34b184b5b8f9d0ad3dedc40a">More...</a><br /></td></tr>
<tr class="separator:a2542660b34b184b5b8f9d0ad3dedc40a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad105615dbf7ede75caa0e778c873bd06"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#ad105615dbf7ede75caa0e778c873bd06">operator+=</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:ad105615dbf7ede75caa0e778c873bd06"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place addition.  <a href="#ad105615dbf7ede75caa0e778c873bd06">More...</a><br /></td></tr>
<tr class="separator:ad105615dbf7ede75caa0e778c873bd06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6feef48cf24733d22ca53a27cbc33ac0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a6feef48cf24733d22ca53a27cbc33ac0">operator-=</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:a6feef48cf24733d22ca53a27cbc33ac0"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place subtraction.  <a href="#a6feef48cf24733d22ca53a27cbc33ac0">More...</a><br /></td></tr>
<tr class="separator:a6feef48cf24733d22ca53a27cbc33ac0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5fd3c3b58af1147a5c73657c05a16f5b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#a5fd3c3b58af1147a5c73657c05a16f5b">operator*=</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:a5fd3c3b58af1147a5c73657c05a16f5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place multiplication.  <a href="#a5fd3c3b58af1147a5c73657c05a16f5b">More...</a><br /></td></tr>
<tr class="separator:a5fd3c3b58af1147a5c73657c05a16f5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab345c8ddb8048bfe3d667bc7ce6522f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1MatrixCoord.html#aab345c8ddb8048bfe3d667bc7ce6522f">operator/=</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;b)</td></tr>
<tr class="memdesc:aab345c8ddb8048bfe3d667bc7ce6522f"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place division.  <a href="#aab345c8ddb8048bfe3d667bc7ce6522f">More...</a><br /></td></tr>
<tr class="separator:aab345c8ddb8048bfe3d667bc7ce6522f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_structcutlass_1_1Coord"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_structcutlass_1_1Coord')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 2, int &gt;</a></td></tr>
<tr class="memitem:a5281db2419b5567db4265dead7ac02cc inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a5281db2419b5567db4265dead7ac02cc">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> value=<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>(0))</td></tr>
<tr class="memdesc:a5281db2419b5567db4265dead7ac02cc inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default ctor initializes uniformly.  <a href="#a5281db2419b5567db4265dead7ac02cc">More...</a><br /></td></tr>
<tr class="separator:a5281db2419b5567db4265dead7ac02cc inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7094975a4b7471315ca083ae575030a inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ab7094975a4b7471315ca083ae575030a">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const (&amp;_idx)[<a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a>])</td></tr>
<tr class="memdesc:ab7094975a4b7471315ca083ae575030a inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs from an array of integers.  <a href="#ab7094975a4b7471315ca083ae575030a">More...</a><br /></td></tr>
<tr class="separator:ab7094975a4b7471315ca083ae575030a inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42aefbb547e39b8cc7267c58a610c147 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a42aefbb547e39b8cc7267c58a610c147">Coord</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; <a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a>, <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>, <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> &gt; const &amp;coord)</td></tr>
<tr class="memdesc:a42aefbb547e39b8cc7267c58a610c147 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="#a42aefbb547e39b8cc7267c58a610c147">More...</a><br /></td></tr>
<tr class="separator:a42aefbb547e39b8cc7267c58a610c147 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a329f97d4a09ef34e8470fe55800871f8 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Slice &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a329f97d4a09ef34e8470fe55800871f8">slice</a> (int start=0, <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> identity=0) const</td></tr>
<tr class="separator:a329f97d4a09ef34e8470fe55800871f8 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae89e8a9fa3f07308f8938052ef1aa1fb inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ae89e8a9fa3f07308f8938052ef1aa1fb">min_dim_index</a> () const</td></tr>
<tr class="memdesc:ae89e8a9fa3f07308f8938052ef1aa1fb inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the index of the dimension with least value.  <a href="#ae89e8a9fa3f07308f8938052ef1aa1fb">More...</a><br /></td></tr>
<tr class="separator:ae89e8a9fa3f07308f8938052ef1aa1fb inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe58b7c8f153a6029c2adc173f340fe0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#abe58b7c8f153a6029c2adc173f340fe0">max_dim_index</a> () const</td></tr>
<tr class="memdesc:abe58b7c8f153a6029c2adc173f340fe0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the index of the dimension with greatest value.  <a href="#abe58b7c8f153a6029c2adc173f340fe0">More...</a><br /></td></tr>
<tr class="separator:abe58b7c8f153a6029c2adc173f340fe0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88096d051dd05111cf265a011a89f7f6 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a88096d051dd05111cf265a011a89f7f6">operator bool</a> () const</td></tr>
<tr class="memdesc:a88096d051dd05111cf265a011a89f7f6 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if Coord is non-zero.  <a href="#a88096d051dd05111cf265a011a89f7f6">More...</a><br /></td></tr>
<tr class="separator:a88096d051dd05111cf265a011a89f7f6 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa733c6fae0da553053530cba2dddcaa0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#aa733c6fae0da553053530cba2dddcaa0">operator!</a> () const</td></tr>
<tr class="memdesc:aa733c6fae0da553053530cba2dddcaa0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if Coord is uniformly zero.  <a href="#aa733c6fae0da553053530cba2dddcaa0">More...</a><br /></td></tr>
<tr class="separator:aa733c6fae0da553053530cba2dddcaa0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec4c529a728118c0df6a3f527daba746 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#aec4c529a728118c0df6a3f527daba746">operator+</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:aec4c529a728118c0df6a3f527daba746 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise addition.  <a href="#aec4c529a728118c0df6a3f527daba746">More...</a><br /></td></tr>
<tr class="separator:aec4c529a728118c0df6a3f527daba746 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e1a706629eae28128230a0fa34b84a0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a2e1a706629eae28128230a0fa34b84a0">operator-</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a2e1a706629eae28128230a0fa34b84a0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise subtraction.  <a href="#a2e1a706629eae28128230a0fa34b84a0">More...</a><br /></td></tr>
<tr class="separator:a2e1a706629eae28128230a0fa34b84a0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6b5fd8d0e5cb856d363fbff9a5b89dd inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ac6b5fd8d0e5cb856d363fbff9a5b89dd">operator*</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:ac6b5fd8d0e5cb856d363fbff9a5b89dd inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise multiplication.  <a href="#ac6b5fd8d0e5cb856d363fbff9a5b89dd">More...</a><br /></td></tr>
<tr class="separator:ac6b5fd8d0e5cb856d363fbff9a5b89dd inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ae9f189d1a7a5ce7bb5e4416559c79f inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a6ae9f189d1a7a5ce7bb5e4416559c79f">operator/</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a6ae9f189d1a7a5ce7bb5e4416559c79f inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Element-wise division.  <a href="#a6ae9f189d1a7a5ce7bb5e4416559c79f">More...</a><br /></td></tr>
<tr class="separator:a6ae9f189d1a7a5ce7bb5e4416559c79f inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb799faf60a17b708d0802f9e23c812f inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#acb799faf60a17b708d0802f9e23c812f">operator+=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:acb799faf60a17b708d0802f9e23c812f inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place addition.  <a href="#acb799faf60a17b708d0802f9e23c812f">More...</a><br /></td></tr>
<tr class="separator:acb799faf60a17b708d0802f9e23c812f inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15ac170c861b34d418432aeb62ea86e0 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a15ac170c861b34d418432aeb62ea86e0">operator-=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:a15ac170c861b34d418432aeb62ea86e0 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place subtraction.  <a href="#a15ac170c861b34d418432aeb62ea86e0">More...</a><br /></td></tr>
<tr class="separator:a15ac170c861b34d418432aeb62ea86e0 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00e618bc944d355badf67c0edd791412 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a00e618bc944d355badf67c0edd791412">operator*=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:a00e618bc944d355badf67c0edd791412 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place multiplication.  <a href="#a00e618bc944d355badf67c0edd791412">More...</a><br /></td></tr>
<tr class="separator:a00e618bc944d355badf67c0edd791412 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af515e669363986dbbd60951ea6b69e14 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#af515e669363986dbbd60951ea6b69e14">operator/=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b)</td></tr>
<tr class="memdesc:af515e669363986dbbd60951ea6b69e14 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">In-place division.  <a href="#af515e669363986dbbd60951ea6b69e14">More...</a><br /></td></tr>
<tr class="separator:af515e669363986dbbd60951ea6b69e14 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6eee93e5fdbe147f751ec108b28275a1 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a6eee93e5fdbe147f751ec108b28275a1">operator[]</a> (int dim)</td></tr>
<tr class="memdesc:a6eee93e5fdbe147f751ec108b28275a1 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Member access operator.  <a href="#a6eee93e5fdbe147f751ec108b28275a1">More...</a><br /></td></tr>
<tr class="separator:a6eee93e5fdbe147f751ec108b28275a1 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accf5689f0d6a6f91965bff0cfd9ec296 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#accf5689f0d6a6f91965bff0cfd9ec296">operator[]</a> (int dim) const</td></tr>
<tr class="memdesc:accf5689f0d6a6f91965bff0cfd9ec296 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Member access operator.  <a href="#accf5689f0d6a6f91965bff0cfd9ec296">More...</a><br /></td></tr>
<tr class="separator:accf5689f0d6a6f91965bff0cfd9ec296 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a057a417a4d4a6e2f69e0b55a6f7ee902 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a057a417a4d4a6e2f69e0b55a6f7ee902">dot</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b, <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a> <a class="el" href="structcutlass_1_1Coord.html#a49bb1a68198bd4c520d15efe3e84f757">sum</a>=<a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>(0)) const</td></tr>
<tr class="memdesc:a057a417a4d4a6e2f69e0b55a6f7ee902 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes the dot product with anotherCoord object.  <a href="#a057a417a4d4a6e2f69e0b55a6f7ee902">More...</a><br /></td></tr>
<tr class="separator:a057a417a4d4a6e2f69e0b55a6f7ee902 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a65128c86b236cd2bea875b85a34bc1 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a> ()</td></tr>
<tr class="memdesc:a8a65128c86b236cd2bea875b85a34bc1 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the index of a given Coord element.  <a href="#a8a65128c86b236cd2bea875b85a34bc1">More...</a><br /></td></tr>
<tr class="separator:a8a65128c86b236cd2bea875b85a34bc1 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a358dde78a1c2105a9aeb4adee8bb3d2d inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a358dde78a1c2105a9aeb4adee8bb3d2d">at</a> (int dim)</td></tr>
<tr class="memdesc:a358dde78a1c2105a9aeb4adee8bb3d2d inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access via index; may limit unrolling potential.  <a href="#a358dde78a1c2105a9aeb4adee8bb3d2d">More...</a><br /></td></tr>
<tr class="separator:a358dde78a1c2105a9aeb4adee8bb3d2d inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7379275d7431ead927af7966b6fa0ec inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ac7379275d7431ead927af7966b6fa0ec">at</a> () const</td></tr>
<tr class="memdesc:ac7379275d7431ead927af7966b6fa0ec inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the index of a given Coord element.  <a href="#ac7379275d7431ead927af7966b6fa0ec">More...</a><br /></td></tr>
<tr class="separator:ac7379275d7431ead927af7966b6fa0ec inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06046c22b877abfb277d3f0fe4f8578a inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a> const &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a06046c22b877abfb277d3f0fe4f8578a">at</a> (int dim) const</td></tr>
<tr class="memdesc:a06046c22b877abfb277d3f0fe4f8578a inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access via index; may limit unrolling potential.  <a href="#a06046c22b877abfb277d3f0fe4f8578a">More...</a><br /></td></tr>
<tr class="separator:a06046c22b877abfb277d3f0fe4f8578a inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8424ccd74e7e0ff1cf358ef571779cba inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8424ccd74e7e0ff1cf358ef571779cba">operator==</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a8424ccd74e7e0ff1cf358ef571779cba inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determines if two Coord&lt;&gt; objects are equal.  <a href="#a8424ccd74e7e0ff1cf358ef571779cba">More...</a><br /></td></tr>
<tr class="separator:a8424ccd74e7e0ff1cf358ef571779cba inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8183b9203a213d4b6381ad7dc120deea inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a8183b9203a213d4b6381ad7dc120deea">operator!=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a8183b9203a213d4b6381ad7dc120deea inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not equal.  <a href="#a8183b9203a213d4b6381ad7dc120deea">More...</a><br /></td></tr>
<tr class="separator:a8183b9203a213d4b6381ad7dc120deea inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40e145063833155c800b38f82cee7461 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html">Coord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a40e145063833155c800b38f82cee7461">clamp</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;max, <a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;min=<a class="el" href="structcutlass_1_1Coord.html">Coord</a>())</td></tr>
<tr class="memdesc:a40e145063833155c800b38f82cee7461 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clamps a coordinate to a range specified by maximum and minimum values.  <a href="#a40e145063833155c800b38f82cee7461">More...</a><br /></td></tr>
<tr class="separator:a40e145063833155c800b38f82cee7461 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49bb1a68198bd4c520d15efe3e84f757 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Index</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a49bb1a68198bd4c520d15efe3e84f757">sum</a> () const</td></tr>
<tr class="memdesc:a49bb1a68198bd4c520d15efe3e84f757 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the sum of all elements.  <a href="#a49bb1a68198bd4c520d15efe3e84f757">More...</a><br /></td></tr>
<tr class="separator:a49bb1a68198bd4c520d15efe3e84f757 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5a2fb5b6b57e0726624c2b6e7c6545c inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">LongIndex</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ad5a2fb5b6b57e0726624c2b6e7c6545c">product</a> () const</td></tr>
<tr class="memdesc:ad5a2fb5b6b57e0726624c2b6e7c6545c inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the product of all elements.  <a href="#ad5a2fb5b6b57e0726624c2b6e7c6545c">More...</a><br /></td></tr>
<tr class="separator:ad5a2fb5b6b57e0726624c2b6e7c6545c inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47ad37153eb8d291266a51b39ead5948 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a47ad37153eb8d291266a51b39ead5948">operator&lt;</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a47ad37153eb8d291266a51b39ead5948 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Less than operator.  <a href="#a47ad37153eb8d291266a51b39ead5948">More...</a><br /></td></tr>
<tr class="separator:a47ad37153eb8d291266a51b39ead5948 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e6405e081936a4fb23f15160e94ad08 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a0e6405e081936a4fb23f15160e94ad08">operator&lt;=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a0e6405e081936a4fb23f15160e94ad08 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Less than or equals operator.  <a href="#a0e6405e081936a4fb23f15160e94ad08">More...</a><br /></td></tr>
<tr class="separator:a0e6405e081936a4fb23f15160e94ad08 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08507ff13f518a93a7d16ea0018f8a53 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a08507ff13f518a93a7d16ea0018f8a53">operator&gt;</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:a08507ff13f518a93a7d16ea0018f8a53 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Greater than operator.  <a href="#a08507ff13f518a93a7d16ea0018f8a53">More...</a><br /></td></tr>
<tr class="separator:a08507ff13f518a93a7d16ea0018f8a53 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae37243e9f51b2b92b5fd09de69392657 inherit pub_methods_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#ae37243e9f51b2b92b5fd09de69392657">operator&gt;=</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a> const &amp;b) const</td></tr>
<tr class="memdesc:ae37243e9f51b2b92b5fd09de69392657 inherit pub_methods_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Greater than or equals operator.  <a href="#ae37243e9f51b2b92b5fd09de69392657">More...</a><br /></td></tr>
<tr class="separator:ae37243e9f51b2b92b5fd09de69392657 inherit pub_methods_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_attribs_structcutlass_1_1Coord"><td colspan="2" onclick="javascript:toggleInherit('pub_static_attribs_structcutlass_1_1Coord')"><img src="closed.png" alt="-"/>&#160;Static Public Attributes inherited from <a class="el" href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 2, int &gt;</a></td></tr>
<tr class="memitem:a2b07d7291d175920274c5e3346e5b68b inherit pub_static_attribs_structcutlass_1_1Coord"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Coord.html#a2b07d7291d175920274c5e3346e5b68b">kRank</a></td></tr>
<tr class="memdesc:a2b07d7291d175920274c5e3346e5b68b inherit pub_static_attribs_structcutlass_1_1Coord"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of elements in Coord.  <a href="#a2b07d7291d175920274c5e3346e5b68b">More...</a><br /></td></tr>
<tr class="separator:a2b07d7291d175920274c5e3346e5b68b inherit pub_static_attribs_structcutlass_1_1Coord"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> wraps <a class="el" href="structcutlass_1_1Coord.html">Coord&lt;2, int&gt;</a> to provide a helper for accessing named dimensions. Classes expecting a coordinate in the rank=2 index space of a matrix should use <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>. </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ab0ac39fff446bb681b94de36e3f3650a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">cutlass::MatrixCoord::Base</a> =  <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;2, <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a25bb16898763baea29b73d8cf13f70d8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">cutlass::MatrixCoord::Index</a> =  int</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a36a8a680a466b55325eb0c0cb9fc29c6"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::MatrixCoord::MatrixCoord </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a64bddbf8238dc937a01a140722f7f39c"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::MatrixCoord::MatrixCoord </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 2, <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> &gt; const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac77b18e67be18cfdfe1935939e7f2017"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::MatrixCoord::MatrixCoord </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a>&#160;</td>
          <td class="paramname"><em>row</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a>&#160;</td>
          <td class="paramname"><em>column</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="afbdcc5ca5b91f11f29046667b0bfde7b"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> const&amp; cutlass::MatrixCoord::column </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a093f5e568a81c6464dbf4aef996c32ba"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a>&amp; cutlass::MatrixCoord::column </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2d0b67b654f39527b392505b1f1c77a5"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> cutlass::MatrixCoord::operator* </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5fd3c3b58af1147a5c73657c05a16f5b"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&amp; cutlass::MatrixCoord::operator*= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afc89138032ae6b0bc29edb932959eed4"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> cutlass::MatrixCoord::operator+ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad105615dbf7ede75caa0e778c873bd06"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&amp; cutlass::MatrixCoord::operator+= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af7da194fa0200966f24a06dda344c6df"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> cutlass::MatrixCoord::operator- </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6feef48cf24733d22ca53a27cbc33ac0"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&amp; cutlass::MatrixCoord::operator-= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2542660b34b184b5b8f9d0ad3dedc40a"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> cutlass::MatrixCoord::operator/ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aab345c8ddb8048bfe3d667bc7ce6522f"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&amp; cutlass::MatrixCoord::operator/= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html#ab0ac39fff446bb681b94de36e3f3650a">Base</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0580610f28427e376b24b71f67602d03"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a> const&amp; cutlass::MatrixCoord::row </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a67f3102e51abad1205e8a3450e7a6c7e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1MatrixCoord.html#a25bb16898763baea29b73d8cf13f70d8">Index</a>&amp; cutlass::MatrixCoord::row </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="matrix__coord_8h_source.html">matrix_coord.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
