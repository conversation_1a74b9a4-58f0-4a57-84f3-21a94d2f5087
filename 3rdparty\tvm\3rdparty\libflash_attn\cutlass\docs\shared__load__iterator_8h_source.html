<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: shared_load_iterator.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_05a6795d99d74f63b7300fc6eb9e55c2.html">threadblock</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">shared_load_iterator.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="shared__load__iterator_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__shape_8h.html">cutlass/matrix_shape.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__ref_8h.html">cutlass/tensor_ref.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="output__tile__thread__map_8h.html">cutlass/epilogue/threadblock/output_tile_thread_map.h</a>&quot;</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">namespace </span>threadblock {</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">typename</span> ThreadMap_,       </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keyword">typename</span> Element_,         </div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keywordtype">int</span> MaxAlignment = ThreadMap_::kElementsPerAccess * <a class="code" href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">sizeof_bits&lt;Element_&gt;::value</a> / 8</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;&gt;</div><div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html">   61</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html">SharedLoadIterator</a> {</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a55f1d151cdd69269ac86c3567d1eff28">   63</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a55f1d151cdd69269ac86c3567d1eff28">ThreadMap</a> = ThreadMap_;</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a996bdfe3c2ef7f1d3684ce9a71f3b31d">   64</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a996bdfe3c2ef7f1d3684ce9a71f3b31d">Shape</a> = <span class="keyword">typename</span> ThreadMap::Shape;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a961521dfce056013892c64a0ecd4ef2f">   66</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a961521dfce056013892c64a0ecd4ef2f">Element</a> = Element_;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#abc1ac65c12315e325e02c5c2e313e0a7">   68</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae7ddd9a25483dcd3efb1d314bea81137">   69</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a5208835793dcd89f36ea65a8fcadf7e7">   70</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a5208835793dcd89f36ea65a8fcadf7e7">ConstTensorRef</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">TensorRef::ConstTensorRef</a>;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a82ecb0e54bbcb04364a1924167cbeb15">   72</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a82ecb0e54bbcb04364a1924167cbeb15">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#aa49e242b14b4f482bc6bdd082acfb576">Layout::Index</a>;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae5daed317dfa5a63b781f4900454bcd3">   73</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae5daed317dfa5a63b781f4900454bcd3">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">Layout::LongIndex</a>;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#aa30154b84e1eb2d473d389b3e1c56f29">   74</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae7cc22c50e7e00742c7430080d673dc8">   76</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae7cc22c50e7e00742c7430080d673dc8">kElementsPerAccess</a> = ThreadMap::kElementsPerAccess;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#abd70e40965a6296adeeeb7ddcca7da90">   78</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#abd70e40965a6296adeeeb7ddcca7da90">kMinAlignment</a> = ThreadMap_::kElementsPerAccess * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element_&gt;::value</a> / 8;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae46b49371732c012f0b44399f39d8b0a">   80</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae46b49371732c012f0b44399f39d8b0a">kAlignment</a> = (MaxAlignment &lt; kMinAlignment ? MaxAlignment : <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#abd70e40965a6296adeeeb7ddcca7da90">kMinAlignment</a>);</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a5adc9c1897c49421e3b31fc3f8d34cbc">   82</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a5adc9c1897c49421e3b31fc3f8d34cbc">kThreads</a> = ThreadMap::kThreads;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">Fragment</a> = Array&lt;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a961521dfce056013892c64a0ecd4ef2f">Element</a>, </div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    ThreadMap::Iterations::kColumn * </div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    ThreadMap::Iterations::kRow * </div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    ThreadMap::Iterations::kGroup * </div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    ThreadMap::Iterations::kCluster * </div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">   91</a></span>&#160;    ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray</a>&lt;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a961521dfce056013892c64a0ecd4ef2f">Element</a>, </div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    ThreadMap::kElementsPerAccess, </div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a813f447702209b6efdb1d4dea64efc2c">   97</a></span>&#160;    kAlignment&gt;;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  uint8_t *byte_pointer_;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="keywordtype">int</span> stride_;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae031b4f21a36638f51091ad12d529d5a">  119</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae031b4f21a36638f51091ad12d529d5a">SharedLoadIterator</a>(</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> ref,</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <span class="keywordtype">int</span> thread_idx</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  ):</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    byte_pointer_(reinterpret_cast&lt;uint8_t *&gt;(ref.data())),</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    stride_((ref.stride(0) * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt;Element&gt;::value) / 8) {</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> thread_offset = ThreadMap::initial_offset(thread_idx);</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <span class="comment">// Initialize pointer</span></div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    byte_pointer_ +=</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;      thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * stride_ + </div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;      thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * <span class="keyword">sizeof</span>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a813f447702209b6efdb1d4dea64efc2c">AccessType</a>) / kElementsPerAccess;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    <span class="keywordtype">int</span> byte_offset = thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * stride_ + </div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;      thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * <span class="keyword">sizeof</span>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a813f447702209b6efdb1d4dea64efc2c">AccessType</a>) / kElementsPerAccess;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  }</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00139"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a1e022ec8d968520bfd0bc7ce51dcaf93">  139</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a1e022ec8d968520bfd0bc7ce51dcaf93">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae5daed317dfa5a63b781f4900454bcd3">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    byte_pointer_ += pointer_offset * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> / 8;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  }</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00144"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a4706618cd7b040c01d23440574696417">  144</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a4706618cd7b040c01d23440574696417">add_tile_offset</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;offset) {</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a1e022ec8d968520bfd0bc7ce51dcaf93">add_pointer_offset</a>(offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * stride_ / (<a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> / 8) + offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * Shape::kColumn);</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  }</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a54ab974e2fbeb22d335d55618b3c7b3f">  150</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a54ab974e2fbeb22d335d55618b3c7b3f">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a82ecb0e54bbcb04364a1924167cbeb15">Index</a> pointer_offset) {</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> cluster = 0; cluster &lt; ThreadMap::Iterations::kCluster; ++cluster) {</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> group = 0; group &lt; ThreadMap::Iterations::kGroup; ++group) {</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> row = 0; row &lt; ThreadMap::Iterations::kRow; ++row) {</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;          uint8_t <span class="keyword">const</span> *byte_pointer = byte_pointer_ + </div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;            row * ThreadMap::Delta::kRow * stride_ + </div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;            group * ThreadMap::Delta::kGroup* stride_ + </div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;            cluster * ThreadMap::Delta::kCluster * stride_ +</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;            pointer_offset * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> / 8;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;          <span class="keywordtype">int</span> frag_row_idx = </div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;            (row + ThreadMap::Iterations::kRow * (group + ThreadMap::Iterations::kGroup * cluster));</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;          <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const</span> *memory_pointer = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(byte_pointer);</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;          <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;          <span class="keywordflow">for</span> (<span class="keywordtype">int</span> column = 0; column &lt; ThreadMap::Iterations::kColumn; ++column) {</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;            </div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;            <span class="keywordtype">int</span> frag_idx = frag_row_idx * ThreadMap::Iterations::kColumn + column;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;            frag_ptr[frag_idx] = </div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;              memory_pointer[column * ThreadMap::Delta::kColumn / <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae7cc22c50e7e00742c7430080d673dc8">kElementsPerAccess</a>];            </div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;          }</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;        }</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;      }</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    }</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  }</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00189"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#acc9440f6242ee064cb6e48f9387d73ad">  189</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#acc9440f6242ee064cb6e48f9387d73ad">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">Fragment</a> &amp;frag) {</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a54ab974e2fbeb22d335d55618b3c7b3f">load_with_pointer_offset</a>(frag, 0);</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;  }</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;};</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;} <span class="comment">// namespace threadblock</span></div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_a4062a36ab044fdea058504ed52ee60b8"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">cutlass::layout::RowMajor::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:62</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_afb37ed9a0f3873600b9e743b2dcb805e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">cutlass::epilogue::threadblock::SharedLoadIterator::Fragment</a></div><div class="ttdeci">Array&lt; Element, ThreadMap::Iterations::kColumn *ThreadMap::Iterations::kRow *ThreadMap::Iterations::kGroup *ThreadMap::Iterations::kCluster *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object. </div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:91</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html_aff47de86de21dae23ad36184c3d2bb12"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html#aff47de86de21dae23ad36184c3d2bb12">cutlass::sizeof_bits::value</a></div><div class="ttdeci">static int const value</div><div class="ttdef"><b>Definition:</b> numeric_types.h:43</div></div>
<div class="ttc" id="tensor__ref_8h_html"><div class="ttname"><a href="tensor__ref_8h.html">tensor_ref.h</a></div><div class="ttdoc">Defines a structure containing strides, bounds, and a pointer to tensor data. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a54ab974e2fbeb22d335d55618b3c7b3f"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a54ab974e2fbeb22d335d55618b3c7b3f">cutlass::epilogue::threadblock::SharedLoadIterator::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:150</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a5adc9c1897c49421e3b31fc3f8d34cbc"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a5adc9c1897c49421e3b31fc3f8d34cbc">cutlass::epilogue::threadblock::SharedLoadIterator::kThreads</a></div><div class="ttdeci">static int const kThreads</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:82</div></div>
<div class="ttc" id="classcutlass_1_1AlignedArray_html"><div class="ttname"><a href="classcutlass_1_1AlignedArray.html">cutlass::AlignedArray</a></div><div class="ttdoc">Aligned array type. </div><div class="ttdef"><b>Definition:</b> array.h:511</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_ae031b4f21a36638f51091ad12d529d5a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae031b4f21a36638f51091ad12d529d5a">cutlass::epilogue::threadblock::SharedLoadIterator::SharedLoadIterator</a></div><div class="ttdeci">CUTLASS_DEVICE SharedLoadIterator(TensorRef ref, int thread_idx)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:119</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_abd70e40965a6296adeeeb7ddcca7da90"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#abd70e40965a6296adeeeb7ddcca7da90">cutlass::epilogue::threadblock::SharedLoadIterator::kMinAlignment</a></div><div class="ttdeci">static int const kMinAlignment</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:78</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a5208835793dcd89f36ea65a8fcadf7e7"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a5208835793dcd89f36ea65a8fcadf7e7">cutlass::epilogue::threadblock::SharedLoadIterator::ConstTensorRef</a></div><div class="ttdeci">typename TensorRef::ConstTensorRef ConstTensorRef</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:70</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ad3c5c9466713f62a5191e720827f34da"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">cutlass::TensorRef&lt; Element, Layout &gt;::ConstTensorRef</a></div><div class="ttdeci">TensorRef&lt; typename platform::remove_const&lt; Element &gt;::type const, Layout &gt; ConstTensorRef</div><div class="ttdoc">TensorRef to constant data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:179</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a55f1d151cdd69269ac86c3567d1eff28"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a55f1d151cdd69269ac86c3567d1eff28">cutlass::epilogue::threadblock::SharedLoadIterator::ThreadMap</a></div><div class="ttdeci">ThreadMap_ ThreadMap</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:63</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_aa49e242b14b4f482bc6bdd082acfb576"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#aa49e242b14b4f482bc6bdd082acfb576">cutlass::layout::RowMajor::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:59</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_ae46b49371732c012f0b44399f39d8b0a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae46b49371732c012f0b44399f39d8b0a">cutlass::epilogue::threadblock::SharedLoadIterator::kAlignment</a></div><div class="ttdeci">static int const kAlignment</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:80</div></div>
<div class="ttc" id="matrix__shape_8h_html"><div class="ttname"><a href="matrix__shape_8h.html">matrix_shape.h</a></div><div class="ttdoc">Defines a Shape template for matrix tiles. </div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html">cutlass::sizeof_bits</a></div><div class="ttdoc">Defines the size of an element in bits. </div><div class="ttdef"><b>Definition:</b> numeric_types.h:42</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a813f447702209b6efdb1d4dea64efc2c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a813f447702209b6efdb1d4dea64efc2c">cutlass::epilogue::threadblock::SharedLoadIterator::AccessType</a></div><div class="ttdeci">AlignedArray&lt; Element, ThreadMap::kElementsPerAccess, kAlignment &gt; AccessType</div><div class="ttdoc">Memory access size. </div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:97</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a82ecb0e54bbcb04364a1924167cbeb15"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a82ecb0e54bbcb04364a1924167cbeb15">cutlass::epilogue::threadblock::SharedLoadIterator::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:72</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="output__tile__thread__map_8h_html"><div class="ttname"><a href="output__tile__thread__map_8h.html">output_tile_thread_map.h</a></div><div class="ttdoc">Metaprogram for determining the mapping of output elements to threads for epilogue tiles...</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_acc9440f6242ee064cb6e48f9387d73ad"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#acc9440f6242ee064cb6e48f9387d73ad">cutlass::epilogue::threadblock::SharedLoadIterator::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment. </div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:189</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a961521dfce056013892c64a0ecd4ef2f"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a961521dfce056013892c64a0ecd4ef2f">cutlass::epilogue::threadblock::SharedLoadIterator::Element</a></div><div class="ttdeci">Element_ Element</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:66</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a996bdfe3c2ef7f1d3684ce9a71f3b31d"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a996bdfe3c2ef7f1d3684ce9a71f3b31d">cutlass::epilogue::threadblock::SharedLoadIterator::Shape</a></div><div class="ttdeci">typename ThreadMap::Shape Shape</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:64</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a1e022ec8d968520bfd0bc7ce51dcaf93"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a1e022ec8d968520bfd0bc7ce51dcaf93">cutlass::epilogue::threadblock::SharedLoadIterator::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:139</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_ae7cc22c50e7e00742c7430080d673dc8"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae7cc22c50e7e00742c7430080d673dc8">cutlass::epilogue::threadblock::SharedLoadIterator::kElementsPerAccess</a></div><div class="ttdeci">static int const kElementsPerAccess</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:76</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html">cutlass::epilogue::threadblock::SharedLoadIterator</a></div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:61</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_ae5daed317dfa5a63b781f4900454bcd3"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#ae5daed317dfa5a63b781f4900454bcd3">cutlass::epilogue::threadblock::SharedLoadIterator::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:73</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_a4706618cd7b040c01d23440574696417"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#a4706618cd7b040c01d23440574696417">cutlass::epilogue::threadblock::SharedLoadIterator::add_tile_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void add_tile_offset(TensorCoord const &amp;offset)</div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:144</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
