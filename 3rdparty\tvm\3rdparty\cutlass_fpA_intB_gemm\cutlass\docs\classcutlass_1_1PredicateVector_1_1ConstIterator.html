<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a></li><li class="navelem"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classcutlass_1_1PredicateVector_1_1ConstIterator-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator Class Reference<div class="ingroups"><a class="el" href="group__predicate__iterator__concept.html">Predicate Iterator Concept</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>An iterator implementing <a class="el" href="group__predicate__iterator__concept.html">Predicate Iterator Concept</a> enabling sequential read and write access to predicates.  
</p>

<p><code>#include &lt;<a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a1216aab9c567ec0d4232019008ef3ea7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a1216aab9c567ec0d4232019008ef3ea7">ConstIterator</a> (<a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> const &amp;it)</td></tr>
<tr class="memdesc:a1216aab9c567ec0d4232019008ef3ea7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="#a1216aab9c567ec0d4232019008ef3ea7">More...</a><br /></td></tr>
<tr class="separator:a1216aab9c567ec0d4232019008ef3ea7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb6749fd0f66f9442fa18fabbb3588e4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#abb6749fd0f66f9442fa18fabbb3588e4">ConstIterator</a> (<a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> const &amp;vec, int _start=0)</td></tr>
<tr class="memdesc:abb6749fd0f66f9442fa18fabbb3588e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs an iterator from a <a class="el" href="structcutlass_1_1PredicateVector.html" title="Statically sized array of bits implementing. ">PredicateVector</a>.  <a href="#abb6749fd0f66f9442fa18fabbb3588e4">More...</a><br /></td></tr>
<tr class="separator:abb6749fd0f66f9442fa18fabbb3588e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10ee4bb2f206432aa5ee1a83cb046b70"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a10ee4bb2f206432aa5ee1a83cb046b70">operator++</a> ()</td></tr>
<tr class="memdesc:a10ee4bb2f206432aa5ee1a83cb046b70"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pre-increment.  <a href="#a10ee4bb2f206432aa5ee1a83cb046b70">More...</a><br /></td></tr>
<tr class="separator:a10ee4bb2f206432aa5ee1a83cb046b70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ab6e127b815ec870abf80ecfa94963c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a1ab6e127b815ec870abf80ecfa94963c">operator+=</a> (int offset)</td></tr>
<tr class="memdesc:a1ab6e127b815ec870abf80ecfa94963c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Increment.  <a href="#a1ab6e127b815ec870abf80ecfa94963c">More...</a><br /></td></tr>
<tr class="separator:a1ab6e127b815ec870abf80ecfa94963c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2763012a9284e97650b14e20c5668286"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a2763012a9284e97650b14e20c5668286">operator--</a> ()</td></tr>
<tr class="memdesc:a2763012a9284e97650b14e20c5668286"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pre-decrement.  <a href="#a2763012a9284e97650b14e20c5668286">More...</a><br /></td></tr>
<tr class="separator:a2763012a9284e97650b14e20c5668286"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c178f795233f0212e89f5b522097d7b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a6c178f795233f0212e89f5b522097d7b">operator-=</a> (int offset)</td></tr>
<tr class="memdesc:a6c178f795233f0212e89f5b522097d7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decrement.  <a href="#a6c178f795233f0212e89f5b522097d7b">More...</a><br /></td></tr>
<tr class="separator:a6c178f795233f0212e89f5b522097d7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a977a99af3166a58d5bc5a613a1abe7d5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a977a99af3166a58d5bc5a613a1abe7d5">operator++</a> (int)</td></tr>
<tr class="memdesc:a977a99af3166a58d5bc5a613a1abe7d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Post-increment.  <a href="#a977a99af3166a58d5bc5a613a1abe7d5">More...</a><br /></td></tr>
<tr class="separator:a977a99af3166a58d5bc5a613a1abe7d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2910a714d34a688b8ea560ea2933436b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a2910a714d34a688b8ea560ea2933436b">operator--</a> (int)</td></tr>
<tr class="memdesc:a2910a714d34a688b8ea560ea2933436b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Post-decrement.  <a href="#a2910a714d34a688b8ea560ea2933436b">More...</a><br /></td></tr>
<tr class="separator:a2910a714d34a688b8ea560ea2933436b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c7811b73f6e0d80fca977b412d466c8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a8c7811b73f6e0d80fca977b412d466c8">operator+</a> (int offset)</td></tr>
<tr class="memdesc:a8c7811b73f6e0d80fca977b412d466c8"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">Iterator</a> advances by some amount.  <a href="#a8c7811b73f6e0d80fca977b412d466c8">More...</a><br /></td></tr>
<tr class="separator:a8c7811b73f6e0d80fca977b412d466c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d9ebd49895cd9d218118edfdc3350d7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a4d9ebd49895cd9d218118edfdc3350d7">operator-</a> (int offset)</td></tr>
<tr class="memdesc:a4d9ebd49895cd9d218118edfdc3350d7"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">Iterator</a> recedes by some amount.  <a href="#a4d9ebd49895cd9d218118edfdc3350d7">More...</a><br /></td></tr>
<tr class="separator:a4d9ebd49895cd9d218118edfdc3350d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acce1de47d03b30131d36ca02327b77f6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#acce1de47d03b30131d36ca02327b77f6">operator==</a> (<a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> const &amp;it) const </td></tr>
<tr class="memdesc:acce1de47d03b30131d36ca02327b77f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if iterators point to the same bit.  <a href="#acce1de47d03b30131d36ca02327b77f6">More...</a><br /></td></tr>
<tr class="separator:acce1de47d03b30131d36ca02327b77f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ed3c88f8229d50b812f3cf151778ad9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a0ed3c88f8229d50b812f3cf151778ad9">operator!=</a> (<a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> const &amp;it) const </td></tr>
<tr class="memdesc:a0ed3c88f8229d50b812f3cf151778ad9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns false if iterators point to the same bit.  <a href="#a0ed3c88f8229d50b812f3cf151778ad9">More...</a><br /></td></tr>
<tr class="separator:a0ed3c88f8229d50b812f3cf151778ad9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b9b8f338a12fb3954ded0e5927c5318"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a5b9b8f338a12fb3954ded0e5927c5318">get</a> ()</td></tr>
<tr class="memdesc:a5b9b8f338a12fb3954ded0e5927c5318"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the bit at the pointed to location.  <a href="#a5b9b8f338a12fb3954ded0e5927c5318">More...</a><br /></td></tr>
<tr class="separator:a5b9b8f338a12fb3954ded0e5927c5318"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a691a6b129b56af221741b3825d58d2ac"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a691a6b129b56af221741b3825d58d2ac">at</a> () const </td></tr>
<tr class="memdesc:a691a6b129b56af221741b3825d58d2ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the bit at the pointed to location.  <a href="#a691a6b129b56af221741b3825d58d2ac">More...</a><br /></td></tr>
<tr class="separator:a691a6b129b56af221741b3825d58d2ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31fad0a6c23f15b0e7add2e130f5fa0b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a31fad0a6c23f15b0e7add2e130f5fa0b">operator*</a> () const </td></tr>
<tr class="memdesc:a31fad0a6c23f15b0e7add2e130f5fa0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dereferences iterator.  <a href="#a31fad0a6c23f15b0e7add2e130f5fa0b">More...</a><br /></td></tr>
<tr class="separator:a31fad0a6c23f15b0e7add2e130f5fa0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a1216aab9c567ec0d4232019008ef3ea7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::ConstIterator </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> const &amp;&#160;</td>
          <td class="paramname"><em>it</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abb6749fd0f66f9442fa18fabbb3588e4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::ConstIterator </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> const &amp;&#160;</td>
          <td class="paramname"><em>vec</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>_start</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a691a6b129b56af221741b3825d58d2ac"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::at </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5b9b8f338a12fb3954ded0e5927c5318"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::get </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0ed3c88f8229d50b812f3cf151778ad9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator!= </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> const &amp;&#160;</td>
          <td class="paramname"><em>it</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a31fad0a6c23f15b0e7add2e130f5fa0b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator* </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8c7811b73f6e0d80fca977b412d466c8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator+ </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a10ee4bb2f206432aa5ee1a83cb046b70"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator++ </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a977a99af3166a58d5bc5a613a1abe7d5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator++ </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1ab6e127b815ec870abf80ecfa94963c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator+= </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4d9ebd49895cd9d218118edfdc3350d7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator- </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2763012a9284e97650b14e20c5668286"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator-- </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2910a714d34a688b8ea560ea2933436b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator-- </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6c178f795233f0212e89f5b522097d7b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator-= </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>offset</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acce1de47d03b30131d36ca02327b77f6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator::operator== </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html">ConstIterator</a> const &amp;&#160;</td>
          <td class="paramname"><em>it</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
