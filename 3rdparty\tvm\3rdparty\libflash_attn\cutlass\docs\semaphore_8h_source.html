<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: semaphore.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">semaphore.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="semaphore_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="aligned__buffer_8h.html">cutlass/aligned_buffer.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__shape_8h.html">cutlass/matrix_shape.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html">   48</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1Semaphore.html">Semaphore</a> { </div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#aa68a93d7f787b8c1d1805c070b1e7249">   51</a></span>&#160;  <span class="keywordtype">int</span> *<a class="code" href="classcutlass_1_1Semaphore.html#aa68a93d7f787b8c1d1805c070b1e7249">lock</a>;</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#a21537a5165a13fe4395465b442322104">   52</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1Semaphore.html#a21537a5165a13fe4395465b442322104">wait_thread</a>;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#aed6e8af94d3811d15d5976859f42619d">   53</a></span>&#160;  <span class="keywordtype">int</span> <a class="code" href="classcutlass_1_1Semaphore.html#aed6e8af94d3811d15d5976859f42619d">state</a>;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#a2ce4cd07fe773efa429f726cfbd98070">   59</a></span>&#160;  <a class="code" href="classcutlass_1_1Semaphore.html#a2ce4cd07fe773efa429f726cfbd98070">Semaphore</a>(<span class="keywordtype">int</span> *lock_, <span class="keywordtype">int</span> thread_id): </div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    lock(lock_), </div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    wait_thread(thread_id &lt; 0 || thread_id == 0),</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    state(-1) {</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  }</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#af7e78f85e6106c1c82c10bee0b76d454">   68</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1Semaphore.html#af7e78f85e6106c1c82c10bee0b76d454">fetch</a>() {</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    <span class="keyword">asm</span> <span class="keyword">volatile</span> (<span class="stringliteral">&quot;ld.global.cg.s32 %0, [%1];\n&quot;</span> : <span class="stringliteral">&quot;=r&quot;</span>(<a class="code" href="classcutlass_1_1Semaphore.html#aed6e8af94d3811d15d5976859f42619d">state</a>) : <span class="stringliteral">&quot;l&quot;</span>(lock));</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  }</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#a4ddbd78190c1342d2f964ad2ce18b59e">   75</a></span>&#160;  <span class="keywordtype">int</span> <a class="code" href="classcutlass_1_1Semaphore.html#a4ddbd78190c1342d2f964ad2ce18b59e">get_state</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1Semaphore.html#aed6e8af94d3811d15d5976859f42619d">state</a>;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  }</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#a176a4cbf65e47e9fcba9d93fc264b9c3">   81</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1Semaphore.html#a176a4cbf65e47e9fcba9d93fc264b9c3">wait</a>(<span class="keywordtype">int</span> status = 0) {</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    <span class="keywordflow">if</span> (wait_thread) {</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;      <span class="keywordflow">while</span> (state != status) {</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        <a class="code" href="classcutlass_1_1Semaphore.html#af7e78f85e6106c1c82c10bee0b76d454">fetch</a>();</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        __syncwarp(0x01);</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      };</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    }</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    __syncthreads();</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  }</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classcutlass_1_1Semaphore.html#a04e893ba5a9ddb20e1b3c6475771c0e9">   98</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1Semaphore.html#a04e893ba5a9ddb20e1b3c6475771c0e9">release</a>(<span class="keywordtype">int</span> status = 0) {</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    __syncthreads();</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    <span class="keywordflow">if</span> (wait_thread) {</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;      <span class="keyword">asm</span> <span class="keyword">volatile</span> (<span class="stringliteral">&quot;st.global.cg.s32 [%0], %1;\n&quot;</span> : : <span class="stringliteral">&quot;l&quot;</span>(<a class="code" href="classcutlass_1_1Semaphore.html#aa68a93d7f787b8c1d1805c070b1e7249">lock</a>), <span class="stringliteral">&quot;r&quot;</span>(status));</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    }</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  }</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;};</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_a2ce4cd07fe773efa429f726cfbd98070"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#a2ce4cd07fe773efa429f726cfbd98070">cutlass::Semaphore::Semaphore</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Semaphore(int *lock_, int thread_id)</div><div class="ttdoc">Implements a semaphore to wait for a flag to reach a given value. </div><div class="ttdef"><b>Definition:</b> semaphore.h:59</div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_af7e78f85e6106c1c82c10bee0b76d454"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#af7e78f85e6106c1c82c10bee0b76d454">cutlass::Semaphore::fetch</a></div><div class="ttdeci">CUTLASS_DEVICE void fetch()</div><div class="ttdoc">Permit fetching the synchronization mechanism early. </div><div class="ttdef"><b>Definition:</b> semaphore.h:68</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="matrix__shape_8h_html"><div class="ttname"><a href="matrix__shape_8h.html">matrix_shape.h</a></div><div class="ttdoc">Defines a Shape template for matrix tiles. </div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_a4ddbd78190c1342d2f964ad2ce18b59e"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#a4ddbd78190c1342d2f964ad2ce18b59e">cutlass::Semaphore::get_state</a></div><div class="ttdeci">CUTLASS_DEVICE int get_state() const </div><div class="ttdoc">Gets the internal state. </div><div class="ttdef"><b>Definition:</b> semaphore.h:75</div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_a21537a5165a13fe4395465b442322104"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#a21537a5165a13fe4395465b442322104">cutlass::Semaphore::wait_thread</a></div><div class="ttdeci">bool wait_thread</div><div class="ttdef"><b>Definition:</b> semaphore.h:52</div></div>
<div class="ttc" id="aligned__buffer_8h_html"><div class="ttname"><a href="aligned__buffer_8h.html">aligned_buffer.h</a></div><div class="ttdoc">AlignedBuffer is a container for trivially copyable elements suitable for use in unions and shared me...</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html"><div class="ttname"><a href="classcutlass_1_1Semaphore.html">cutlass::Semaphore</a></div><div class="ttdoc">CTA-wide semaphore for inter-CTA synchronization. </div><div class="ttdef"><b>Definition:</b> semaphore.h:48</div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_a04e893ba5a9ddb20e1b3c6475771c0e9"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#a04e893ba5a9ddb20e1b3c6475771c0e9">cutlass::Semaphore::release</a></div><div class="ttdeci">CUTLASS_DEVICE void release(int status=0)</div><div class="ttdoc">Updates the lock with the given result. </div><div class="ttdef"><b>Definition:</b> semaphore.h:98</div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_a176a4cbf65e47e9fcba9d93fc264b9c3"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#a176a4cbf65e47e9fcba9d93fc264b9c3">cutlass::Semaphore::wait</a></div><div class="ttdeci">CUTLASS_DEVICE void wait(int status=0)</div><div class="ttdoc">Waits until the semaphore is equal to the given value. </div><div class="ttdef"><b>Definition:</b> semaphore.h:81</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_aed6e8af94d3811d15d5976859f42619d"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#aed6e8af94d3811d15d5976859f42619d">cutlass::Semaphore::state</a></div><div class="ttdeci">int state</div><div class="ttdef"><b>Definition:</b> semaphore.h:53</div></div>
<div class="ttc" id="classcutlass_1_1Semaphore_html_aa68a93d7f787b8c1d1805c070b1e7249"><div class="ttname"><a href="classcutlass_1_1Semaphore.html#aa68a93d7f787b8c1d1805c070b1e7249">cutlass::Semaphore::lock</a></div><div class="ttdeci">int * lock</div><div class="ttdef"><b>Definition:</b> semaphore.h:51</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
