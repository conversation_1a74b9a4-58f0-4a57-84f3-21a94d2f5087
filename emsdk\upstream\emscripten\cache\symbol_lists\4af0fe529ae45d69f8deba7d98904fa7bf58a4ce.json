{"deps": {"$noExitRuntime": [], "$getValue": [], "$setValue": [], "$ExitStatus": [], "$stackSave": ["emscripten_stack_get_current"], "$stackRestore": ["_emscripten_stack_restore"], "$addOnPreRun": [], "$addOnPostRun": [], "$callRuntimeCallbacks": [], "$writeI53ToI64": [], "$writeI53ToI64Clamped": [], "$writeI53ToI64Signaling": [], "$writeI53ToU64Clamped": [], "$writeI53ToU64Signaling": [], "$readI53FromI64": [], "$readI53FromU64": [], "$convertI32PairToI53": [], "$convertI32PairToI53Checked": [], "$convertU32PairToI53": [], "$INT53_MAX": [], "$INT53_MIN": [], "$bigintToI53Checked": [], "$stackAlloc": ["_emscripten_stack_alloc"], "$getTempRet0": ["_emscripten_tempret_get"], "$setTempRet0": ["_emscripten_tempret_set"], "stackAlloc": ["_emscripten_stack_alloc"], "stackSave": ["emscripten_stack_get_current"], "stackRestore": ["emscripten_stack_get_current"], "setTempRet0": ["_emscripten_tempret_set"], "getTempRet0": ["_emscripten_tempret_get"], "$ptrToString": [], "$zeroMemory": [], "$exitJS": [], "exit": [], "emscripten_get_heap_max": [], "$getHeapMax": [], "$growMemory": [], "emscripten_resize_heap": [], "emscripten_notify_memory_growth": [], "_emscripten_system": [], "$ENV": [], "$withStackSave": ["_emscripten_stack_restore", "emscripten_stack_get_current"], "_emscripten_throw_longjmp": ["setThrew"], "$ERRNO_CODES": [], "$strError": ["strerror"], "$inetPton4": [], "$inetNtop4": [], "$inetPton6": ["htons"], "$inetNtop6": ["ntohs"], "$readSockaddr": ["ntohs"], "$writeSockaddr": ["htons"], "$DNS": ["htons"], "_emscripten_lookup_name": ["htons"], "getaddrinfo": ["malloc", "htonl", "htons", "ntohs"], "getnameinfo": ["ntohs", "htons"], "$Protocols": [], "setprotoent": ["malloc"], "endprotoent": [], "getprotoent": ["malloc"], "getprotobyname": ["malloc"], "getprotobynumber": ["malloc"], "$Sockets": [], "$timers": [], "_setitimer_js": ["_emscripten_timeout"], "__call_sighandler": [], "emscripten_run_script": [], "emscripten_run_script_int": [], "emscripten_run_script_string": ["realloc"], "emscripten_random": [], "emscripten_date_now": [], "emscripten_performance_now": [], "emscripten_get_now": [], "emscripten_get_now_res": [], "$nowIsMonotonic": [], "_emscripten_get_now_is_monotonic": [], "$warnOnce": [], "$emscriptenLog": [], "emscripten_log": [], "emscripten_get_compiler_setting": [], "emscripten_has_asyncify": [], "emscripten_debugger": [], "emscripten_print_double": [], "$readEmAsmArgsArray": [], "$readEmAsmArgs": [], "$runEmAsmFunction": [], "emscripten_asm_const_int": [], "emscripten_asm_const_double": [], "emscripten_asm_const_ptr": [], "$runMainThreadEmAsm": [], "emscripten_asm_const_int_sync_on_main_thread": [], "emscripten_asm_const_ptr_sync_on_main_thread": [], "emscripten_asm_const_double_sync_on_main_thread": [], "emscripten_asm_const_async_on_main_thread": [], "$jstoi_q": [], "_Unwind_Backtrace": [], "_Unwind_GetIPInfo": [], "_Unwind_FindEnclosingFunction": [], "_Unwind_RaiseException": [], "_Unwind_DeleteException": [], "$getExecutableName": [], "$autoResumeAudioContext": [], "$getDynCaller": [], "$dynCall": [], "$wasmTableMirror": [], "$setWasmTableEntry": [], "$getWasmTableEntry": [], "emscripten_exit_with_live_runtime": [], "_emscripten_runtime_keepalive_clear": [], "emscripten_force_exit": [], "emscripten_out": [], "emscripten_outn": [], "emscripten_err": [], "emscripten_errn": [], "_emscripten_get_progname": [], "emscripten_console_log": [], "emscripten_console_warn": [], "emscripten_console_error": [], "emscripten_console_trace": [], "emscripten_throw_number": [], "emscripten_throw_string": [], "$handleException": [], "$runtimeKeepaliveCounter": [], "$keepRuntimeAlive": [], "$runtimeKeepalivePush": [], "$runtimeKeepalivePop": [], "emscripten_runtime_keepalive_push": [], "emscripten_runtime_keepalive_pop": [], "emscripten_runtime_keepalive_check": [], "$callUserCallback": [], "$maybeExit": [], "$asmjsMangle": [], "$asyncLoad": [], "$alignMemory": [], "$mmapAlloc": [], "_emscripten_fs_load_embedded_files": [], "$HandleAllocator": [], "$getNativeTypeSize": [], "$wasmTable": [], "$getUniqueRunDependency": [], "$onPreRuns": [], "$onInits": [], "$addOnInit": [], "$onPostCtors": [], "$addOnPostCtor": [], "$onMains": [], "$addOnPreMain": [], "$onExits": [], "$addOnExit": [], "$onPostRuns": [], "$STACK_SIZE": [], "$STACK_ALIGN": [], "$POINTER_SIZE": [], "$ASSERTIONS": [], "$getCFunc": [], "$ccall": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$cwrap": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$uleb128Encode": [], "$sigToWasmTypes": [], "$generateFuncType": [], "$convertJsFunctionToWasm": [], "$freeTableIndexes": [], "$functionsInTableMap": [], "$getEmptyTableSlot": [], "$updateTableMap": [], "$getFunctionAddress": [], "$addFunction": [], "$removeFunction": [], "$reallyNegative": [], "$unSign": [], "$strLen": [], "$reSign": [], "$formatString": [], "emscripten_math_cbrt": [], "emscripten_math_pow": [], "emscripten_math_random": [], "emscripten_math_sign": [], "emscripten_math_sqrt": [], "emscripten_math_exp": [], "emscripten_math_expm1": [], "emscripten_math_fmod": [], "emscripten_math_log": [], "emscripten_math_log1p": [], "emscripten_math_log10": [], "emscripten_math_log2": [], "emscripten_math_round": [], "emscripten_math_acos": [], "emscripten_math_acosh": [], "emscripten_math_asin": [], "emscripten_math_asinh": [], "emscripten_math_atan": [], "emscripten_math_atanh": [], "emscripten_math_atan2": [], "emscripten_math_cos": [], "emscripten_math_cosh": [], "emscripten_math_hypot": [], "emscripten_math_sin": [], "emscripten_math_sinh": [], "emscripten_math_tan": [], "emscripten_math_tanh": [], "$PATH": [], "$PATH_FS": [], "$UTF8Decoder": [], "$UTF8ArrayToString": [], "$UTF8ToString": [], "$stringToUTF8Array": [], "$stringToUTF8": [], "$lengthBytesUTF8": [], "$intArrayFromString": [], "$intArrayToString": [], "$AsciiToString": [], "$stringToAscii": [], "$UTF16Decoder": [], "$UTF16ToString": [], "$stringToUTF16": [], "$lengthBytesUTF16": [], "$UTF32ToString": [], "$stringToUTF32": [], "$lengthBytesUTF32": [], "$stringToNewUTF8": ["malloc"], "$stringToUTF8OnStack": ["_emscripten_stack_alloc"], "$writeArrayToMemory": [], "$JSEvents": [], "$registerKeyEventCallback": ["malloc"], "$specialHTMLTargets": [], "$maybeCStringToJsString": [], "$findEventTarget": [], "$findCanvasEventTarget": [], "emscripten_set_keypress_callback_on_thread": ["malloc"], "emscripten_set_keydown_callback_on_thread": ["malloc"], "emscripten_set_keyup_callback_on_thread": ["malloc"], "$getBoundingClientRect": [], "$fillMouseEventData": [], "$registerMouseEventCallback": ["malloc"], "emscripten_set_click_callback_on_thread": ["malloc"], "emscripten_set_mousedown_callback_on_thread": ["malloc"], "emscripten_set_mouseup_callback_on_thread": ["malloc"], "emscripten_set_dblclick_callback_on_thread": ["malloc"], "emscripten_set_mousemove_callback_on_thread": ["malloc"], "emscripten_set_mouseenter_callback_on_thread": ["malloc"], "emscripten_set_mouseleave_callback_on_thread": ["malloc"], "emscripten_set_mouseover_callback_on_thread": ["malloc"], "emscripten_set_mouseout_callback_on_thread": ["malloc"], "emscripten_get_mouse_status": [], "$registerWheelEventCallback": ["malloc"], "emscripten_set_wheel_callback_on_thread": ["malloc"], "$registerUiEventCallback": ["malloc"], "emscripten_set_resize_callback_on_thread": ["malloc"], "emscripten_set_scroll_callback_on_thread": ["malloc"], "$registerFocusEventCallback": ["malloc"], "emscripten_set_blur_callback_on_thread": ["malloc"], "emscripten_set_focus_callback_on_thread": ["malloc"], "emscripten_set_focusin_callback_on_thread": ["malloc"], "emscripten_set_focusout_callback_on_thread": ["malloc"], "$fillDeviceOrientationEventData": [], "$registerDeviceOrientationEventCallback": [], "emscripten_set_deviceorientation_callback_on_thread": [], "emscripten_get_deviceorientation_status": [], "$fillDeviceMotionEventData": [], "$registerDeviceMotionEventCallback": ["malloc"], "emscripten_set_devicemotion_callback_on_thread": ["malloc"], "emscripten_get_devicemotion_status": [], "$screenOrientation": [], "$fillOrientationChangeEventData": [], "$registerOrientationChangeEventCallback": ["malloc"], "emscripten_set_orientationchange_callback_on_thread": ["malloc"], "emscripten_get_orientation_status": [], "emscripten_lock_orientation": [], "emscripten_unlock_orientation": [], "$fillFullscreenChangeEventData": [], "$registerFullscreenChangeEventCallback": ["malloc"], "emscripten_set_fullscreenchange_callback_on_thread": ["malloc"], "emscripten_get_fullscreen_status": [], "$JSEvents_requestFullscreen": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$JSEvents_resizeCanvasForFullscreen": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$registerRestoreOldStyle": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$hideEverythingExceptGivenElement": [], "$restoreHiddenElements": [], "$setLetterbox": [], "$currentFullscreenStrategy": [], "$restoreOldWindowedStyle": [], "$softFullscreenResizeWebGLRenderTarget": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$doRequestFullscreen": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_request_fullscreen": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_request_fullscreen_strategy": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_enter_soft_fullscreen": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_exit_soft_fullscreen": [], "emscripten_exit_fullscreen": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$fillPointerlockChangeEventData": [], "$registerPointerlockChangeEventCallback": ["malloc"], "emscripten_set_pointerlockchange_callback_on_thread": ["malloc"], "$registerPointerlockErrorEventCallback": [], "emscripten_set_pointerlockerror_callback_on_thread": [], "emscripten_get_pointerlock_status": [], "$requestPointerLock": [], "emscripten_request_pointerlock": [], "emscripten_exit_pointerlock": [], "emscripten_vibrate": [], "emscripten_vibrate_pattern": [], "$fillVisibilityChangeEventData": [], "$registerVisibilityChangeEventCallback": ["malloc"], "emscripten_set_visibilitychange_callback_on_thread": ["malloc"], "emscripten_get_visibility_status": [], "$registerTouchEventCallback": ["malloc"], "emscripten_set_touchstart_callback_on_thread": ["malloc"], "emscripten_set_touchend_callback_on_thread": ["malloc"], "emscripten_set_touchmove_callback_on_thread": ["malloc"], "emscripten_set_touchcancel_callback_on_thread": ["malloc"], "$fillGamepadEventData": [], "$registerGamepadEventCallback": ["malloc"], "emscripten_set_gamepadconnected_callback_on_thread": ["malloc"], "emscripten_set_gamepaddisconnected_callback_on_thread": ["malloc"], "emscripten_sample_gamepad_data": [], "emscripten_get_num_gamepads": [], "emscripten_get_gamepad_status": [], "$registerBeforeUnloadEventCallback": [], "emscripten_set_beforeunload_callback_on_thread": [], "$fillBatteryEventData": [], "$battery": [], "$registerBatteryEventCallback": ["malloc"], "emscripten_set_batterychargingchange_callback_on_thread": ["malloc"], "emscripten_set_batterylevelchange_callback_on_thread": ["malloc"], "emscripten_get_battery_status": [], "emscripten_set_canvas_element_size": [], "$setCanvasElementSize": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_get_canvas_element_size": [], "$getCanvasElementSize": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_set_element_css_size": [], "emscripten_get_element_css_size": [], "emscripten_html5_remove_all_event_listeners": [], "emscripten_request_animation_frame": [], "emscripten_cancel_animation_frame": [], "emscripten_request_animation_frame_loop": [], "emscripten_get_device_pixel_ratio": [], "$jsStackTrace": [], "$getCallstack": [], "emscripten_get_callstack": [], "$convertFrameToPC": [], "emscripten_return_address": [], "$UNWIND_CACHE": [], "emscripten_stack_snapshot": [], "$saveInUnwindCache": [], "emscripten_stack_unwind_buffer": [], "emscripten_pc_get_function": [], "$convertPCtoSourceLocation": [], "emscripten_pc_get_file": ["free", "malloc"], "emscripten_pc_get_line": [], "emscripten_pc_get_column": [], "proc_exit": [], "sched_yield": [], "$getEnvStrings": [], "environ_sizes_get": [], "environ_get": [], "args_sizes_get": [], "args_get": [], "$checkWasiClock": [], "clock_time_get": [], "clock_res_get": [], "$doReadv": [], "$doWritev": [], "fd_write": [], "fd_pwrite": [], "fd_close": [], "fd_read": [], "fd_pread": [], "fd_seek": [], "$wasiRightsToMuslOFlags": [], "$wasiOFlagsToMuslOFlags": [], "fd_fdstat_get": [], "fd_sync": [], "$initRandomFill": [], "$randomFill": [], "random_get": [], "emscripten_unwind_to_js_event_loop": [], "$safeSetTimeout": [], "$setImmediateWrapped": [], "$safeRequestAnimationFrame": [], "$clearImmediateWrapped": [], "$emSetImmediate": [], "$emClearImmediate_deps": [], "$emClearImmediate": [], "emscripten_set_immediate": [], "emscripten_clear_immediate": [], "emscripten_set_immediate_loop": [], "emscripten_set_timeout": [], "emscripten_clear_timeout": [], "emscripten_set_timeout_loop": [], "emscripten_set_interval": [], "emscripten_clear_interval": [], "emscripten_async_call": [], "$registerPostMainLoop": [], "$registerPreMainLoop": [], "$MainLoop": [], "emscripten_get_main_loop_timing": [], "emscripten_set_main_loop_timing": [], "emscripten_set_main_loop": [], "$setMainLoop": [], "emscripten_set_main_loop_arg": [], "emscripten_cancel_main_loop": [], "emscripten_pause_main_loop": [], "emscripten_resume_main_loop": [], "_emscripten_push_main_loop_blocker": [], "_emscripten_push_uncounted_main_loop_blocker": [], "emscripten_set_main_loop_expected_blockers": [], "$promiseMap": [], "$getPromise": [], "$makePromise": [], "$idsToPromises": [], "emscripten_promise_create": [], "emscripten_promise_destroy": [], "emscripten_promise_resolve": [], "$makePromiseCallback": ["emscripten_stack_get_current", "_emscripten_stack_restore", "_emscripten_stack_alloc"], "emscripten_promise_then": ["emscripten_stack_get_current", "_emscripten_stack_restore", "_emscripten_stack_alloc"], "emscripten_promise_all": [], "$setPromiseResult": [], "emscripten_promise_all_settled": [], "emscripten_promise_any": [], "emscripten_promise_race": [], "emscripten_promise_await": [], "__cxa_find_matching_catch_2": ["__cxa_can_catch", "_emscripten_tempret_set"], "__cxa_find_matching_catch_3": ["__cxa_can_catch", "_emscripten_tempret_set"], "__cxa_find_matching_catch_4": ["__cxa_can_catch", "_emscripten_tempret_set"], "$uncaughtExceptionCount": [], "$exceptionLast": [], "$exceptionCaught": [], "$ExceptionInfo": [], "__cxa_throw": [], "__cxa_rethrow": [], "llvm_eh_typeid_for": [], "__cxa_begin_catch": ["__cxa_increment_exception_refcount", "__cxa_get_exception_ptr"], "__cxa_end_catch": ["__cxa_decrement_exception_refcount", "setThrew"], "__cxa_uncaught_exceptions": [], "__cxa_call_unexpected": [], "__cxa_current_primary_exception": ["__cxa_increment_exception_refcount"], "__cxa_current_exception_type": [], "__cxa_rethrow_primary_exception": [], "$findMatchingCatch": ["__cxa_can_catch", "_emscripten_tempret_set"], "__resumeException": [], "$Browser": [], "$requestFullscreen": [], "$setCanvasSize": [], "$getUserMedia": [], "$createContext": [], "emscripten_run_preload_plugins": [], "$Browser_asyncPrepareDataCounter": [], "emscripten_run_preload_plugins_data": ["malloc"], "emscripten_async_run_script": [], "emscripten_async_load_script": [], "emscripten_get_window_title": [], "emscripten_set_window_title": [], "emscripten_get_screen_size": [], "emscripten_hide_mouse": [], "emscripten_set_canvas_size": [], "emscripten_get_canvas_size": [], "emscripten_create_worker": ["realloc"], "emscripten_destroy_worker": ["free"], "emscripten_call_worker": [], "emscripten_get_worker_queue_size": [], "emscripten_get_preloaded_image_data": [], "$getPreloadedImageData__data": [], "$getPreloadedImageData": [], "emscripten_get_preloaded_image_data_from_FILE": ["fileno"], "$wget": [], "emscripten_async_wget": ["_emscripten_stack_alloc", "_emscripten_stack_restore"], "emscripten_async_wget_data": ["malloc", "free"], "emscripten_async_wget2": ["_emscripten_stack_alloc", "_emscripten_stack_restore"], "emscripten_async_wget2_data": ["malloc", "free"], "emscripten_async_wget2_abort": [], "$SYSCALLS": [], "$syscallGetVarargI": [], "$syscallGetVarargP": [], "_mmap_js": ["emscripten_builtin_memalign"], "_munmap_js": [], "__syscall_chdir": [], "__syscall_chmod": [], "__syscall_rmdir": [], "__syscall_dup": [], "__syscall_pipe": [], "__syscall_ioctl": [], "__syscall_fchmod": [], "$getSocketFromFD": [], "$getSocketAddress": ["htons", "ntohs"], "__syscall_socket": [], "__syscall_getsockname": ["htons"], "__syscall_getpeername": ["htons"], "__syscall_connect": ["htons", "ntohs"], "__syscall_shutdown": [], "__syscall_accept4": ["htons"], "__syscall_bind": ["htons", "ntohs"], "__syscall_listen": [], "__syscall_recvfrom": ["htons"], "__syscall_sendto": ["htons", "ntohs"], "__syscall_getsockopt": [], "__syscall_sendmsg": ["htons", "ntohs"], "__syscall_recvmsg": ["htons"], "__syscall_fchdir": [], "__syscall__newselect": [], "_msync_js": [], "__syscall_fdatasync": [], "__syscall_poll": [], "__syscall_getcwd": [], "__syscall_truncate64": [], "__syscall_ftruncate64": [], "__syscall_stat64": [], "__syscall_lstat64": [], "__syscall_fstat64": [], "__syscall_fchown32": [], "__syscall_getdents64": [], "__syscall_fcntl64": [], "__syscall_statfs64": [], "__syscall_fstatfs64": [], "__syscall_fadvise64": [], "__syscall_openat": [], "__syscall_mkdirat": [], "__syscall_mknodat": [], "__syscall_fchownat": [], "__syscall_newfstatat": [], "__syscall_unlinkat": [], "__syscall_renameat": [], "__syscall_symlinkat": [], "__syscall_readlinkat": [], "__syscall_fchmodat2": [], "__syscall_faccessat": [], "__syscall_utimensat": [], "__syscall_fallocate": [], "__syscall_dup3": [], "$preloadPlugins": [], "$FS_handledByPreloadPlugin": [], "$FS_createPreloadedFile": [], "$FS_modeStringToFlags": [], "$FS_getMode": [], "$FS_stdin_getChar_buffer": [], "$FS_stdin_getChar": [], "$FS_unlink": [], "$FS_createPath": [], "$FS_createDevice": [], "$FS_readFile": [], "$FS": [], "$FS_mkdirTree": [], "$FS_root": [], "$FS_mounts": [], "$FS_devices": [], "$FS_streams": [], "$FS_nextInode": [], "$FS_nameTable": [], "$FS_currentPath": [], "$FS_initialized": [], "$FS_ignorePermissions": [], "$FS_filesystems": [], "$FS_syncFSRequests": [], "$FS_readFiles": [], "$FS_lookupPath": [], "$FS_getPath": [], "$FS_hashName": [], "$FS_hashAddNode": [], "$FS_hashRemoveNode": [], "$FS_lookupNode": [], "$FS_createNode": [], "$FS_destroyNode": [], "$FS_isRoot": [], "$FS_isMountpoint": [], "$FS_isFile": [], "$FS_isDir": [], "$FS_isLink": [], "$FS_isChrdev": [], "$FS_isBlkdev": [], "$FS_isFIFO": [], "$FS_isSocket": [], "$FS_flagsToPermissionString": [], "$FS_nodePermissions": [], "$FS_mayLookup": [], "$FS_mayCreate": [], "$FS_mayDelete": [], "$FS_mayOpen": [], "$FS_checkOpExists": [], "$FS_nextfd": [], "$FS_getStreamChecked": [], "$FS_getStream": [], "$FS_createStream": [], "$FS_closeStream": [], "$FS_dupStream": [], "$FS_doSetAttr": [], "$FS_chrdev_stream_ops": [], "$FS_major": [], "$FS_minor": [], "$FS_makedev": [], "$FS_registerDevice": [], "$FS_getDevice": [], "$FS_getMounts": [], "$FS_syncfs": [], "$FS_mount": [], "$FS_unmount": [], "$FS_lookup": [], "$FS_mknod": [], "$FS_statfs": [], "$FS_statfsStream": [], "$FS_statfsNode": [], "$FS_create": [], "$FS_mkdir": [], "$FS_mkdev": [], "$FS_symlink": [], "$FS_rename": [], "$FS_rmdir": [], "$FS_readdir": [], "$FS_readlink": [], "$FS_stat": [], "$FS_fstat": [], "$FS_lstat": [], "$FS_doChmod": [], "$FS_chmod": [], "$FS_lchmod": [], "$FS_fchmod": [], "$FS_doChown": [], "$FS_chown": [], "$FS_lchown": [], "$FS_fchown": [], "$FS_doTruncate": [], "$FS_truncate": [], "$FS_ftruncate": [], "$FS_utime": [], "$FS_open": [], "$FS_close": [], "$FS_isClosed": [], "$FS_llseek": [], "$FS_read": [], "$FS_write": [], "$FS_mmap": [], "$FS_msync": [], "$FS_ioctl": [], "$FS_writeFile": [], "$FS_cwd": [], "$FS_chdir": [], "$FS_createDefaultDirectories": [], "$FS_createDefaultDevices": [], "$FS_createSpecialDirectories": [], "$FS_createStandardStreams": [], "$FS_staticInit": [], "$FS_init": [], "$FS_quit": [], "$FS_findObject": [], "$FS_analyzePath": [], "$FS_createFile": [], "$FS_createDataFile": [], "$FS_forceLoadFile": [], "$FS_createLazyFile": [], "$MEMFS": [], "$TTY": [], "$PIPEFS": [], "$SOCKFS": [], "$_setNetworkCallback": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_set_socket_error_callback": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_set_socket_open_callback": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_set_socket_listen_callback": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_set_socket_connection_callback": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_set_socket_message_callback": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "emscripten_set_socket_close_callback": ["_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$tempFixedLengthArray": [], "$miniTempWebGLFloatBuffers": [], "$miniTempWebGLIntBuffers": [], "$heapObjectForWebGLType": [], "$toTypedArrayIndex": [], "$webgl_enable_ANGLE_instanced_arrays": [], "emscripten_webgl_enable_ANGLE_instanced_arrays": [], "$webgl_enable_OES_vertex_array_object": [], "emscripten_webgl_enable_OES_vertex_array_object": [], "$webgl_enable_WEBGL_draw_buffers": [], "emscripten_webgl_enable_WEBGL_draw_buffers": [], "$webgl_enable_WEBGL_multi_draw": [], "emscripten_webgl_enable_WEBGL_multi_draw": [], "$webgl_enable_EXT_polygon_offset_clamp": [], "emscripten_webgl_enable_EXT_polygon_offset_clamp": [], "$webgl_enable_EXT_clip_control": [], "emscripten_webgl_enable_EXT_clip_control": [], "$webgl_enable_WEBGL_polygon_mode": [], "emscripten_webgl_enable_WEBGL_polygon_mode": [], "$getEmscriptenSupportedExtensions": [], "$GLctx": [], "$GL": [], "$webglGetExtensions": [], "glPixelStorei": [], "glGetString": ["malloc"], "$emscriptenWebGLGet": [], "glGetIntegerv": [], "glGetFloatv": [], "glGetBooleanv": [], "glDeleteTextures": [], "glCompressedTexImage2D": [], "glCompressedTexSubImage2D": [], "$computeUnpackAlignedImageSize": [], "$colorChannelsInGlTextureFormat": [], "$emscriptenWebGLGetTexPixelData": [], "glTexImage2D": [], "glTexSubImage2D": [], "glReadPixels": [], "glBindTexture": [], "glGetTexParameterfv": [], "glGetTexParameteriv": [], "glTexParameterfv": [], "glTexParameteriv": [], "glIsTexture": [], "glGenBuffers": [], "glGenTextures": [], "glDeleteBuffers": [], "glGetBufferParameteriv": [], "glBufferData": [], "glBufferSubData": [], "glGenQueriesEXT": [], "glDeleteQueriesEXT": [], "glIsQueryEXT": [], "glBeginQueryEXT": [], "glEndQueryEXT": [], "glQueryCounterEXT": [], "glGetQueryivEXT": [], "glGetQueryObjectivEXT": [], "glGetQueryObjectuivEXT": [], "glGetQueryObjecti64vEXT": [], "glGetQueryObjectui64vEXT": [], "glIsBuffer": [], "glGenRenderbuffers": [], "glDeleteRenderbuffers": [], "glBindRenderbuffer": [], "glGetRenderbufferParameteriv": [], "glIsRenderbuffer": [], "$emscriptenWebGLGetUniform": [], "glGetUniformfv": [], "glGetUniformiv": [], "$webglGetUniformLocation": [], "$webglPrepareUniformLocationsBeforeFirstUse": [], "$webglGetLeftBracePos": [], "glGetUniformLocation": [], "$emscriptenWebGLGetVertexAttrib": [], "glGetVertexAttribfv": [], "glGetVertexAttribiv": [], "glGetVertexAttribPointerv": [], "glUniform1f": [], "glUniform2f": [], "glUniform3f": [], "glUniform4f": [], "glUniform1i": [], "glUniform2i": [], "glUniform3i": [], "glUniform4i": [], "glUniform1iv": [], "glUniform2iv": [], "glUniform3iv": [], "glUniform4iv": [], "glUniform1fv": [], "glUniform2fv": [], "glUniform3fv": [], "glUniform4fv": [], "glUniformMatrix2fv": [], "glUniformMatrix3fv": [], "glUniformMatrix4fv": [], "glBindBuffer": [], "glVertexAttrib1fv": [], "glVertexAttrib2fv": [], "glVertexAttrib3fv": [], "glVertexAttrib4fv": [], "glGetAttribLocation": [], "$__glGetActiveAttribOrUniform": [], "glGetActiveAttrib": [], "glGetActiveUniform": [], "glCreateShader": [], "glDeleteShader": [], "glGetAttachedShaders": [], "glShaderSource": [], "glGetShaderSource": [], "glCompileShader": [], "glGetShaderInfoLog": [], "glGetShaderiv": [], "glGetProgramiv": [], "glIsShader": [], "glCreateProgram": [], "glDeleteProgram": [], "glAttachShader": [], "glDetachShader": [], "glGetShaderPrecisionFormat": [], "glLinkProgram": [], "glGetProgramInfoLog": [], "glUseProgram": [], "glValidateProgram": [], "glIsProgram": [], "glBindAttribLocation": [], "glBindFramebuffer": [], "glGenFramebuffers": [], "glDeleteFramebuffers": [], "glFramebufferRenderbuffer": [], "glFramebufferTexture2D": [], "glGetFramebufferAttachmentParameteriv": [], "glIsFramebuffer": [], "glGenVertexArrays": [], "glDeleteVertexArrays": [], "glBindVertexArray": [], "glIsVertexArray": [], "glVertexPointer": [], "glMatrixMode": [], "glBegin": [], "glLoadIdentity": [], "glGenVertexArraysOES": [], "glDeleteVertexArraysOES": [], "glBindVertexArrayOES": [], "glIsVertexArrayOES": [], "glVertexAttribPointer": [], "glEnableVertexAttribArray": [], "glDisableVertexAttribArray": [], "glDrawArrays": [], "glDrawElements": [], "glShaderBinary": [], "glReleaseShaderCompiler": [], "glGetError": [], "glVertexAttribDivisor": [], "glDrawArraysInstanced": [], "glDrawElementsInstanced": [], "glVertexAttribDivisorNV": [], "glDrawArraysInstancedNV": [], "glDrawElementsInstancedNV": [], "glVertexAttribDivisorEXT": [], "glDrawArraysInstancedEXT": [], "glDrawElementsInstancedEXT": [], "glVertexAttribDivisorARB": [], "glDrawArraysInstancedARB": [], "glDrawElementsInstancedARB": [], "glVertexAttribDivisorANGLE": [], "glDrawArraysInstancedANGLE": [], "glDrawElementsInstancedANGLE": [], "glDrawBuffers": [], "glDrawBuffersEXT": [], "glDrawBuffersWEBGL": [], "glColorMask": [], "glDepthMask": [], "glSampleCoverage": [], "glMultiDrawArrays": [], "glMultiDrawArraysANGLE": [], "glMultiDrawArraysWEBGL": [], "glMultiDrawArraysInstancedANGLE": [], "glMultiDrawArraysInstancedWEBGL": [], "glMultiDrawElements": [], "glMultiDrawElementsANGLE": [], "glMultiDrawElementsWEBGL": [], "glMultiDrawElementsInstancedANGLE": [], "glMultiDrawElementsInstancedWEBGL": [], "glPolygonOffsetClampEXT": [], "glClipControlEXT": [], "glPolygonModeWEBGL": [], "glFinish": [], "glFlush": [], "glClearDepth": [], "glClearDepthf": [], "glDepthFunc": [], "glEnable": [], "glDisable": [], "glFrontFace": [], "glCullFace": [], "glClear": [], "glLineWidth": [], "glClearStencil": [], "glStencilMask": [], "glCheckFramebufferStatus": [], "glGenerateMipmap": [], "glActiveTexture": [], "glBlendEquation": [], "glIsEnabled": [], "glBlendFunc": [], "glBlendEquationSeparate": [], "glDepthRange": [], "glDepthRangef": [], "glStencilMaskSeparate": [], "glHint": [], "glPolygonOffset": [], "glVertexAttrib1f": [], "glTexParameteri": [], "glTexParameterf": [], "glVertexAttrib2f": [], "glStencilFunc": [], "glStencilOp": [], "glViewport": [], "glClearColor": [], "glScissor": [], "glVertexAttrib3f": [], "glRenderbufferStorage": [], "glBlendFuncSeparate": [], "glBlendColor": [], "glStencilFuncSeparate": [], "glStencilOpSeparate": [], "glVertexAttrib4f": [], "glCopyTexImage2D": [], "glCopyTexSubImage2D": [], "emscripten_glPixelStorei": [], "emscripten_glGetString": ["malloc"], "emscripten_glGetIntegerv": [], "emscripten_glGetFloatv": [], "emscripten_glGetBooleanv": [], "emscripten_glDeleteTextures": [], "emscripten_glCompressedTexImage2D": [], "emscripten_glCompressedTexSubImage2D": [], "emscripten_glTexImage2D": [], "emscripten_glTexSubImage2D": [], "emscripten_glReadPixels": [], "emscripten_glBindTexture": [], "emscripten_glGetTexParameterfv": [], "emscripten_glGetTexParameteriv": [], "emscripten_glTexParameterfv": [], "emscripten_glTexParameteriv": [], "emscripten_glIsTexture": [], "emscripten_glGenBuffers": [], "emscripten_glGenTextures": [], "emscripten_glDeleteBuffers": [], "emscripten_glGetBufferParameteriv": [], "emscripten_glBufferData": [], "emscripten_glBufferSubData": [], "emscripten_glGenQueriesEXT": [], "emscripten_glDeleteQueriesEXT": [], "emscripten_glIsQueryEXT": [], "emscripten_glBeginQueryEXT": [], "emscripten_glEndQueryEXT": [], "emscripten_glQueryCounterEXT": [], "emscripten_glGetQueryivEXT": [], "emscripten_glGetQueryObjectivEXT": [], "emscripten_glGetQueryObjectuivEXT": [], "emscripten_glGetQueryObjecti64vEXT": [], "emscripten_glGetQueryObjectui64vEXT": [], "emscripten_glIsBuffer": [], "emscripten_glGenRenderbuffers": [], "emscripten_glDeleteRenderbuffers": [], "emscripten_glBindRenderbuffer": [], "emscripten_glGetRenderbufferParameteriv": [], "emscripten_glIsRenderbuffer": [], "emscripten_glGetUniformfv": [], "emscripten_glGetUniformiv": [], "emscripten_glGetUniformLocation": [], "emscripten_glGetVertexAttribfv": [], "emscripten_glGetVertexAttribiv": [], "emscripten_glGetVertexAttribPointerv": [], "emscripten_glUniform1f": [], "emscripten_glUniform2f": [], "emscripten_glUniform3f": [], "emscripten_glUniform4f": [], "emscripten_glUniform1i": [], "emscripten_glUniform2i": [], "emscripten_glUniform3i": [], "emscripten_glUniform4i": [], "emscripten_glUniform1iv": [], "emscripten_glUniform2iv": [], "emscripten_glUniform3iv": [], "emscripten_glUniform4iv": [], "emscripten_glUniform1fv": [], "emscripten_glUniform2fv": [], "emscripten_glUniform3fv": [], "emscripten_glUniform4fv": [], "emscripten_glUniformMatrix2fv": [], "emscripten_glUniformMatrix3fv": [], "emscripten_glUniformMatrix4fv": [], "emscripten_glBindBuffer": [], "emscripten_glVertexAttrib1fv": [], "emscripten_glVertexAttrib2fv": [], "emscripten_glVertexAttrib3fv": [], "emscripten_glVertexAttrib4fv": [], "emscripten_glGetAttribLocation": [], "emscripten_glGetActiveAttrib": [], "emscripten_glGetActiveUniform": [], "emscripten_glCreateShader": [], "emscripten_glDeleteShader": [], "emscripten_glGetAttachedShaders": [], "emscripten_glShaderSource": [], "emscripten_glGetShaderSource": [], "emscripten_glCompileShader": [], "emscripten_glGetShaderInfoLog": [], "emscripten_glGetShaderiv": [], "emscripten_glGetProgramiv": [], "emscripten_glIsShader": [], "emscripten_glCreateProgram": [], "emscripten_glDeleteProgram": [], "emscripten_glAttachShader": [], "emscripten_glDetachShader": [], "emscripten_glGetShaderPrecisionFormat": [], "emscripten_glLinkProgram": [], "emscripten_glGetProgramInfoLog": [], "emscripten_glUseProgram": [], "emscripten_glValidateProgram": [], "emscripten_glIsProgram": [], "emscripten_glBindAttribLocation": [], "emscripten_glBindFramebuffer": [], "emscripten_glGenFramebuffers": [], "emscripten_glDeleteFramebuffers": [], "emscripten_glFramebufferRenderbuffer": [], "emscripten_glFramebufferTexture2D": [], "emscripten_glGetFramebufferAttachmentParameteriv": [], "emscripten_glIsFramebuffer": [], "emscripten_glGenVertexArrays": [], "emscripten_glDeleteVertexArrays": [], "emscripten_glBindVertexArray": [], "emscripten_glIsVertexArray": [], "emscripten_glVertexPointer": [], "emscripten_glMatrixMode": [], "emscripten_glBegin": [], "emscripten_glLoadIdentity": [], "emscripten_glGenVertexArraysOES": [], "emscripten_glDeleteVertexArraysOES": [], "emscripten_glBindVertexArrayOES": [], "emscripten_glIsVertexArrayOES": [], "emscripten_glVertexAttribPointer": [], "emscripten_glEnableVertexAttribArray": [], "emscripten_glDisableVertexAttribArray": [], "emscripten_glDrawArrays": [], "emscripten_glDrawElements": [], "emscripten_glShaderBinary": [], "emscripten_glReleaseShaderCompiler": [], "emscripten_glGetError": [], "emscripten_glVertexAttribDivisor": [], "emscripten_glDrawArraysInstanced": [], "emscripten_glDrawElementsInstanced": [], "emscripten_glVertexAttribDivisorNV": [], "emscripten_glDrawArraysInstancedNV": [], "emscripten_glDrawElementsInstancedNV": [], "emscripten_glVertexAttribDivisorEXT": [], "emscripten_glDrawArraysInstancedEXT": [], "emscripten_glDrawElementsInstancedEXT": [], "emscripten_glVertexAttribDivisorARB": [], "emscripten_glDrawArraysInstancedARB": [], "emscripten_glDrawElementsInstancedARB": [], "emscripten_glVertexAttribDivisorANGLE": [], "emscripten_glDrawArraysInstancedANGLE": [], "emscripten_glDrawElementsInstancedANGLE": [], "emscripten_glDrawBuffers": [], "emscripten_glDrawBuffersEXT": [], "emscripten_glDrawBuffersWEBGL": [], "emscripten_glColorMask": [], "emscripten_glDepthMask": [], "emscripten_glSampleCoverage": [], "emscripten_glMultiDrawArrays": [], "emscripten_glMultiDrawArraysANGLE": [], "emscripten_glMultiDrawArraysWEBGL": [], "emscripten_glMultiDrawArraysInstancedANGLE": [], "emscripten_glMultiDrawArraysInstancedWEBGL": [], "emscripten_glMultiDrawElements": [], "emscripten_glMultiDrawElementsANGLE": [], "emscripten_glMultiDrawElementsWEBGL": [], "emscripten_glMultiDrawElementsInstancedANGLE": [], "emscripten_glMultiDrawElementsInstancedWEBGL": [], "emscripten_glPolygonOffsetClampEXT": [], "emscripten_glClipControlEXT": [], "emscripten_glPolygonModeWEBGL": [], "emscripten_glFinish": [], "emscripten_glFlush": [], "emscripten_glClearDepth": [], "emscripten_glClearDepthf": [], "emscripten_glDepthFunc": [], "emscripten_glEnable": [], "emscripten_glDisable": [], "emscripten_glFrontFace": [], "emscripten_glCullFace": [], "emscripten_glClear": [], "emscripten_glLineWidth": [], "emscripten_glClearStencil": [], "emscripten_glStencilMask": [], "emscripten_glCheckFramebufferStatus": [], "emscripten_glGenerateMipmap": [], "emscripten_glActiveTexture": [], "emscripten_glBlendEquation": [], "emscripten_glIsEnabled": [], "emscripten_glBlendFunc": [], "emscripten_glBlendEquationSeparate": [], "emscripten_glDepthRange": [], "emscripten_glDepthRangef": [], "emscripten_glStencilMaskSeparate": [], "emscripten_glHint": [], "emscripten_glPolygonOffset": [], "emscripten_glVertexAttrib1f": [], "emscripten_glTexParameteri": [], "emscripten_glTexParameterf": [], "emscripten_glVertexAttrib2f": [], "emscripten_glStencilFunc": [], "emscripten_glStencilOp": [], "emscripten_glViewport": [], "emscripten_glClearColor": [], "emscripten_glScissor": [], "emscripten_glVertexAttrib3f": [], "emscripten_glRenderbufferStorage": [], "emscripten_glBlendFuncSeparate": [], "emscripten_glBlendColor": [], "emscripten_glStencilFuncSeparate": [], "emscripten_glStencilOpSeparate": [], "emscripten_glVertexAttrib4f": [], "emscripten_glCopyTexImage2D": [], "emscripten_glCopyTexSubImage2D": [], "$writeGLArray": [], "$webglPowerPreferences": [], "emscripten_webgl_create_context": [], "emscripten_webgl_get_current_context": [], "emscripten_webgl_commit_frame": [], "emscripten_webgl_do_create_context": [], "emscripten_webgl_make_context_current": [], "emscripten_webgl_do_get_current_context": [], "emscripten_webgl_get_drawing_buffer_size": [], "emscripten_webgl_do_commit_frame": [], "emscripten_webgl_get_context_attributes": [], "emscripten_webgl_destroy_context": [], "emscripten_webgl_enable_extension": [], "emscripten_supports_offscreencanvas": [], "$registerWebGlEventCallback": [], "emscripten_set_webglcontextlost_callback_on_thread": [], "emscripten_set_webglcontextrestored_callback_on_thread": [], "emscripten_is_webgl_context_lost": [], "emscripten_webgl_get_supported_extensions": ["malloc"], "emscripten_webgl_get_program_parameter_d": [], "emscripten_webgl_get_program_info_log_utf8": ["malloc"], "emscripten_webgl_get_shader_parameter_d": [], "emscripten_webgl_get_shader_info_log_utf8": ["malloc"], "emscripten_webgl_get_shader_source_utf8": ["malloc"], "emscripten_webgl_get_vertex_attrib_d": [], "emscripten_webgl_get_vertex_attrib_o": [], "emscripten_webgl_get_vertex_attrib_v": [], "emscripten_webgl_get_uniform_d": [], "emscripten_webgl_get_uniform_v": [], "emscripten_webgl_get_parameter_v": [], "emscripten_webgl_get_parameter_d": [], "emscripten_webgl_get_parameter_o": [], "emscripten_webgl_get_parameter_utf8": ["malloc"], "emscripten_webgl_get_parameter_i64v": [], "$AL": [], "alcCaptureOpenDevice": [], "alcCaptureCloseDevice": [], "alcCaptureStart": [], "alcCaptureStop": [], "alcCaptureSamples": [], "alcOpenDevice": [], "alcCloseDevice": [], "alcCreateContext": [], "alcDestroyContext": [], "alcGetError": [], "alcGetCurrentContext": [], "alcMakeContextCurrent": [], "alcGetContextsDevice": [], "alcProcessContext": [], "alcSuspendContext": [], "alcIsExtensionPresent": [], "alcGetEnumValue": [], "alcGetString": ["malloc"], "alcGetIntegerv": [], "emscripten_alcDevicePauseSOFT": [], "emscripten_alcDeviceResumeSOFT": [], "emscripten_alcGetStringiSOFT": ["malloc"], "emscripten_alcResetDeviceSOFT": [], "alGenBuffers": [], "alDeleteBuffers": [], "alGenSources": [], "alDeleteSources": [], "alGetError": [], "alIsExtensionPresent": [], "alGetEnumValue": [], "alGetString": ["malloc"], "alEnable": [], "alDisable": [], "alIsEnabled": [], "alGetDouble": [], "alGetDoublev": [], "alGetFloat": [], "alGetFloatv": [], "alGetInteger": [], "alGetIntegerv": [], "alGetBoolean": [], "alGetBooleanv": [], "alDistanceModel": [], "alSpeedOfSound": [], "alDopplerFactor": [], "alDopplerVelocity": [], "alGetListenerf": [], "alGetListener3f": [], "alGetListenerfv": [], "alGetListeneri": [], "alGetListener3i": [], "alGetListeneriv": [], "alListenerf": [], "alListener3f": [], "alListenerfv": [], "alListeneri": [], "alListener3i": [], "alListeneriv": [], "alIsBuffer": [], "alBufferData": [], "alGetBufferf": [], "alGetBuffer3f": [], "alGetBufferfv": [], "alGetBufferi": [], "alGetBuffer3i": [], "alGetBufferiv": [], "alBufferf": [], "alBuffer3f": [], "alBufferfv": [], "alBufferi": [], "alBuffer3i": [], "alBufferiv": [], "alIsSource": [], "alSourceQueueBuffers": [], "alSourceUnqueueBuffers": [], "alSourcePlay": [], "alSourcePlayv": [], "alSourceStop": [], "alSourceStopv": [], "alSourceRewind": [], "alSourceRewindv": [], "alSourcePause": [], "alSourcePausev": [], "alGetSourcef": [], "alGetSource3f": [], "alGetSourcefv": [], "alGetSourcei": [], "alGetSource3i": [], "alGetSourceiv": [], "alSourcef": [], "alSource3f": [], "alSourcefv": [], "alSourcei": [], "alSource3i": [], "alSourceiv": [], "$GLUT": [], "glutGetModifiers": [], "glutInit": [], "glutInitWindowSize": [], "glutInitWindowPosition": [], "glutGet": [], "glutIdleFunc": [], "glutTimerFunc": [], "glutDisplayFunc": [], "glutKeyboardFunc": [], "glutKeyboardUpFunc": [], "glutSpecialFunc": [], "glutSpecialUpFunc": [], "glutReshapeFunc": [], "glutMotionFunc": [], "glutPassiveMotionFunc": [], "glutMouseFunc": [], "glutSetCursor": [], "glutCreateWindow": [], "glutDestroyWindow": [], "glutReshapeWindow": [], "glutPositionWindow": [], "glutFullScreen": [], "glutInitDisplayMode": [], "glutSwapBuffers": [], "glutPostRedisplay": [], "glutMainLoop": [], "XOpenDisplay": [], "XCreateWindow": [], "XChangeWindowAttributes": [], "XSetWMHints": [], "XMapWindow": [], "XStoreName": [], "XInternAtom": [], "XSendEvent": [], "XPending": [], "$EGL": [], "eglGetDisplay": [], "eglInitialize": [], "eglTerminate": [], "eglGetConfigs": [], "eglChooseConfig": [], "eglGetConfigAttrib": [], "eglCreateWindowSurface": [], "eglDestroySurface": [], "eglCreateContext": [], "eglDestroyContext": [], "eglQuerySurface": [], "eglQueryContext": [], "eglGetError": [], "eglQueryString": ["malloc"], "eglBindAPI": [], "eglQueryAPI": [], "eglWaitClient": [], "eglWaitNative": [], "eglWaitGL": [], "eglSwapInterval": [], "eglMakeCurrent": [], "eglGetCurrentContext": [], "eglGetCurrentSurface": [], "eglGetCurrentDisplay": [], "eglSwapBuffers": [], "eglReleaseThread": [], "uuid_clear": [], "uuid_compare": ["memcmp"], "uuid_copy": ["memcpy"], "uuid_generate": [], "uuid_is_null": [], "uuid_parse": [], "uuid_unparse": [], "uuid_unparse_lower": [], "uuid_unparse_upper": [], "uuid_type": [], "uuid_variant": [], "$GLEW": ["malloc"], "glewInit": ["malloc"], "glewIsSupported": ["malloc"], "glewGetExtension": ["malloc"], "glewGetErrorString": ["malloc"], "glewGetString": ["malloc"], "$IDBStore": [], "emscripten_idb_async_load": ["malloc", "free"], "emscripten_idb_async_store": [], "emscripten_idb_async_delete": [], "emscripten_idb_async_exists": [], "emscripten_idb_async_clear": [], "emscripten_idb_load": [], "emscripten_idb_store": [], "emscripten_idb_delete": [], "emscripten_idb_exists": [], "emscripten_idb_clear": [], "$runAndAbortIfError": [], "emscripten_sleep": [], "emscripten_wget": [], "emscripten_wget_data": [], "emscripten_scan_registers": [], "emscripten_fiber_swap": [], "$SDL": ["malloc", "free", "memcpy"], "SDL_Linked_Version": ["malloc", "free", "memcpy"], "SDL_Init": ["calloc", "malloc", "free", "memcpy"], "SDL_WasInit": ["malloc", "free", "memcpy", "calloc"], "SDL_GetVideoInfo": ["calloc", "malloc", "free", "memcpy"], "SDL_ListModes": ["malloc", "free", "memcpy"], "SDL_VideoModeOK": ["malloc", "free", "memcpy"], "SDL_AudioDriverName": ["malloc", "free", "memcpy"], "SDL_VideoDriverName": ["malloc", "free", "memcpy"], "SDL_SetVideoMode": ["malloc", "free", "memcpy"], "SDL_GetVideoSurface": ["malloc", "free", "memcpy"], "SDL_AudioQuit": ["malloc", "free", "memcpy"], "SDL_VideoQuit": ["malloc", "free", "memcpy"], "SDL_QuitSubSystem": ["malloc", "free", "memcpy"], "SDL_Quit": ["malloc", "free", "memcpy"], "SDL_LockSurface": ["malloc", "free", "memcpy"], "SDL_UnlockSurface": ["malloc", "free", "memcpy"], "SDL_Flip": ["malloc", "free", "memcpy"], "SDL_UpdateRect": ["malloc", "free", "memcpy"], "SDL_UpdateRects": ["malloc", "free", "memcpy"], "SDL_Delay": ["malloc", "free", "memcpy"], "SDL_WM_SetCaption": ["malloc", "free", "memcpy"], "SDL_EnableKeyRepeat": ["malloc", "free", "memcpy"], "SDL_GetKeyboardState": ["malloc", "free", "memcpy"], "SDL_GetKeyState": ["malloc", "free", "memcpy"], "SDL_GetKeyName": ["realloc", "malloc", "free", "memcpy"], "SDL_GetModState": ["malloc", "free", "memcpy"], "SDL_GetMouseState": ["malloc", "free", "memcpy"], "SDL_WarpMouse": ["malloc", "free", "memcpy"], "SDL_ShowCursor": ["malloc", "free", "memcpy"], "SDL_GetError": ["malloc", "free", "memcpy"], "SDL_SetError": ["malloc", "free", "memcpy"], "SDL_CreateRGBSurface": ["malloc", "free", "memcpy"], "SDL_CreateRGBSurfaceFrom": ["malloc", "free", "memcpy"], "SDL_ConvertSurface": ["malloc", "free", "memcpy"], "SDL_DisplayFormat": ["malloc", "free", "memcpy"], "SDL_DisplayFormatAlpha": ["malloc", "free", "memcpy"], "SDL_FreeSurface": ["malloc", "free", "memcpy"], "SDL_UpperBlit": ["malloc", "free", "memcpy"], "SDL_UpperBlitScaled": ["malloc", "free", "memcpy"], "SDL_LowerBlit": ["malloc", "free", "memcpy"], "SDL_LowerBlitScaled": ["malloc", "free", "memcpy"], "SDL_GetClipRect": ["malloc", "free", "memcpy"], "SDL_SetClipRect": ["malloc", "free", "memcpy"], "SDL_FillRect": ["malloc", "free", "memcpy"], "zoomSurface": ["malloc", "free", "memcpy"], "rotozoomSurface": ["malloc", "free", "memcpy"], "SDL_SetAlpha": ["malloc", "free", "memcpy"], "SDL_SetColorKey": ["malloc", "free", "memcpy"], "SDL_GetTicks": ["malloc", "free", "memcpy"], "SDL_PollEvent": ["malloc", "free", "memcpy"], "SDL_PushEvent": ["malloc", "free", "memcpy"], "SDL_PeepEvents": ["malloc", "free", "memcpy"], "SDL_PumpEvents": ["malloc", "free", "memcpy"], "emscripten_SDL_SetEventHandler": ["malloc", "free", "memcpy"], "SDL_SetColors": ["malloc", "free", "memcpy"], "SDL_SetPalette": ["malloc", "free", "memcpy"], "SDL_MapRGB": ["malloc", "free", "memcpy"], "SDL_MapRGBA": ["malloc", "free", "memcpy"], "SDL_GetRGB": ["malloc", "free", "memcpy"], "SDL_GetRGBA": ["malloc", "free", "memcpy"], "SDL_GetAppState": ["malloc", "free", "memcpy"], "SDL_WM_GrabInput": ["malloc", "free", "memcpy"], "SDL_WM_ToggleFullScreen": ["malloc", "free", "memcpy"], "IMG_Init": ["malloc", "free", "memcpy"], "IMG_Load_RW": ["malloc", "free", "memcpy", "_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "SDL_LoadBMP_RW": ["malloc", "free", "memcpy", "_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "IMG_Load": ["malloc", "free", "memcpy", "_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "IMG_Quit": ["malloc", "free", "memcpy"], "SDL_OpenAudio": ["malloc", "free", "memcpy"], "SDL_PauseAudio": ["malloc", "free", "memcpy"], "SDL_CloseAudio": ["malloc", "free", "memcpy"], "SDL_LockAudio": ["malloc", "free", "memcpy"], "SDL_UnlockAudio": ["malloc", "free", "memcpy"], "SDL_CreateMutex": ["malloc", "free", "memcpy"], "SDL_mutexP": ["malloc", "free", "memcpy"], "SDL_mutexV": ["malloc", "free", "memcpy"], "SDL_DestroyMutex": ["malloc", "free", "memcpy"], "SDL_CreateCond": ["malloc", "free", "memcpy"], "SDL_CondSignal": ["malloc", "free", "memcpy"], "SDL_CondWait": ["malloc", "free", "memcpy"], "SDL_DestroyCond": ["malloc", "free", "memcpy"], "SDL_StartTextInput": ["malloc", "free", "memcpy"], "SDL_StopTextInput": ["malloc", "free", "memcpy"], "Mix_Init": ["malloc", "free", "memcpy"], "Mix_Quit": ["malloc", "free", "memcpy"], "Mix_OpenAudio": ["malloc", "free", "memcpy"], "Mix_CloseAudio": ["malloc", "free", "memcpy"], "Mix_AllocateChannels": ["malloc", "free", "memcpy"], "Mix_ChannelFinished": ["malloc", "free", "memcpy"], "Mix_Volume": ["malloc", "free", "memcpy"], "Mix_SetPanning": ["malloc", "free", "memcpy"], "Mix_LoadWAV_RW": ["malloc", "free", "memcpy"], "Mix_LoadWAV": ["malloc", "free", "memcpy"], "Mix_QuickLoad_RAW": ["malloc", "free", "memcpy"], "Mix_FreeChunk": ["malloc", "free", "memcpy"], "Mix_ReserveChannels": ["malloc", "free", "memcpy"], "Mix_PlayChannelTimed": ["malloc", "free", "memcpy"], "Mix_FadingChannel": ["malloc", "free", "memcpy"], "Mix_HaltChannel": ["malloc", "free", "memcpy"], "Mix_HookMusicFinished": ["malloc", "free", "memcpy"], "Mix_VolumeMusic": ["malloc", "free", "memcpy"], "Mix_LoadMUS_RW": ["malloc", "free", "memcpy"], "Mix_LoadMUS": ["malloc", "free", "memcpy"], "Mix_FreeMusic": ["malloc", "free", "memcpy"], "Mix_PlayMusic": ["malloc", "free", "memcpy"], "Mix_PauseMusic": ["malloc", "free", "memcpy"], "Mix_ResumeMusic": ["malloc", "free", "memcpy"], "Mix_HaltMusic": ["malloc", "free", "memcpy"], "Mix_FadeInMusicPos": ["malloc", "free", "memcpy"], "Mix_FadeOutMusic": ["malloc", "free", "memcpy"], "Mix_PlayingMusic": ["malloc", "free", "memcpy"], "Mix_Playing": ["malloc", "free", "memcpy"], "Mix_Pause": ["malloc", "free", "memcpy"], "Mix_Paused": ["malloc", "free", "memcpy"], "Mix_PausedMusic": ["malloc", "free", "memcpy"], "Mix_Resume": ["malloc", "free", "memcpy"], "TTF_Init": ["malloc", "free", "memcpy"], "TTF_OpenFont": ["malloc", "free", "memcpy"], "TTF_CloseFont": ["malloc", "free", "memcpy"], "TTF_RenderText_Solid": ["malloc", "free", "memcpy"], "TTF_RenderText_Blended": ["malloc", "free", "memcpy"], "TTF_RenderText_Shaded": ["malloc", "free", "memcpy"], "TTF_RenderUTF8_Solid": ["malloc", "free", "memcpy"], "TTF_SizeUTF8": ["malloc", "free", "memcpy"], "TTF_SizeText": ["malloc", "free", "memcpy"], "TTF_GlyphMetrics": ["malloc", "free", "memcpy"], "TTF_FontAscent": ["malloc", "free", "memcpy"], "TTF_FontDescent": ["malloc", "free", "memcpy"], "TTF_FontHeight": ["malloc", "free", "memcpy"], "TTF_FontLineSkip": ["malloc", "free", "memcpy"], "TTF_Quit": ["malloc", "free", "memcpy"], "$SDL_gfx": ["malloc", "free", "memcpy"], "boxColor": ["malloc", "free", "memcpy"], "boxRGBA": ["malloc", "free", "memcpy"], "rectangleColor": ["malloc", "free", "memcpy"], "rectangleRGBA": ["malloc", "free", "memcpy"], "ellipseColor": ["malloc", "free", "memcpy"], "ellipseRGBA": ["malloc", "free", "memcpy"], "filledEllipseColor": ["malloc", "free", "memcpy"], "filledEllipseRGBA": ["malloc", "free", "memcpy"], "lineColor": ["malloc", "free", "memcpy"], "lineRGBA": ["malloc", "free", "memcpy"], "pixelRGBA": ["malloc", "free", "memcpy"], "SDL_GL_SetAttribute": ["malloc", "free", "memcpy"], "SDL_GL_GetAttribute": ["malloc", "free", "memcpy"], "SDL_GL_SwapBuffers": ["malloc", "free", "memcpy"], "SDL_GL_ExtensionSupported": ["malloc", "free", "memcpy"], "SDL_DestroyWindow": ["malloc", "free", "memcpy"], "SDL_DestroyRenderer": ["malloc", "free", "memcpy"], "SDL_GetWindowFlags": ["malloc", "free", "memcpy"], "SDL_GL_SwapWindow": ["malloc", "free", "memcpy"], "SDL_GL_MakeCurrent": ["malloc", "free", "memcpy"], "SDL_GL_DeleteContext": ["malloc", "free", "memcpy"], "SDL_GL_GetSwapInterval": ["malloc", "free", "memcpy"], "SDL_GL_SetSwapInterval": ["malloc", "free", "memcpy"], "SDL_SetWindowTitle": ["malloc", "free", "memcpy"], "SDL_GetWindowSize": ["malloc", "free", "memcpy"], "SDL_LogSetOutputFunction": ["malloc", "free", "memcpy"], "SDL_SetWindowFullscreen": ["malloc", "free", "memcpy"], "SDL_ClearError": ["malloc", "free", "memcpy"], "SDL_SetGamma": ["malloc", "free", "memcpy"], "SDL_SetGammaRamp": ["malloc", "free", "memcpy"], "SDL_NumJoysticks": ["malloc", "free", "memcpy"], "SDL_JoystickName": ["malloc", "free", "memcpy"], "SDL_JoystickOpen": ["malloc", "free", "memcpy"], "SDL_JoystickOpened": ["malloc", "free", "memcpy"], "SDL_JoystickIndex": ["malloc", "free", "memcpy"], "SDL_JoystickNumAxes": ["malloc", "free", "memcpy"], "SDL_JoystickNumBalls": ["malloc", "free", "memcpy"], "SDL_JoystickNumHats": ["malloc", "free", "memcpy"], "SDL_JoystickNumButtons": ["malloc", "free", "memcpy"], "SDL_JoystickUpdate": ["malloc", "free", "memcpy"], "SDL_JoystickEventState": ["malloc", "free", "memcpy"], "SDL_JoystickGetAxis": ["malloc", "free", "memcpy"], "SDL_JoystickGetHat": ["malloc", "free", "memcpy"], "SDL_JoystickGetBall": ["malloc", "free", "memcpy"], "SDL_JoystickGetButton": ["malloc", "free", "memcpy"], "SDL_JoystickClose": ["malloc", "free", "memcpy"], "SDL_InitSubSystem": ["malloc", "free", "memcpy"], "SDL_RWFromConstMem": ["malloc", "free", "memcpy"], "SDL_RWFromMem": ["malloc", "free", "memcpy"], "SDL_RWFromFile": ["malloc", "free", "memcpy"], "SDL_FreeRW": ["malloc", "free", "memcpy"], "SDL_GetNumAudioDrivers": ["malloc", "free", "memcpy"], "SDL_GetCurrentAudioDriver": ["malloc", "free", "memcpy"], "SDL_GetScancodeFromKey": ["malloc", "free", "memcpy"], "SDL_GetAudioDriver": ["malloc", "free", "memcpy"], "SDL_EnableUNICODE": ["malloc", "free", "memcpy"], "SDL_AddTimer": ["malloc", "free", "memcpy"], "SDL_RemoveTimer": ["malloc", "free", "memcpy"], "SDL_CreateThread": ["malloc", "free", "memcpy"], "SDL_WaitThread": ["malloc", "free", "memcpy"], "SDL_GetThreadID": ["malloc", "free", "memcpy"], "SDL_ThreadID": ["malloc", "free", "memcpy"], "SDL_AllocRW": ["malloc", "free", "memcpy"], "SDL_CondBroadcast": ["malloc", "free", "memcpy"], "SDL_CondWaitTimeout": ["malloc", "free", "memcpy"], "SDL_WM_IconifyWindow": ["malloc", "free", "memcpy"], "Mix_SetPostMix": ["malloc", "free", "memcpy"], "Mix_VolumeChunk": ["malloc", "free", "memcpy"], "Mix_SetPosition": ["malloc", "free", "memcpy"], "Mix_QuerySpec": ["malloc", "free", "memcpy"], "Mix_FadeInChannelTimed": ["malloc", "free", "memcpy"], "Mix_FadeOutChannel": ["malloc", "free", "memcpy"], "Mix_Linked_Version": ["malloc", "free", "memcpy"], "SDL_SaveBMP_RW": ["malloc", "free", "memcpy"], "SDL_WM_SetIcon": ["malloc", "free", "memcpy"], "SDL_HasRDTSC": ["malloc", "free", "memcpy"], "SDL_HasMMX": ["malloc", "free", "memcpy"], "SDL_HasMMXExt": ["malloc", "free", "memcpy"], "SDL_Has3DNow": ["malloc", "free", "memcpy"], "SDL_Has3DNowExt": ["malloc", "free", "memcpy"], "SDL_HasSSE": ["malloc", "free", "memcpy"], "SDL_HasSSE2": ["malloc", "free", "memcpy"], "SDL_HasAltiVec": ["malloc", "free", "memcpy"], "$ALLOC_NORMAL": [], "$ALLOC_STACK": [], "$allocate": ["malloc", "_emscripten_stack_alloc"], "$writeStringToMemory": [], "$writeAsciiToMemory": [], "$allocateUTF8": ["malloc"], "$allocateUTF8OnStack": ["_emscripten_stack_alloc"], "$demangle": ["__cxa_demangle", "free", "_emscripten_stack_alloc", "_emscripten_stack_restore", "emscripten_stack_get_current"], "$stackTrace": [], "$print": [], "$printErr": [], "$jstoi_s": [], "emscripten_is_main_browser_thread": []}, "asyncFuncs": [], "extraLibraryFuncs": []}