/*
Copyright Rene Rivera 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(MSGPACK_PREDEF_COMPILER_H) || defined(MSGPACK_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef MSGPACK_PREDEF_COMPILER_H
#define MSGPACK_PREDEF_COMPILER_H
#endif

#include <msgpack/predef/compiler/borland.h>
#include <msgpack/predef/compiler/clang.h>
#include <msgpack/predef/compiler/comeau.h>
#include <msgpack/predef/compiler/compaq.h>
#include <msgpack/predef/compiler/diab.h>
#include <msgpack/predef/compiler/digitalmars.h>
#include <msgpack/predef/compiler/dignus.h>
#include <msgpack/predef/compiler/edg.h>
#include <msgpack/predef/compiler/ekopath.h>
#include <msgpack/predef/compiler/gcc_xml.h>
#include <msgpack/predef/compiler/gcc.h>
#include <msgpack/predef/compiler/greenhills.h>
#include <msgpack/predef/compiler/hp_acc.h>
#include <msgpack/predef/compiler/iar.h>
#include <msgpack/predef/compiler/ibm.h>
#include <msgpack/predef/compiler/intel.h>
#include <msgpack/predef/compiler/kai.h>
#include <msgpack/predef/compiler/llvm.h>
#include <msgpack/predef/compiler/metaware.h>
#include <msgpack/predef/compiler/metrowerks.h>
#include <msgpack/predef/compiler/microtec.h>
#include <msgpack/predef/compiler/mpw.h>
#include <msgpack/predef/compiler/nvcc.h>
#include <msgpack/predef/compiler/palm.h>
#include <msgpack/predef/compiler/pgi.h>
#include <msgpack/predef/compiler/sgi_mipspro.h>
#include <msgpack/predef/compiler/sunpro.h>
#include <msgpack/predef/compiler/tendra.h>
#include <msgpack/predef/compiler/visualc.h>
#include <msgpack/predef/compiler/watcom.h>

#endif
