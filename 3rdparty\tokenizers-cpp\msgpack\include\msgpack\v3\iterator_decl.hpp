//
// MessagePack for C++ static resolution routine
//
// Copyright (C) 2018 <PERSON>OND<PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef MSGPACK_V3_ITERATOR_DECL_HPP
#define MSGPACK_V3_ITERATOR_DECL_HPP
#if !defined(MSGPACK_USE_CPP03)

#include "msgpack/v2/iterator_decl.hpp"

namespace msgpack {

/// @cond
MSGPACK_API_VERSION_NAMESPACE(v3) {
/// @endcond

using v2::begin;
using v2::end;

/// @cond
}
/// @endcond

}

#endif // !defined(MSGPACK_USE_CPP03)
#endif // MSGPACK_V3_ITERATOR_DECL_HPP
