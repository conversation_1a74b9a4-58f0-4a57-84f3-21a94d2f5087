//
// MessagePack for C++ deserializing routine
//
// Copyright (C) 2016 <PERSON><PERSON><PERSON><PERSON> Takatoshi
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_UNPACK_DECL_HPP
#define MSGPACK_UNPACK_DECL_HPP

#include "msgpack/v1/unpack_decl.hpp"
#include "msgpack/v2/unpack_decl.hpp"
#include "msgpack/v3/unpack_decl.hpp"

#endif // MSGPACK_UNPACK_DECL_HPP
