<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: batched_reduction_traits.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ac488927e63b76ba9cb3ad9c317bbde9.html">reduction</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">batched_reduction_traits.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="batched__reduction__traits_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">* Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">* Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">* provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">*     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">*       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">*     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">*       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">*       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">*     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">*       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">*       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">* FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">* FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment">* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment">* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment">* STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">* OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment">*</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment">**************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;cutlass/shape.h&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="reduction_2threadblock__swizzle_8h.html">cutlass/reduction/threadblock_swizzle.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="batched__reduction_8h.html">cutlass/reduction/batched_reduction.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;cutlass/gemm/linear_scaling.h&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">namespace </span>reduction {</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="comment">/*</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="comment">OutputTile defines the work load per thread block</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="comment">Subtile defines the work load per thread block per iteration</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="comment">OutputTile / Subtile = number of iterations within a kernel</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="comment">ThreadShape defines the work load per thread</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="comment">Subtile / ThreadShape = number of threads per thread block</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="comment">*/</span></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keyword">typename</span> ScalarA_,</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keyword">typename</span> ScalarC_,</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <span class="keyword">typename</span> ScalarD_,</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  <span class="keyword">typename</span> ScalarAlphaBeta_,</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  <span class="keyword">typename</span> ScalarAccum_,</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keywordtype">int</span> ReductionSize_ = 1,</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  <span class="keyword">typename</span> OutputTile_ = Shape&lt;1, 1, 128&gt;,</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> SubTile_ = Shape&lt;1, 1, 64&gt;,</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">typename</span> ThreadShape_ = Shape&lt;1, 1, 2&gt;,</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="keyword">typename</span> Index_ = int,</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keyword">typename</span> BlockSwizzle_ = DefaultBlockSwizzle,</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <span class="keywordtype">int</span> maxInReg_ = 160,</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <span class="keywordtype">int</span> maxOutReg_ = 64,</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="keyword">typename</span> Functor_ = <span class="keyword">typename</span> cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;&gt;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">   76</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">BatchedReductionTraits</a> {</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="keyword">typedef</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">BatchedReductionTraits</a>&lt;ScalarA_,</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    ScalarC_,</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    ScalarD_,</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    ScalarAlphaBeta_,</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    ScalarAccum_,</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    ReductionSize_,</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    OutputTile_,</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    SubTile_,</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    ThreadShape_,</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    Index_,</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    BlockSwizzle_,</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    maxInReg_,</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    maxOutReg_,</div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#acfeed4cb4e5eec9d8e1ae2b787cc88e2">   91</a></span>&#160;    Functor_&gt; <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#acfeed4cb4e5eec9d8e1ae2b787cc88e2">This_</a>;</div><div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a085c72d54426f5eb60f5bffa9c383229">   93</a></span>&#160;  <span class="keyword">typedef</span> <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReduction.html">cutlass::reduction::BatchedReduction&lt;This_&gt;</a> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a085c72d54426f5eb60f5bffa9c383229">KernelClass</a>;</div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a3b0e98519b7e551ef6adac69e7406d8f">   95</a></span>&#160;  <span class="keyword">typedef</span> OutputTile_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a3b0e98519b7e551ef6adac69e7406d8f">OutputTile</a>;</div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ad9d4dcb20a69869ebda639747a16e647">   97</a></span>&#160;  <span class="keyword">typedef</span> SubTile_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ad9d4dcb20a69869ebda639747a16e647">SubTile</a>;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a29e8adec50bc7e5c0a91f975dff4a7f3">   99</a></span>&#160;  <span class="keyword">typedef</span> ThreadShape_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a29e8adec50bc7e5c0a91f975dff4a7f3">ThreadShape</a>;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">  101</a></span>&#160;  <span class="keyword">typedef</span> ScalarA_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">ScalarA</a>;</div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">  103</a></span>&#160;  <span class="keyword">typedef</span> ScalarC_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">ScalarC</a>;</div><div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">  105</a></span>&#160;  <span class="keyword">typedef</span> ScalarD_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">ScalarD</a>;</div><div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">  107</a></span>&#160;  <span class="keyword">typedef</span> ScalarAlphaBeta_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a>;</div><div class="line"><a name="l00109"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae7e468b1d372b4b807e2e1089af885ec">  109</a></span>&#160;  <span class="keyword">typedef</span> ScalarAccum_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae7e468b1d372b4b807e2e1089af885ec">ScalarAccum</a>;</div><div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">  111</a></span>&#160;  <span class="keyword">typedef</span> Index_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>;</div><div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae0c016bcbe687063774d8abd554939b6">  113</a></span>&#160;  <span class="keyword">typedef</span> BlockSwizzle_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae0c016bcbe687063774d8abd554939b6">BlockSwizzle</a>;</div><div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a00c71c9a18aaad84f4a48023dbbb454e">  115</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a00c71c9a18aaad84f4a48023dbbb454e">ReductionSize</a> = ReductionSize_;</div><div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab4f5f457dbfa6bd250a4c34e1d573a85">  117</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">bool</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab4f5f457dbfa6bd250a4c34e1d573a85">ThreadShapeMultiple2</a> = (ThreadShape::kW % 2 == 0);</div><div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af62fd99fae22a4e4d93cfa0560f1dcc5">  119</a></span>&#160;  <span class="keyword">typedef</span> Functor_ <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af62fd99fae22a4e4d93cfa0560f1dcc5">Functor</a>;</div><div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab35edeae5cd8767bd376fad5f6680e25">  122</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab35edeae5cd8767bd376fad5f6680e25">kThreads</a> = SubTile::kW / ThreadShape::kW;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af11a3284195a24e580d2f379f179f05a">  124</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af11a3284195a24e580d2f379f179f05a">maxInReg</a> = maxInReg_;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ac28e31791c5888bbe7b04abe6376a422">  126</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ac28e31791c5888bbe7b04abe6376a422">maxOutReg</a> = maxOutReg_;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(SubTile::kW % ThreadShape::kW == 0, <span class="stringliteral">&quot;cannot evenly distribute work load among threads&quot;</span>);</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(kThreads % 32 == 0, <span class="stringliteral">&quot;threads per threadblock is not multiple of 32&quot;</span>);</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(OutputTile::kW % SubTile::kW == 0, <span class="stringliteral">&quot;cannot evenly distribute work load among iterations&quot;</span>);</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(ReductionSize * ThreadShape::kW &lt;= maxInReg, <span class="stringliteral">&quot;ReductionSize * ThreadShape::kW should not be bigger than maxInReg&quot;</span>);</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(ThreadShape::kW &lt;= maxOutReg, <span class="stringliteral">&quot;ThreadShape::kW should not be bigger than maxOutReg&quot;</span>);</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00138"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html">  138</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html">Params</a> {</div><div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#adbd0cf20c4f7033016d1b8fdaca6aeae">  140</a></span>&#160;    <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;3&gt;</a> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#adbd0cf20c4f7033016d1b8fdaca6aeae">problem_size</a>;</div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#afada1cbad87636228fb58d8577bb8470">  142</a></span>&#160;    ScalarAlphaBeta <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#afada1cbad87636228fb58d8577bb8470">alpha</a>;</div><div class="line"><a name="l00144"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a805f78cae27c3305c988f251207d85f7">  144</a></span>&#160;    ScalarAlphaBeta <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a805f78cae27c3305c988f251207d85f7">beta</a>;</div><div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a5d1463d473d4226b0d19c581b16ed3b2">  146</a></span>&#160;    <span class="keywordtype">long</span> <span class="keywordtype">long</span> <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a5d1463d473d4226b0d19c581b16ed3b2">reduction_stride</a>;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#af1b12ba220602692e84616b420b00f1c">  148</a></span>&#160;    ScalarA <span class="keyword">const</span> *<a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#af1b12ba220602692e84616b420b00f1c">d_a</a>;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ab14fa69759f1d600a28df4fabaf59c28">  150</a></span>&#160;    Index <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ab14fa69759f1d600a28df4fabaf59c28">lda</a>;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00152"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac79830eaf080ea0ffddd2100db6cf3e1">  152</a></span>&#160;    ScalarC <span class="keyword">const</span> *<a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac79830eaf080ea0ffddd2100db6cf3e1">d_c</a>;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00154"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a6bdb4dfce17da648b55c7aa68ef5b191">  154</a></span>&#160;    Index <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a6bdb4dfce17da648b55c7aa68ef5b191">ldc</a>;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00156"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#abf9744373a72f3819a616b5a5b3bff22">  156</a></span>&#160;    ScalarD *<a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#abf9744373a72f3819a616b5a5b3bff22">d_d</a>;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00158"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ad24096385e51009866b5aafd90b42ad2">  158</a></span>&#160;    Index <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ad24096385e51009866b5aafd90b42ad2">ldd</a>;</div><div class="line"><a name="l00160"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a11e54597165b66e3054c6b9f43210760">  160</a></span>&#160;    <span class="keyword">typename</span> Functor::Params <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a11e54597165b66e3054c6b9f43210760">functorParams</a>;</div><div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac27f42beb3625c5183b76b26677c0cb0">  162</a></span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac27f42beb3625c5183b76b26677c0cb0">initialize</a>(Index m_,</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;                                       Index n_,</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;                                       ScalarAlphaBeta alpha_,</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;                                       ScalarAlphaBeta beta_,</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;                                       <span class="keywordtype">long</span> <span class="keywordtype">long</span> <span class="keywordtype">int</span> reduction_stride_,</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;                                       ScalarA <span class="keyword">const</span> *d_a_,</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;                                       Index lda_,</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;                                       ScalarC <span class="keyword">const</span> *d_c_,</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;                                       Index ldc_,</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;                                       ScalarD *d_d_,</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;                                       Index ldd_){</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;      problem_size = <a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(1, n_, m_);</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;      alpha = alpha_;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;      beta = beta_;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;      reduction_stride = reduction_stride_;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;      d_a = d_a_;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;      lda = lda_;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      d_c = d_c_;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;      d_d = d_d_;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      ldc = ldc_;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;      ldd = ldd_;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;      functorParams.initialize(alpha_, beta_);</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;      <span class="keywordflow">return</span> 0;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    }</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;  };</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;};</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;} <span class="comment">// namespace reduction</span></div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_adbd0cf20c4f7033016d1b8fdaca6aeae"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#adbd0cf20c4f7033016d1b8fdaca6aeae">cutlass::reduction::BatchedReductionTraits::Params::problem_size</a></div><div class="ttdeci">Coord&lt; 3 &gt; problem_size</div><div class="ttdoc">The dimension of output tensor. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:140</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html">cutlass::reduction::BatchedReductionTraits::Params</a></div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:138</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ae0c016bcbe687063774d8abd554939b6"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae0c016bcbe687063774d8abd554939b6">cutlass::reduction::BatchedReductionTraits::BlockSwizzle</a></div><div class="ttdeci">BlockSwizzle_ BlockSwizzle</div><div class="ttdoc">The thread block swizzle. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:113</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_acfeed4cb4e5eec9d8e1ae2b787cc88e2"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#acfeed4cb4e5eec9d8e1ae2b787cc88e2">cutlass::reduction::BatchedReductionTraits::This_</a></div><div class="ttdeci">BatchedReductionTraits&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt; This_</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:91</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ab35edeae5cd8767bd376fad5f6680e25"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab35edeae5cd8767bd376fad5f6680e25">cutlass::reduction::BatchedReductionTraits::kThreads</a></div><div class="ttdeci">static int const kThreads</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:122</div></div>
<div class="ttc" id="namespacecutlass_html_a7419519fa453a121dfa5f26bf87318d9"><div class="ttname"><a href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">cutlass::make_Coord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord&lt; 1 &gt; make_Coord(int _0)</div><div class="ttdoc">Helper to make a 2-element coordinate. </div><div class="ttdef"><b>Definition:</b> coord.h:387</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ae7e468b1d372b4b807e2e1089af885ec"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae7e468b1d372b4b807e2e1089af885ec">cutlass::reduction::BatchedReductionTraits::ScalarAccum</a></div><div class="ttdeci">ScalarAccum_ ScalarAccum</div><div class="ttdoc">The type for accumulation. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:109</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_ab14fa69759f1d600a28df4fabaf59c28"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ab14fa69759f1d600a28df4fabaf59c28">cutlass::reduction::BatchedReductionTraits::Params::lda</a></div><div class="ttdeci">Index lda</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:150</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_a805f78cae27c3305c988f251207d85f7"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a805f78cae27c3305c988f251207d85f7">cutlass::reduction::BatchedReductionTraits::Params::beta</a></div><div class="ttdeci">ScalarAlphaBeta beta</div><div class="ttdoc">The beta. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:144</div></div>
<div class="ttc" id="reduction_2threadblock__swizzle_8h_html"><div class="ttname"><a href="reduction_2threadblock__swizzle_8h.html">threadblock_swizzle.h</a></div><div class="ttdoc">Defies functors for mapping blockIdx to partitions of the batched reduction computation. </div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_a29e8adec50bc7e5c0a91f975dff4a7f3"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a29e8adec50bc7e5c0a91f975dff4a7f3">cutlass::reduction::BatchedReductionTraits::ThreadShape</a></div><div class="ttdeci">ThreadShape_ ThreadShape</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:99</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_ad24096385e51009866b5aafd90b42ad2"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ad24096385e51009866b5aafd90b42ad2">cutlass::reduction::BatchedReductionTraits::Params::ldd</a></div><div class="ttdeci">Index ldd</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:158</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_abb54e3addfee4097b37deb5cb30fb582"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">cutlass::reduction::BatchedReductionTraits::ScalarD</a></div><div class="ttdeci">ScalarD_ ScalarD</div><div class="ttdoc">The output pointer type. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:105</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_a5d1463d473d4226b0d19c581b16ed3b2"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a5d1463d473d4226b0d19c581b16ed3b2">cutlass::reduction::BatchedReductionTraits::Params::reduction_stride</a></div><div class="ttdeci">long long int reduction_stride</div><div class="ttdoc">stride between two element that will be sumed </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:146</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ad9d4dcb20a69869ebda639747a16e647"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ad9d4dcb20a69869ebda639747a16e647">cutlass::reduction::BatchedReductionTraits::SubTile</a></div><div class="ttdeci">SubTile_ SubTile</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:97</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_ac79830eaf080ea0ffddd2100db6cf3e1"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac79830eaf080ea0ffddd2100db6cf3e1">cutlass::reduction::BatchedReductionTraits::Params::d_c</a></div><div class="ttdeci">ScalarC const * d_c</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:152</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_a3b0e98519b7e551ef6adac69e7406d8f"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a3b0e98519b7e551ef6adac69e7406d8f">cutlass::reduction::BatchedReductionTraits::OutputTile</a></div><div class="ttdeci">OutputTile_ OutputTile</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:95</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_a6bdb4dfce17da648b55c7aa68ef5b191"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a6bdb4dfce17da648b55c7aa68ef5b191">cutlass::reduction::BatchedReductionTraits::Params::ldc</a></div><div class="ttdeci">Index ldc</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:154</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ab89c35cfce0017a47341a1e3b2894e0f"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">cutlass::reduction::BatchedReductionTraits::ScalarAlphaBeta</a></div><div class="ttdeci">ScalarAlphaBeta_ ScalarAlphaBeta</div><div class="ttdoc">The alpha beta type. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:107</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_adb39cf54a839bdb2e38fbc8a0bf304a8"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">cutlass::reduction::BatchedReductionTraits::ScalarC</a></div><div class="ttdeci">ScalarC_ ScalarC</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:103</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_af1b12ba220602692e84616b420b00f1c"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#af1b12ba220602692e84616b420b00f1c">cutlass::reduction::BatchedReductionTraits::Params::d_a</a></div><div class="ttdeci">ScalarA const * d_a</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:148</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReduction_html"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReduction.html">cutlass::reduction::BatchedReduction</a></div><div class="ttdef"><b>Definition:</b> batched_reduction.h:52</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_a00c71c9a18aaad84f4a48023dbbb454e"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a00c71c9a18aaad84f4a48023dbbb454e">cutlass::reduction::BatchedReductionTraits::ReductionSize</a></div><div class="ttdeci">static const int ReductionSize</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:115</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_a1c6cd0a76b2b2fc9cd021b016f30f459"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">cutlass::reduction::BatchedReductionTraits::ScalarA</a></div><div class="ttdeci">ScalarA_ ScalarA</div><div class="ttdoc">The input pointer type. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:101</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord&lt; 3 &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_afada1cbad87636228fb58d8577bb8470"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#afada1cbad87636228fb58d8577bb8470">cutlass::reduction::BatchedReductionTraits::Params::alpha</a></div><div class="ttdeci">ScalarAlphaBeta alpha</div><div class="ttdoc">The alpha. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:142</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_af62fd99fae22a4e4d93cfa0560f1dcc5"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af62fd99fae22a4e4d93cfa0560f1dcc5">cutlass::reduction::BatchedReductionTraits::Functor</a></div><div class="ttdeci">Functor_ Functor</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:119</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_af11a3284195a24e580d2f379f179f05a"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af11a3284195a24e580d2f379f179f05a">cutlass::reduction::BatchedReductionTraits::maxInReg</a></div><div class="ttdeci">static int const maxInReg</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:124</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_abf9744373a72f3819a616b5a5b3bff22"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#abf9744373a72f3819a616b5a5b3bff22">cutlass::reduction::BatchedReductionTraits::Params::d_d</a></div><div class="ttdeci">ScalarD * d_d</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:156</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_a11e54597165b66e3054c6b9f43210760"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a11e54597165b66e3054c6b9f43210760">cutlass::reduction::BatchedReductionTraits::Params::functorParams</a></div><div class="ttdeci">Functor::Params functorParams</div><div class="ttdoc">The functor params. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:160</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ab4f5f457dbfa6bd250a4c34e1d573a85"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab4f5f457dbfa6bd250a4c34e1d573a85">cutlass::reduction::BatchedReductionTraits::ThreadShapeMultiple2</a></div><div class="ttdeci">static const bool ThreadShapeMultiple2</div><div class="ttdoc">check if threadShape is multiple of 2. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:117</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ab0c9e548f3ee62746e727127e387a8f4"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">cutlass::reduction::BatchedReductionTraits::Index</a></div><div class="ttdeci">Index_ Index</div><div class="ttdoc">The index. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:111</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_ac28e31791c5888bbe7b04abe6376a422"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ac28e31791c5888bbe7b04abe6376a422">cutlass::reduction::BatchedReductionTraits::maxOutReg</a></div><div class="ttdeci">static int const maxOutReg</div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:126</div></div>
<div class="ttc" id="batched__reduction_8h_html"><div class="ttname"><a href="batched__reduction_8h.html">batched_reduction.h</a></div><div class="ttdoc">Implements a software-pipelined efficient batched reduction. D = alpha * Reduction(A) + beta * C...</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a></div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:76</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params_html_ac27f42beb3625c5183b76b26677c0cb0"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac27f42beb3625c5183b76b26677c0cb0">cutlass::reduction::BatchedReductionTraits::Params::initialize</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE int initialize(Index m_, Index n_, ScalarAlphaBeta alpha_, ScalarAlphaBeta beta_, long long int reduction_stride_, ScalarA const *d_a_, Index lda_, ScalarC const *d_c_, Index ldc_, ScalarD *d_d_, Index ldd_)</div><div class="ttdoc">Initialize the parameters for 2D output tensor. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:162</div></div>
<div class="ttc" id="structcutlass_1_1reduction_1_1BatchedReductionTraits_html_a085c72d54426f5eb60f5bffa9c383229"><div class="ttname"><a href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a085c72d54426f5eb60f5bffa9c383229">cutlass::reduction::BatchedReductionTraits::KernelClass</a></div><div class="ttdeci">cutlass::reduction::BatchedReduction&lt; This_ &gt; KernelClass</div><div class="ttdoc">The struct that consumes this Traits. </div><div class="ttdef"><b>Definition:</b> batched_reduction_traits.h:93</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
