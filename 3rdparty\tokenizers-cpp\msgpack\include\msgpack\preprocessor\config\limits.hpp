# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2011) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_CONFIG_LIMITS_HPP
# define MSGPACK_PREPROCESSOR_CONFIG_LIMITS_HPP
#
# define MSGPACK_PP_LIMIT_MAG 256
# define MSGPACK_PP_LIMIT_TUPLE 64
# define MSGPACK_PP_LIMIT_DIM 3
# define MSGPACK_PP_LIMIT_REPEAT 256
# define MSGPACK_PP_LIMIT_WHILE 256
# define MSGPACK_PP_LIMIT_FOR 256
# define MSGPACK_PP_LIMIT_ITERATION 256
# define MSGPACK_PP_LIMIT_ITERATION_DIM 3
# define MSGPACK_PP_LIMIT_SEQ 256
# define MSGPACK_PP_LIMIT_SLOT_SIG 10
# define MSGPACK_PP_LIMIT_SLOT_COUNT 5
#
# endif
