//
// MessagePack for C++ FILE* buffer adaptor
//
// Copyright (C) 2013-2018 <PERSON> and <PERSON><PERSON><PERSON><PERSON>
//
//    Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//    http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef MSGPACK_V3_FBUFFER_DECL_HPP
#define MSGPACK_V3_FBUFFER_DECL_HPP

#include "msgpack/v2/fbuffer_decl.hpp"

#include <cstdio>
#include <stdexcept>

namespace msgpack {

/// @cond
MSGPACK_API_VERSION_NAMESPACE(v3) {
/// @endcond

using v2::fbuffer;

/// @cond
}  // MSGPACK_API_VERSION_NAMESPACE(v3)
/// @endcond

}  // namespace msgpack

#endif // MSGPACK_V3_FBUFFER_DECL_HPP
