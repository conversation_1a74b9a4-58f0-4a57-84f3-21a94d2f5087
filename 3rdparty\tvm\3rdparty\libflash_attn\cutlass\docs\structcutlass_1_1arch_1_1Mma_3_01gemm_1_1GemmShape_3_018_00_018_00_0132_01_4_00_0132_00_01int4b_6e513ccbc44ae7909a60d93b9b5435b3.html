<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 32 &gt;, 32, int4b_t, layout::RowMajor, int4b_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAdd &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1arch.html">arch</a></li><li class="navelem"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html">Mma&lt; gemm::GemmShape&lt; 8, 8, 32 &gt;, 32, int4b_t, layout::RowMajor, int4b_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAdd &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_b4842cad42fe945980d6229487761771.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::arch::Mma&lt; gemm::GemmShape&lt; 8, 8, 32 &gt;, 32, int4b_t, layout::RowMajor, int4b_t, layout::ColumnMajor, int, layout::RowMajor, OpMultiplyAdd &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Matrix multiply-add operation: S32 = S4 * S4 + S32.  
</p>

<p><code>#include &lt;<a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:aebde3befd83cd9143b59a3d0d3b09326"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aebde3befd83cd9143b59a3d0d3b09326">Shape</a> = <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;</td></tr>
<tr class="separator:aebde3befd83cd9143b59a3d0d3b09326"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a301b989f6a481cea2f733f1f3ceb77d9"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a301b989f6a481cea2f733f1f3ceb77d9">ElementA</a> = <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a></td></tr>
<tr class="separator:a301b989f6a481cea2f733f1f3ceb77d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af73260f1928688306a5b3b73fc89b7f3"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#af73260f1928688306a5b3b73fc89b7f3">LayoutA</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:af73260f1928688306a5b3b73fc89b7f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39fb4bf1e9411e0ce49991235fee7ad6"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a39fb4bf1e9411e0ce49991235fee7ad6">FragmentA</a> = Array&lt; <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, 8 &gt;</td></tr>
<tr class="separator:a39fb4bf1e9411e0ce49991235fee7ad6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d4d7e597291dc50b724d76980bbec37"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a8d4d7e597291dc50b724d76980bbec37">ElementB</a> = <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a></td></tr>
<tr class="separator:a8d4d7e597291dc50b724d76980bbec37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a722058d8c0d890095d05264de70d2de0"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a722058d8c0d890095d05264de70d2de0">LayoutB</a> = <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td></tr>
<tr class="separator:a722058d8c0d890095d05264de70d2de0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac90c60556e063b75b9a308e3a6c0d149"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#ac90c60556e063b75b9a308e3a6c0d149">FragmentB</a> = Array&lt; <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, 8 &gt;</td></tr>
<tr class="separator:ac90c60556e063b75b9a308e3a6c0d149"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae218ea47f86a5909eeda1ce3e2673f05"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#ae218ea47f86a5909eeda1ce3e2673f05">ElementC</a> = int</td></tr>
<tr class="separator:ae218ea47f86a5909eeda1ce3e2673f05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07799cfaa15dd4af93e140ae952af86b"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a07799cfaa15dd4af93e140ae952af86b">LayoutC</a> = <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td></tr>
<tr class="separator:a07799cfaa15dd4af93e140ae952af86b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4e15207db0471ab1ec0b2eee6ff9e62"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aa4e15207db0471ab1ec0b2eee6ff9e62">FragmentC</a> = Array&lt; int, 2 &gt;</td></tr>
<tr class="separator:aa4e15207db0471ab1ec0b2eee6ff9e62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67b167e48057a5172cb6e0a173317fad"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a67b167e48057a5172cb6e0a173317fad">Operator</a> = OpMultiplyAdd</td></tr>
<tr class="separator:a67b167e48057a5172cb6e0a173317fad"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8137013b1551deb37b4015e2cfd19b97"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a8137013b1551deb37b4015e2cfd19b97">operator()</a> (<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aa4e15207db0471ab1ec0b2eee6ff9e62">FragmentC</a> &amp;d, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a39fb4bf1e9411e0ce49991235fee7ad6">FragmentA</a> const &amp;a, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#ac90c60556e063b75b9a308e3a6c0d149">FragmentB</a> const &amp;b, <a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aa4e15207db0471ab1ec0b2eee6ff9e62">FragmentC</a> const &amp;c) const </td></tr>
<tr class="memdesc:a8137013b1551deb37b4015e2cfd19b97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes multiply-add.  <a href="#a8137013b1551deb37b4015e2cfd19b97">More...</a><br /></td></tr>
<tr class="separator:a8137013b1551deb37b4015e2cfd19b97"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a301b989f6a481cea2f733f1f3ceb77d9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a301b989f6a481cea2f733f1f3ceb77d9">ElementA</a> =  <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8d4d7e597291dc50b724d76980bbec37"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a8d4d7e597291dc50b724d76980bbec37">ElementB</a> =  <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae218ea47f86a5909eeda1ce3e2673f05"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#ae218ea47f86a5909eeda1ce3e2673f05">ElementC</a> =  int</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a39fb4bf1e9411e0ce49991235fee7ad6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a39fb4bf1e9411e0ce49991235fee7ad6">FragmentA</a> =  Array&lt;<a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, 8&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac90c60556e063b75b9a308e3a6c0d149"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#ac90c60556e063b75b9a308e3a6c0d149">FragmentB</a> =  Array&lt;<a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, 8&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa4e15207db0471ab1ec0b2eee6ff9e62"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aa4e15207db0471ab1ec0b2eee6ff9e62">FragmentC</a> =  Array&lt;int, 2&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af73260f1928688306a5b3b73fc89b7f3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#af73260f1928688306a5b3b73fc89b7f3">LayoutA</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a722058d8c0d890095d05264de70d2de0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a722058d8c0d890095d05264de70d2de0">LayoutB</a> =  <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a07799cfaa15dd4af93e140ae952af86b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a07799cfaa15dd4af93e140ae952af86b">LayoutC</a> =  <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a67b167e48057a5172cb6e0a173317fad"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a67b167e48057a5172cb6e0a173317fad">Operator</a> =  OpMultiplyAdd</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aebde3befd83cd9143b59a3d0d3b09326"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::<a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aebde3befd83cd9143b59a3d0d3b09326">Shape</a> =  <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt;8,8,32&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a8137013b1551deb37b4015e2cfd19b97"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void <a class="el" href="structcutlass_1_1arch_1_1Mma.html">cutlass::arch::Mma</a>&lt; <a class="el" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt; 8, 8, 32 &gt;, 32, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, <a class="el" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>, <a class="el" href="classcutlass_1_1layout_1_1ColumnMajor.html">layout::ColumnMajor</a>, int, <a class="el" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>, OpMultiplyAdd &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aa4e15207db0471ab1ec0b2eee6ff9e62">FragmentC</a> &amp;&#160;</td>
          <td class="paramname"><em>d</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#a39fb4bf1e9411e0ce49991235fee7ad6">FragmentA</a> const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#ac90c60556e063b75b9a308e3a6c0d149">FragmentB</a> const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1arch_1_1Mma_3_01gemm_1_1GemmShape_3_018_00_018_00_0132_01_4_00_0132_00_01int4b_6e513ccbc44ae7909a60d93b9b5435b3.html#aa4e15207db0471ab1ec0b2eee6ff9e62">FragmentC</a> const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="mma__sm75_8h_source.html">mma_sm75.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
