<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>std</b></li><li class="navelem"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">numeric_limits&lt; cutlass::half_t &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">std::numeric_limits&lt; cutlass::half_t &gt; Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a2c05c19022c183e8734ada65c8970af5">denorm_min</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a92152311525685a53c6a0db4cb74f193">digits</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab9fc3a009eaff0c922307f2780ee3fc0">epsilon</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#aaf46b5d03403828c1e6633fb714ffd84">has_denorm</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a8b371f82151fd0238b7da083fa2b87a9">has_denorm_loss</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a6f7f2fbe6cd7a04803b90b8fa9172098">has_infinity</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a3d75832e46bc154758e35a03a624ccf8">has_quiet_NaN</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a68a0d0f6ecc2f3b84f2e71475b2c48bd">has_signaling_NaN</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab7a40820e64282376a050095d5004b74">infinity</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a59737f49161b87c259683c26737f42c2">is_bounded</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a7413e9cd24eb03a86cc5f2d47c49db3e">is_exact</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#afc86b85a1fe209658a50d8c06e54cb77">is_iec559</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a2a2ec168a6f0e9f55dc42d3b3e5fff25">is_integer</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a640a034527a3577039053113bc1c5e46">is_modulo</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab3a169117baca2e7fae33846caa5dbfd">is_signed</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ade4affb586360c5356a7939c1b343a40">is_specialized</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ac2ae43139037875e38056a675ae1f6c4">lowest</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a39a5774583daedbb5ac4aaaaa8034883">max</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ad9175b4d7b32fe18cf9c07e4f559b32c">min</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a8c7eafdd3b121353c0914dc6e1c0d108">quiet_NaN</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab0a036db7a1ad11c65e876020c78b1a5">round_error</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#ab0af85c1d7c83ca03ed0c083fe22262f">round_style</a></td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a423fb5b95e6071e832d40918e597f63f">signaling_NaN</a>()</td><td class="entry"><a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html">std::numeric_limits&lt; cutlass::half_t &gt;</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
