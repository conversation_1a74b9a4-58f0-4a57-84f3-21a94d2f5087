# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_SEQ_PUSH_FRONT_HPP
# define MSGPACK_PREPROCESSOR_SEQ_PUSH_FRONT_HPP
#
# /* MSGP<PERSON><PERSON>_PP_SEQ_PUSH_FRONT */
#
# define MSGPACK_PP_SEQ_PUSH_FRONT(seq, elem) (elem)seq
#
# endif
