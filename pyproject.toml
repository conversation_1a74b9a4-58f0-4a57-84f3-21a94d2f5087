# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
[tool.isort]
profile = "black"
src_paths = ["python/mlc_llm"]
known_third_party = ["numpy", "tvm", "tqdm", "torch", "transformers"]

[tool.black]
line-length = 100

[tool.mypy]
ignore_missing_imports = true
show_column_numbers = true
show_error_context = true
follow_imports = "skip"
ignore_errors = false
strict_optional = false

[tool.pylint.messages_control]
max-line-length = 100
disable = """
duplicate-code,
"""
