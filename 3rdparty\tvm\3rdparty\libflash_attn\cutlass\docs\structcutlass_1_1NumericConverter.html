<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::NumericConverter&lt; T, S, Round &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1NumericConverter.html">NumericConverter</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1NumericConverter-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::NumericConverter&lt; T, S, Round &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="numeric__conversion_8h_source.html">numeric_conversion.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a46b1b5c0c96c50176578fcd6f915ee8c"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c">result_type</a> = T</td></tr>
<tr class="separator:a46b1b5c0c96c50176578fcd6f915ee8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15da0162a4c6d46d2acdffbcd718bff0"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1NumericConverter.html#a15da0162a4c6d46d2acdffbcd718bff0">source_type</a> = S</td></tr>
<tr class="separator:a15da0162a4c6d46d2acdffbcd718bff0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aeb946a1caf2882aafc57fae06f1bb1f8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c">result_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1NumericConverter.html#aeb946a1caf2882aafc57fae06f1bb1f8">operator()</a> (<a class="el" href="structcutlass_1_1NumericConverter.html#a15da0162a4c6d46d2acdffbcd718bff0">source_type</a> const &amp;s)</td></tr>
<tr class="separator:aeb946a1caf2882aafc57fae06f1bb1f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a4d1a347bd8c92f3dc5b6e919005d34d2"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c">result_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1NumericConverter.html#a4d1a347bd8c92f3dc5b6e919005d34d2">convert</a> (<a class="el" href="structcutlass_1_1NumericConverter.html#a15da0162a4c6d46d2acdffbcd718bff0">source_type</a> const &amp;s)</td></tr>
<tr class="separator:a4d1a347bd8c92f3dc5b6e919005d34d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a6060b5f316853f791fc87ef3bd180b1a"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6">FloatRoundStyle</a> const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1NumericConverter.html#a6060b5f316853f791fc87ef3bd180b1a">round_style</a> = Round</td></tr>
<tr class="separator:a6060b5f316853f791fc87ef3bd180b1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a46b1b5c0c96c50176578fcd6f915ee8c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, typename S, FloatRoundStyle Round = FloatRoundStyle::round_to_nearest&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1NumericConverter.html">cutlass::NumericConverter</a>&lt; T, S, Round &gt;::<a class="el" href="structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c">result_type</a> =  T</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a15da0162a4c6d46d2acdffbcd718bff0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, typename S, FloatRoundStyle Round = FloatRoundStyle::round_to_nearest&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1NumericConverter.html">cutlass::NumericConverter</a>&lt; T, S, Round &gt;::<a class="el" href="structcutlass_1_1NumericConverter.html#a15da0162a4c6d46d2acdffbcd718bff0">source_type</a> =  S</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a4d1a347bd8c92f3dc5b6e919005d34d2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, typename S, FloatRoundStyle Round = FloatRoundStyle::round_to_nearest&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c">result_type</a> <a class="el" href="structcutlass_1_1NumericConverter.html">cutlass::NumericConverter</a>&lt; T, S, Round &gt;::convert </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1NumericConverter.html#a15da0162a4c6d46d2acdffbcd718bff0">source_type</a> const &amp;&#160;</td>
          <td class="paramname"><em>s</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aeb946a1caf2882aafc57fae06f1bb1f8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, typename S, FloatRoundStyle Round = FloatRoundStyle::round_to_nearest&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1NumericConverter.html#a46b1b5c0c96c50176578fcd6f915ee8c">result_type</a> <a class="el" href="structcutlass_1_1NumericConverter.html">cutlass::NumericConverter</a>&lt; T, S, Round &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1NumericConverter.html#a15da0162a4c6d46d2acdffbcd718bff0">source_type</a> const &amp;&#160;</td>
          <td class="paramname"><em>s</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a6060b5f316853f791fc87ef3bd180b1a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T, typename S, FloatRoundStyle Round = FloatRoundStyle::round_to_nearest&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="namespacecutlass.html#aabe6b8ce223bf05f65a4721a3f5447a6">FloatRoundStyle</a> const <a class="el" href="structcutlass_1_1NumericConverter.html">cutlass::NumericConverter</a>&lt; T, S, Round &gt;::round_style = Round</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="numeric__conversion_8h_source.html">numeric_conversion.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
