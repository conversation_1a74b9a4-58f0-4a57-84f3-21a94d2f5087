cff-version: 1.2.0
title: <PERSON><PERSON><PERSON><PERSON>
message: >-
  If you use this software, please cite using the
  following metadata.
type: software
authors:
  - given-names: <PERSON>
    email: a<PERSON><PERSON>@nvidia.com
    family-names: Kerr
    affiliation: NVIDIA
  - given-names: Haicheng
    family-names: Wu
    affiliation: NVIDIA
    email: haiche<PERSON><PERSON>@nvidia.com
  - given-names: Manish
    family-names: Gupta
    affiliation: Google
    email: <EMAIL>
  - given-names: <PERSON>n
    family-names: <PERSON><PERSON><PERSON>
    email: d<PERSON><PERSON><PERSON>@nvidia.com
    affiliation: NVIDIA
  - given-names: <PERSON>radeep
    family-names: <PERSON><PERSON>
    email: <EMAIL>
    affiliation: NVIDIA
  - given-names: Duane
    family-names: <PERSON>
    email: du<PERSON><PERSON>@nvidia.com
    affiliation: NVIDIA
  - given-names: An<PERSON><PERSON>
    family-names: <PERSON><PERSON>
    email: ashiva<PERSON>@nvidia.com
    affiliation: NVIDIA
  - given-names: Piotr
    family-names: Majcher
    email: <EMAIL>
    affiliation: NVIDIA
  - given-names: Paul
    family-names: Springer
    email: pspring<PERSON>@nvidia.com
    affiliation: NVIDIA
  - given-names: <PERSON>
    family-names: <PERSON><PERSON><PERSON><PERSON>
    affiliation: NVIDIA
    email: <PERSON><PERSON><PERSON><PERSON><PERSON>@nvidia.com
  - given-names: <PERSON>
    family-names: <PERSON>
    email: <EMAIL>
    affiliation: NVIDIA
  - given-names: <PERSON>
    family-names: Nicely
    email: <EMAIL>
    affiliation: NVIDIA
repository-code: 'https://github.com/NVIDIA/cutlass'
abstract: >-
  CUTLASS is a collection of CUDA C++ template
  abstractions for implementing high-performance
  matrix-multiplication (GEMM) and related
  computations at all levels and scales within CUDA.
  It incorporates strategies for hierarchical
  decomposition and data movement similar to those
  used to implement cuBLAS and cuDNN. CUTLASS
  decomposes these "moving parts" into reusable,
  modular software components abstracted by C++
  template classes. These thread-wide, warp-wide,
  block-wide, and device-wide primitives can be
  specialized and tuned via custom tiling sizes, data
  types, and other algorithmic policy. The resulting
  flexibility simplifies their use as building blocks
  within custom kernels and applications.
keywords:
  - 'cutlass, tensor cores, cuda'
license: BSD-3-Clause
license-url: https://github.com/NVIDIA/cutlass/blob/v2.10.0/LICENSE.txt
version: '2.10.0'
date-released: '2022-09-15'
identifiers:
  - type: url
    value: "https://github.com/NVIDIA/cutlass/tree/v2.10.0"
    description: The GitHub release URL of tag 2.10.0
