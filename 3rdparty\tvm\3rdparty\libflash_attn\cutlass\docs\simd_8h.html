<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: simd.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">simd.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Templates exposing SIMD operators.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &quot;<a class="el" href="array_8h_source.html">../array.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="numeric__types_8h_source.html">../numeric_types.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="simd__sm60_8h_source.html">simd_sm60.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="simd__sm61_8h_source.html">simd_sm61.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for simd.h:</div>
<div class="dyncontent">
<div class="center"><img src="simd_8h__incl.png" border="0" usemap="#simd_8h" alt=""/></div>
<map name="simd_8h" id="simd_8h">
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="simd_8h__dep__incl.png" border="0" usemap="#simd_8hdep" alt=""/></div>
<map name="simd_8hdep" id="simd_8hdep">
</map>
</div>
</div>
<p><a href="simd_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespacecutlass"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html">cutlass</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1arch"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html">cutlass::arch</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ade374d7dc92e0d8376be6b3a6b639e9d"><td class="memTemplParams" colspan="2">template&lt;typename T , int N&gt; </td></tr>
<tr class="memitem:ade374d7dc92e0d8376be6b3a6b639e9d"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> Array&lt; T, N &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#ade374d7dc92e0d8376be6b3a6b639e9d">cutlass::arch::operator*</a> (Array&lt; T, N &gt; const &amp;a, Array&lt; T, N &gt; const &amp;b)</td></tr>
<tr class="separator:ade374d7dc92e0d8376be6b3a6b639e9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d90229f6c0acc1125004861e76fa904"><td class="memTemplParams" colspan="2">template&lt;typename T , int N&gt; </td></tr>
<tr class="memitem:a2d90229f6c0acc1125004861e76fa904"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> Array&lt; T, N &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a2d90229f6c0acc1125004861e76fa904">cutlass::arch::operator+</a> (Array&lt; T, N &gt; const &amp;a, Array&lt; T, N &gt; const &amp;b)</td></tr>
<tr class="separator:a2d90229f6c0acc1125004861e76fa904"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31b78db1868e73e79ef2e6a93b2f697c"><td class="memTemplParams" colspan="2">template&lt;typename T , int N&gt; </td></tr>
<tr class="memitem:a31b78db1868e73e79ef2e6a93b2f697c"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> Array&lt; T, N &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#a31b78db1868e73e79ef2e6a93b2f697c">cutlass::arch::operator-</a> (Array&lt; T, N &gt; const &amp;a, Array&lt; T, N &gt; const &amp;b)</td></tr>
<tr class="separator:a31b78db1868e73e79ef2e6a93b2f697c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe575ad586930bfba45449455d3fad58"><td class="memTemplParams" colspan="2">template&lt;typename T , int N&gt; </td></tr>
<tr class="memitem:abe575ad586930bfba45449455d3fad58"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> Array&lt; T, N &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#abe575ad586930bfba45449455d3fad58">cutlass::arch::mac</a> (Array&lt; T, N &gt; const &amp;a, Array&lt; T, N &gt; const &amp;b, Array&lt; T, N &gt; const &amp;c)</td></tr>
<tr class="separator:abe575ad586930bfba45449455d3fad58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa36dc224381add086ca4e0f96a04a964"><td class="memTemplParams" colspan="2">template&lt;typename Element , typename Accumulator , int N&gt; </td></tr>
<tr class="memitem:aa36dc224381add086ca4e0f96a04a964"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> Accumulator&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1arch.html#aa36dc224381add086ca4e0f96a04a964">cutlass::arch::dot</a> (Array&lt; T, N &gt; const &amp;a, Array&lt; T, N &gt; const &amp;b, Accumulator accum)</td></tr>
<tr class="separator:aa36dc224381add086ca4e0f96a04a964"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
