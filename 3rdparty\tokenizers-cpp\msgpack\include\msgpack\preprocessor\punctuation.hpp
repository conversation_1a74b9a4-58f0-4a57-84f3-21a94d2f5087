# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef MSGPACK_PREPROCESSOR_PUNCTUATION_HPP
# define MSGPACK_PREPROCESSOR_PUNCTUATION_HPP
#
# include <msgpack/preprocessor/punctuation/comma.hpp>
# include <msgpack/preprocessor/punctuation/comma_if.hpp>
# include <msgpack/preprocessor/punctuation/is_begin_parens.hpp>
# include <msgpack/preprocessor/punctuation/paren.hpp>
# include <msgpack/preprocessor/punctuation/paren_if.hpp>
# include <msgpack/preprocessor/punctuation/remove_parens.hpp>
#
# endif
