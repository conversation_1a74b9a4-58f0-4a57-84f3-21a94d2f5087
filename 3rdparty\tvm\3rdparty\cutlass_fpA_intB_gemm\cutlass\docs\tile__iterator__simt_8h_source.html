<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tile_iterator_simt.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tile_iterator_simt.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tile__iterator__simt_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear_8h.html">cutlass/layout/pitch_linear.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="simt__policy_8h.html">cutlass/epilogue/warp/simt_policy.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="tile__iterator__simt_8h.html#af5392199c6e71a31335abd0bb1d9ba36">   37</a></span>&#160;<span class="preprocessor">#define CUTLASS_SIMT_EPILOGUE_USE_SCALAR_STORES 1</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">typename</span> WarpShape,     </div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keyword">typename</span> Operator,      </div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <span class="keyword">typename</span> Element,       </div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <span class="keyword">typename</span> Layout,        </div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  <span class="keyword">typename</span> MmaSimtPolicy          </div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;&gt;</div><div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt.html">   55</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt.html">TileIteratorSimt</a>;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keyword">typename</span> WarpShape_,     </div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> Operator_,      </div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">typename</span> Element_,       </div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">typename</span> MmaSimtPolicy_         </div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;&gt;</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html">   66</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt.html">TileIteratorSimt</a>&lt;WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_&gt; {</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aff99d6853af936cc23506b16b780ce09">   69</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aff99d6853af936cc23506b16b780ce09">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa712e96097d9f487858208c04c83d26b">   70</a></span>&#160;  <span class="keyword">using</span> Operator = Operator_;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a7538a71b8c5c38ffbb2056e2c425e47b">   71</a></span>&#160;  <span class="keyword">using</span> Element = Element_;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a9928349624ec760485d8921255edf9e4">   72</a></span>&#160;  <span class="keyword">using</span> Layout = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a9124f663869b4a8d67664c0b5e41e912">   74</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;         </div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a126b9160dc4a4c6156be464673202e1d">   75</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;                      </div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a05ff07efb8d79a930472de75ea30938a">   76</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a05ff07efb8d79a930472de75ea30938a">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">TensorRef::Index</a>;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a5c553a18b6215ed1158e863851be631a">   77</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a5c553a18b6215ed1158e863851be631a">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">TensorRef::LongIndex</a>;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ad70c4e067ea653db52e72cd07a918cc7">   79</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy.html">SimtPolicy&lt;WarpShape, Operator, Layout, MmaSimtPolicy_&gt;</a>;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Shape</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    Policy::kRowsPerIteration,</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    WarpShape::kN</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ad45557644f48e06a4d68c4eb6f8515e8">   85</a></span>&#160;  &gt;;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa1caeb6928f62ceabba7817c754b08b7">Fragment</a> = Array&lt;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    <span class="keyword">typename</span> Operator::ElementC, </div><div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa1caeb6928f62ceabba7817c754b08b7">   90</a></span>&#160;    Policy::kElementsPerIteration&gt;;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a6c100257da1ab23db35da3c4818a32f3">AccumulatorTile</a> = Array&lt;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <span class="keyword">typename</span> Operator::ElementC, </div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a6c100257da1ab23db35da3c4818a32f3">   95</a></span>&#160;    Policy::kAccumulatorElementCount&gt;;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ad22571b7a352a6c9834341ff15614759">   98</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Padding</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    0,</div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa56eb3d1c6b3aea627b8ee024be0e451">  103</a></span>&#160;    4 * Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray&lt;Element, Policy::kElementsPerAccess&gt;</a>;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *pointer_;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  Layout layout_;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a0e0346d2b4a2e5b111a2d1e6d3ac775e">  124</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a0e0346d2b4a2e5b111a2d1e6d3ac775e">TileIteratorSimt</a>(): pointer_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { }</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a25c74737e253375473171917c4f3df6f">  128</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a25c74737e253375473171917c4f3df6f">TileIteratorSimt</a>(</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <span class="keyword">const</span> &amp;ref,</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    <span class="keywordtype">unsigned</span> lane_id</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  ):</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    pointer_(reinterpret_cast&lt;<a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *&gt;(ref.data())),</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    layout_(ref.stride()[0] / <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy.html">Policy</a>::kElementsPerAccess) { </div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    <span class="keyword">auto</span> lane_layout = Policy::MmaSimtPolicy::get_lane_layout();</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> lane_offset = lane_layout.inverse(lane_id);</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    pointer_ += layout_(lane_offset);</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  }</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a59a4695befd919265485918ee935aab1">  143</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt.html">TileIteratorSimt</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a59a4695befd919265485918ee935aab1">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a05ff07efb8d79a930472de75ea30938a">Index</a> pointer_offset) {</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    pointer_ += pointer_offset / Policy::kElementsPerAccess;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  }</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ae9f188af3f314fb898e7ad842cba98df">  150</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt.html">TileIteratorSimt</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ae9f188af3f314fb898e7ad842cba98df">add_tile_offset</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    pointer_ += layout_({</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;      tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * Shape::kRow, </div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;      (tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * Shape::kColumn / Policy::kElementsPerAccess)</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    });</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;  }</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a1a76d3b0fd2419e78d66640650ac511c">  162</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt.html">TileIteratorSimt</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a1a76d3b0fd2419e78d66640650ac511c">operator+=</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    add_tile_offset(tile_offset);</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    </div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;  }</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00171"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a90b52f1411169d8f31a9b336cbb7390b">  171</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a90b52f1411169d8f31a9b336cbb7390b">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa1caeb6928f62ceabba7817c754b08b7">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a05ff07efb8d79a930472de75ea30938a">Index</a> pointer_offset) {</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;<span class="preprocessor">#if CUTLASS_SIMT_EPILOGUE_USE_SCALAR_STORES</span></div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;      <span class="comment">// de-vectorized stores</span></div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;      <span class="keyword">using</span> ScalarAccessType = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray&lt;Element, 1&gt;</a>;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;      ScalarAccessType <span class="keyword">const</span> *scalarFragPtr = <span class="keyword">reinterpret_cast&lt;</span>ScalarAccessType <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;      ScalarAccessType *scalarPointer = <span class="keyword">reinterpret_cast&lt;</span>ScalarAccessType *<span class="keyword">&gt;</span>(pointer_);</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> n = 0; n &lt; Policy::kAccessesPerIteration; ++n) {</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> s = 0; s &lt; Policy::kElementsPerAccess; s++) {</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;          scalarPointer[n * Policy::MmaSimtPolicy::WarpShape::kColumn * Policy::kElementsPerAccess + s] = scalarFragPtr[n * Policy::kElementsPerAccess + s];</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;        }</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;      }</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;<span class="preprocessor">#else</span></div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    <span class="comment">// original vector stores</span></div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const</span> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> n = 0; n &lt; Policy::kAccessesPerIteration; ++n) {</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;      pointer_[n * Policy::MmaSimtPolicy::WarpShape::kColumn] = frag_ptr[n];</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    }</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  }</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00197"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ad3979de3f4e9abec2cbc7e8d8f41641c">  197</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ad3979de3f4e9abec2cbc7e8d8f41641c">store</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa1caeb6928f62ceabba7817c754b08b7">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    store_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;  }</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a91fa2b56deb9d9e4a5d58c0103198559">  203</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a91fa2b56deb9d9e4a5d58c0103198559">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa1caeb6928f62ceabba7817c754b08b7">Fragment</a> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a05ff07efb8d79a930472de75ea30938a">Index</a> pointer_offset)<span class="keyword"> const </span>{</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> n = 0; n &lt; Policy::kAccessesPerIteration; ++n) {</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;      frag_ptr[n] = pointer_[n * Policy::MmaSimtPolicy::WarpShape::kColumn];</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    }</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  }</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#adc0257e19f8d1c6d3cfd8ef52d952f71">  215</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#adc0257e19f8d1c6d3cfd8ef52d952f71">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa1caeb6928f62ceabba7817c754b08b7">Fragment</a> &amp;frag)<span class="keyword"> const </span>{</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    load_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;  }</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;};</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div><div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a6c100257da1ab23db35da3c4818a32f3"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a6c100257da1ab23db35da3c4818a32f3">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::AccumulatorTile</a></div><div class="ttdeci">Array&lt; typename Operator::ElementC, Policy::kAccumulatorElementCount &gt; AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:95</div></div>
<div class="ttc" id="classcutlass_1_1AlignedArray_html"><div class="ttname"><a href="classcutlass_1_1AlignedArray.html">cutlass::AlignedArray</a></div><div class="ttdoc">Aligned array type. </div><div class="ttdef"><b>Definition:</b> array.h:511</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_aa1caeb6928f62ceabba7817c754b08b7"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aa1caeb6928f62ceabba7817c754b08b7">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::Fragment</a></div><div class="ttdeci">Array&lt; typename Operator::ElementC, Policy::kElementsPerIteration &gt; Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:90</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a05ff07efb8d79a930472de75ea30938a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a05ff07efb8d79a930472de75ea30938a">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::Index</a></div><div class="ttdeci">typename TensorRef::Index Index</div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:76</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a5c553a18b6215ed1158e863851be631a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a5c553a18b6215ed1158e863851be631a">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::LongIndex</a></div><div class="ttdeci">typename TensorRef::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:77</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1SimtPolicy.html">cutlass::epilogue::warp::SimtPolicy</a></div><div class="ttdef"><b>Definition:</b> simt_policy.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_adc0257e19f8d1c6d3cfd8ef52d952f71"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#adc0257e19f8d1c6d3cfd8ef52d952f71">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment &amp;frag) const </div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:215</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_ad3979de3f4e9abec2cbc7e8d8f41641c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ad3979de3f4e9abec2cbc7e8d8f41641c">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::store</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:197</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_ae9f188af3f314fb898e7ad842cba98df"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#ae9f188af3f314fb898e7ad842cba98df">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::add_tile_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorSimt &amp; add_tile_offset(TensorCoord const &amp;tile_offset)</div><div class="ttdoc">advances in units of whole tiles along the logical coordinate space of the tensor ...</div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:150</div></div>
<div class="ttc" id="platform_8h_html_ab979d9d4b4923f7c54d6caa6e1a61936"><div class="ttname"><a href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a></div><div class="ttdeci">#define nullptr</div><div class="ttdoc">nullptr </div><div class="ttdef"><b>Definition:</b> platform.h:144</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a90b52f1411169d8f31a9b336cbb7390b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a90b52f1411169d8f31a9b336cbb7390b">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:171</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a59a4695befd919265485918ee935aab1"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a59a4695befd919265485918ee935aab1">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorSimt &amp; add_pointer_offset(Index pointer_offset)</div><div class="ttdoc">Adds a pointer offset. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:143</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a25c74737e253375473171917c4f3df6f"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a25c74737e253375473171917c4f3df6f">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::TileIteratorSimt</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorSimt(TensorRef const &amp;ref, unsigned lane_id)</div><div class="ttdoc">Constructor from TensorRef. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:128</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a0e0346d2b4a2e5b111a2d1e6d3ac775e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a0e0346d2b4a2e5b111a2d1e6d3ac775e">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::TileIteratorSimt</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorSimt()</div><div class="ttdoc">Default constructor. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:124</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a11ec4b07a2132e647ca2ebe5112ce5ec"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">cutlass::TensorRef::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdoc">Index type. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:165</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="pitch__linear_8h_html"><div class="ttname"><a href="pitch__linear_8h.html">pitch_linear.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes for pitch-linear memory. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a1a76d3b0fd2419e78d66640650ac511c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a1a76d3b0fd2419e78d66640650ac511c">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorSimt &amp; operator+=(TensorCoord const &amp;tile_offset)</div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:162</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_a91fa2b56deb9d9e4a5d58c0103198559"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#a91fa2b56deb9d9e4a5d58c0103198559">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load_with_pointer_offset(Fragment &amp;frag, Index pointer_offset) const </div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:203</div></div>
<div class="ttc" id="simt__policy_8h_html"><div class="ttname"><a href="simt__policy_8h.html">simt_policy.h</a></div><div class="ttdoc">Defines basic structures needed for implementing the warp-scoped phase of the epilogue. These quantities assume a &amp;#39;column-major&amp;#39; arrangement of SimtOp instructions, of which a row-oriented slice is visible per iteration. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b_html_aff99d6853af936cc23506b16b780ce09"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_3_01WarpShape___00_01Operator___00_01Elemenf2bd262ed3e202b25d5802d83965bf3b.html#aff99d6853af936cc23506b16b780ce09">cutlass::epilogue::warp::TileIteratorSimt&lt; WarpShape_, Operator_, Element_, layout::RowMajor, MmaSimtPolicy_ &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:69</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorSimt.html">cutlass::epilogue::warp::TileIteratorSimt</a></div><div class="ttdoc">Template for reading and writing tiles of accumulators to shared memory. </div><div class="ttdef"><b>Definition:</b> tile_iterator_simt.h:55</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_adeada5e33b231f125a4aaeaf963bd3a3"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">cutlass::TensorRef::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdoc">Long index used for pointer offsets. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:168</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
