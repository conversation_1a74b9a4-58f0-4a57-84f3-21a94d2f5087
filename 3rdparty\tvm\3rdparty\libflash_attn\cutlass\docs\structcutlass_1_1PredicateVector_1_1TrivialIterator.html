<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a></li><li class="navelem"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html">TrivialIterator</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1PredicateVector_1_1TrivialIterator-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html" title="An iterator implementing Predicate Iterator Concept enabling sequential read and write access to pred...">Iterator</a> that always returns true.  
</p>

<p><code>#include &lt;<a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a6cb3664b5cba4280b7055a65ddad7850"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html#a6cb3664b5cba4280b7055a65ddad7850">TrivialIterator</a> ()</td></tr>
<tr class="memdesc:a6cb3664b5cba4280b7055a65ddad7850"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a6cb3664b5cba4280b7055a65ddad7850">More...</a><br /></td></tr>
<tr class="separator:a6cb3664b5cba4280b7055a65ddad7850"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada8cd3ac6db568bb9bf268ba2c3a3e14"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html#ada8cd3ac6db568bb9bf268ba2c3a3e14">TrivialIterator</a> (<a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;it)</td></tr>
<tr class="memdesc:ada8cd3ac6db568bb9bf268ba2c3a3e14"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor.  <a href="#ada8cd3ac6db568bb9bf268ba2c3a3e14">More...</a><br /></td></tr>
<tr class="separator:ada8cd3ac6db568bb9bf268ba2c3a3e14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3adf0440f9a0143a61b43d39c3f03721"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html#a3adf0440f9a0143a61b43d39c3f03721">TrivialIterator</a> (<a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> const &amp;_vec)</td></tr>
<tr class="memdesc:a3adf0440f9a0143a61b43d39c3f03721"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs an iterator from a <a class="el" href="structcutlass_1_1PredicateVector.html" title="Statically sized array of bits implementing. ">PredicateVector</a>.  <a href="#a3adf0440f9a0143a61b43d39c3f03721">More...</a><br /></td></tr>
<tr class="separator:a3adf0440f9a0143a61b43d39c3f03721"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad24e9b451064e99fb19955f772c30e6a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html">TrivialIterator</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html#ad24e9b451064e99fb19955f772c30e6a">operator++</a> ()</td></tr>
<tr class="memdesc:ad24e9b451064e99fb19955f772c30e6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pre-increment.  <a href="#ad24e9b451064e99fb19955f772c30e6a">More...</a><br /></td></tr>
<tr class="separator:ad24e9b451064e99fb19955f772c30e6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa35b9165920b83b9a5a888df83925051"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html">TrivialIterator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html#aa35b9165920b83b9a5a888df83925051">operator++</a> (int)</td></tr>
<tr class="memdesc:aa35b9165920b83b9a5a888df83925051"><td class="mdescLeft">&#160;</td><td class="mdescRight">Post-increment.  <a href="#aa35b9165920b83b9a5a888df83925051">More...</a><br /></td></tr>
<tr class="separator:aa35b9165920b83b9a5a888df83925051"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6774c80e75fdd469bf1e42d98f8959a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html#aa6774c80e75fdd469bf1e42d98f8959a">operator*</a> () const </td></tr>
<tr class="memdesc:aa6774c80e75fdd469bf1e42d98f8959a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dereferences iterator.  <a href="#aa6774c80e75fdd469bf1e42d98f8959a">More...</a><br /></td></tr>
<tr class="separator:aa6774c80e75fdd469bf1e42d98f8959a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a6cb3664b5cba4280b7055a65ddad7850"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator::TrivialIterator </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ada8cd3ac6db568bb9bf268ba2c3a3e14"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator::TrivialIterator </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html">Iterator</a> const &amp;&#160;</td>
          <td class="paramname"><em>it</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3adf0440f9a0143a61b43d39c3f03721"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator::TrivialIterator </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1PredicateVector.html">PredicateVector</a> const &amp;&#160;</td>
          <td class="paramname"><em>_vec</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="aa6774c80e75fdd469bf1e42d98f8959a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> bool <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator::operator* </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad24e9b451064e99fb19955f772c30e6a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html">TrivialIterator</a>&amp; <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator::operator++ </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa35b9165920b83b9a5a888df83925051"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int kPredicates_, int kPredicatesPerByte_ = 4, int kPredicateStart_ = 0&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1PredicateVector_1_1TrivialIterator.html">TrivialIterator</a> <a class="el" href="structcutlass_1_1PredicateVector.html">cutlass::PredicateVector</a>&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::TrivialIterator::operator++ </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="predicate__vector_8h_source.html">predicate_vector.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
