<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::CommandLine Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="structcutlass_1_1CommandLine.html">CommandLine</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1CommandLine-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::CommandLine Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="command__line_8h_source.html">command_line.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::CommandLine:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1CommandLine__coll__graph.png" border="0" usemap="#cutlass_1_1CommandLine_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1CommandLine_coll__map" id="cutlass_1_1CommandLine_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a7156975dc884e8b58b91c710495fc79d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a7156975dc884e8b58b91c710495fc79d">CommandLine</a> (int argc, const char **argv)</td></tr>
<tr class="separator:a7156975dc884e8b58b91c710495fc79d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a20785501f9ed3d4a57241b08399552"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a5a20785501f9ed3d4a57241b08399552">check_cmd_line_flag</a> (const char *arg_name) const </td></tr>
<tr class="separator:a5a20785501f9ed3d4a57241b08399552"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bee40a3cc6078a08eec5d4ca4711f61"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a0bee40a3cc6078a08eec5d4ca4711f61"><td class="memTemplItemLeft" align="right" valign="top">int&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a0bee40a3cc6078a08eec5d4ca4711f61">num_naked_args</a> () const </td></tr>
<tr class="separator:a0bee40a3cc6078a08eec5d4ca4711f61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06962a53ee69752551c0353e1eb98d98"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a06962a53ee69752551c0353e1eb98d98"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a06962a53ee69752551c0353e1eb98d98">get_cmd_line_argument</a> (int index, value_t &amp;val) const </td></tr>
<tr class="separator:a06962a53ee69752551c0353e1eb98d98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ac897e414cfeddad031b1384ffe815e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a9ac897e414cfeddad031b1384ffe815e">get_cmd_line_argument</a> (const char *arg_name, bool &amp;val, bool _default=true) const </td></tr>
<tr class="separator:a9ac897e414cfeddad031b1384ffe815e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a206ae1ef3a4cc1a10dabd9d651be50d0"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a206ae1ef3a4cc1a10dabd9d651be50d0"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a206ae1ef3a4cc1a10dabd9d651be50d0">get_cmd_line_argument</a> (const char *arg_name, value_t &amp;val, value_t const &amp;_default=value_t()) const </td></tr>
<tr class="separator:a206ae1ef3a4cc1a10dabd9d651be50d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a604c5d891f1328b071290d5341119c2c"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a604c5d891f1328b071290d5341119c2c"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a604c5d891f1328b071290d5341119c2c">get_cmd_line_arguments</a> (const char *arg_name, std::vector&lt; value_t &gt; &amp;vals, char sep= ',') const </td></tr>
<tr class="separator:a604c5d891f1328b071290d5341119c2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38f905a17e6c6e7bd2d1bea9e0c72088"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a38f905a17e6c6e7bd2d1bea9e0c72088">get_cmd_line_argument_pairs</a> (const char *arg_name, std::vector&lt; std::pair&lt; std::string, std::string &gt; &gt; &amp;tokens, char delim= ',', char sep= ':') const </td></tr>
<tr class="separator:a38f905a17e6c6e7bd2d1bea9e0c72088"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a935f23b162d87148cadb56f9a16e094e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a935f23b162d87148cadb56f9a16e094e">get_cmd_line_argument_ranges</a> (const char *arg_name, std::vector&lt; std::vector&lt; std::string &gt; &gt; &amp;vals, char delim= ',', char sep= ':') const </td></tr>
<tr class="separator:a935f23b162d87148cadb56f9a16e094e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a228e1a273d223eec4b2f6d73135d3c1e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a228e1a273d223eec4b2f6d73135d3c1e">parsed_argc</a> () const </td></tr>
<tr class="separator:a228e1a273d223eec4b2f6d73135d3c1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a1944da52162e04b12a82ce0c1ade676e"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a1944da52162e04b12a82ce0c1ade676e">tokenize</a> (std::vector&lt; std::pair&lt; std::string, std::string &gt; &gt; &amp;tokens, std::string const &amp;str, char delim= ',', char sep= ':')</td></tr>
<tr class="memdesc:a1944da52162e04b12a82ce0c1ade676e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tokenizes a comma-delimited list of string pairs delimited by ':'.  <a href="#a1944da52162e04b12a82ce0c1ade676e">More...</a><br /></td></tr>
<tr class="separator:a1944da52162e04b12a82ce0c1ade676e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a440c25cfb006f218ff4705a43320a28b"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a440c25cfb006f218ff4705a43320a28b">tokenize</a> (std::vector&lt; std::string &gt; &amp;tokens, std::string const &amp;str, char delim= ',', char sep= ':')</td></tr>
<tr class="memdesc:a440c25cfb006f218ff4705a43320a28b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tokenizes a comma-delimited list of string pairs delimited by ':'.  <a href="#a440c25cfb006f218ff4705a43320a28b">More...</a><br /></td></tr>
<tr class="separator:a440c25cfb006f218ff4705a43320a28b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f86e4b2bd8c44b739c83530d77c5590"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a5f86e4b2bd8c44b739c83530d77c5590"><td class="memTemplItemLeft" align="right" valign="top">static void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a5f86e4b2bd8c44b739c83530d77c5590">separate_string</a> (std::string const &amp;str, std::vector&lt; value_t &gt; &amp;vals, char sep= ',')</td></tr>
<tr class="separator:a5f86e4b2bd8c44b739c83530d77c5590"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a1603f1c65c6d8d3d4262443b40e5c290"><td class="memItemLeft" align="right" valign="top">std::vector&lt; std::string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a1603f1c65c6d8d3d4262443b40e5c290">keys</a></td></tr>
<tr class="separator:a1603f1c65c6d8d3d4262443b40e5c290"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade127841e9730589f611b618e9440012"><td class="memItemLeft" align="right" valign="top">std::vector&lt; std::string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#ade127841e9730589f611b618e9440012">values</a></td></tr>
<tr class="separator:ade127841e9730589f611b618e9440012"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a338671a8d323882f9d9463863eb1c1"><td class="memItemLeft" align="right" valign="top">std::vector&lt; std::string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1CommandLine.html#a6a338671a8d323882f9d9463863eb1c1">args</a></td></tr>
<tr class="separator:a6a338671a8d323882f9d9463863eb1c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Utility for parsing command line arguments </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a7156975dc884e8b58b91c710495fc79d"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">cutlass::CommandLine::CommandLine </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>argc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char **&#160;</td>
          <td class="paramname"><em>argv</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Constructor </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a5a20785501f9ed3d4a57241b08399552"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool cutlass::CommandLine::check_cmd_line_flag </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>arg_name</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Checks whether a flag "--&lt;flag&gt;" is present in the commandline </p>

</div>
</div>
<a class="anchor" id="a06962a53ee69752551c0353e1eb98d98"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename value_t &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void cutlass::CommandLine::get_cmd_line_argument </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>index</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">value_t &amp;&#160;</td>
          <td class="paramname"><em>val</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the commandline parameter for a given index (not including flags) </p>

</div>
</div>
<a class="anchor" id="a9ac897e414cfeddad031b1384ffe815e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void cutlass::CommandLine::get_cmd_line_argument </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>arg_name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool &amp;&#160;</td>
          <td class="paramname"><em>val</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>_default</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Obtains the boolean value specified for a given commandline parameter &ndash;&lt;flag&gt;=&lt;bool&gt; </p>

</div>
</div>
<a class="anchor" id="a206ae1ef3a4cc1a10dabd9d651be50d0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename value_t &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void cutlass::CommandLine::get_cmd_line_argument </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>arg_name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">value_t &amp;&#160;</td>
          <td class="paramname"><em>val</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">value_t const &amp;&#160;</td>
          <td class="paramname"><em>_default</em> = <code>value_t()</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Obtains the value specified for a given commandline parameter &ndash;&lt;flag&gt;=</p>

</div>
</div>
<a class="anchor" id="a38f905a17e6c6e7bd2d1bea9e0c72088"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void cutlass::CommandLine::get_cmd_line_argument_pairs </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>arg_name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">std::vector&lt; std::pair&lt; std::string, std::string &gt; &gt; &amp;&#160;</td>
          <td class="paramname"><em>tokens</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>delim</em> = <code>','</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>sep</em> = <code>':'</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the values specified for a given commandline parameter &ndash;&lt;flag&gt;=</p>
<p>,&lt;value_start:value_end&gt;* </p>

</div>
</div>
<a class="anchor" id="a935f23b162d87148cadb56f9a16e094e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void cutlass::CommandLine::get_cmd_line_argument_ranges </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>arg_name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">std::vector&lt; std::vector&lt; std::string &gt; &gt; &amp;&#160;</td>
          <td class="paramname"><em>vals</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>delim</em> = <code>','</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>sep</em> = <code>':'</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a list of ranges specified for a given commandline parameter &ndash;&lt;flag&gt;=&lt;key:value&gt;,&lt;key:value&gt;* </p>

</div>
</div>
<a class="anchor" id="a604c5d891f1328b071290d5341119c2c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename value_t &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void cutlass::CommandLine::get_cmd_line_arguments </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>arg_name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">std::vector&lt; value_t &gt; &amp;&#160;</td>
          <td class="paramname"><em>vals</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>sep</em> = <code>','</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the values specified for a given commandline parameter &ndash;&lt;flag&gt;=</p>
<p>,</p>
<p>* </p>

</div>
</div>
<a class="anchor" id="a0bee40a3cc6078a08eec5d4ca4711f61"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename value_t &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int cutlass::CommandLine::num_naked_args </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns number of naked (non-flag and non-key-value) commandline parameters </p>

</div>
</div>
<a class="anchor" id="a228e1a273d223eec4b2f6d73135d3c1e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int cutlass::CommandLine::parsed_argc </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The number of pairs parsed </p>

</div>
</div>
<a class="anchor" id="a5f86e4b2bd8c44b739c83530d77c5590"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename value_t &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void cutlass::CommandLine::separate_string </td>
          <td>(</td>
          <td class="paramtype">std::string const &amp;&#160;</td>
          <td class="paramname"><em>str</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">std::vector&lt; value_t &gt; &amp;&#160;</td>
          <td class="paramname"><em>vals</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>sep</em> = <code>','</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1944da52162e04b12a82ce0c1ade676e"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void cutlass::CommandLine::tokenize </td>
          <td>(</td>
          <td class="paramtype">std::vector&lt; std::pair&lt; std::string, std::string &gt; &gt; &amp;&#160;</td>
          <td class="paramname"><em>tokens</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">std::string const &amp;&#160;</td>
          <td class="paramname"><em>str</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>delim</em> = <code>','</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>sep</em> = <code>':'</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a440c25cfb006f218ff4705a43320a28b"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void cutlass::CommandLine::tokenize </td>
          <td>(</td>
          <td class="paramtype">std::vector&lt; std::string &gt; &amp;&#160;</td>
          <td class="paramname"><em>tokens</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">std::string const &amp;&#160;</td>
          <td class="paramname"><em>str</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>delim</em> = <code>','</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>sep</em> = <code>':'</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a6a338671a8d323882f9d9463863eb1c1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::vector&lt;std::string&gt; cutlass::CommandLine::args</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1603f1c65c6d8d3d4262443b40e5c290"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::vector&lt;std::string&gt; cutlass::CommandLine::keys</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ade127841e9730589f611b618e9440012"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::vector&lt;std::string&gt; cutlass::CommandLine::values</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="command__line_8h_source.html">command_line.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
