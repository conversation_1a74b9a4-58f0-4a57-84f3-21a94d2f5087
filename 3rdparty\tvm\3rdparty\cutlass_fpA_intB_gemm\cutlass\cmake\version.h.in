#include <cstdint>
#include <string>

#define CUTLASS_MAJOR @CUTLASS_VERSION_MAJOR@
#define CUTLASS_MINOR @CUTLASS_VERSION_MINOR@
#define CUTLASS_PATCH @CUTLASS_VERSION_PATCH@
#define CUT<PERSON><PERSON>_BUILD @CUTLASS_VERSION_BUILD@
#define CUTLASS_VERSION ((CUTLASS_MAJOR)*100 + (CUTLASS_MINOR)*10 + CUTLASS_PATCH)

namespace cutlass {

  inline uint32_t getVersion() {
    return CUTLASS_VERSION;
  }
  inline uint32_t getVersionMajor() {
    return CUTLASS_MAJOR;
  }
  inline uint32_t getVersionMinor() {
    return CUTLASS_MINOR;
  }
  inline uint32_t getVersionPatch() {
    return CUTLASS_PATCH;
  }
  inline uint32_t getVersionBuild() {
    return CUTLASS_BUILD + 0;
  }
  inline std::string getVersionString() {
    std::string version = "@CUTLASS_VERSION@";
    if (getVersionBuild()) {
      version += "." + std::to_string(getVersionBuild());
    }
    return version;
  }
  inline std::string getGitRevision() {
    return "@CUTLASS_REVISION@";
  }

} // namespace cutlass
